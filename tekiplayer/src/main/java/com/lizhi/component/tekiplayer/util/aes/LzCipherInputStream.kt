/*
 * Copyright (c) 1997, 2014, Oracle and/or its affiliates. All rights reserved.
 * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.
 *
 * This code is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License version 2 only, as
 * published by the Free Software Foundation.  Oracle designates this
 * particular file as subject to the "Classpath" exception as provided
 * by Oracle in the LICENSE file that accompanied this code.
 *
 * This code is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
 * version 2 for more details (a copy is included in the LICENSE file that
 * accompanied this code).
 *
 * You should have received a copy of the GNU General Public License version
 * 2 along with this work; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.
 *
 * Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA
 * or visit www.oracle.com if you need additional information or have any
 * questions.
 */
package com.lizhi.component.tekiplayer.util.aes

import com.lizhi.component.tekiplayer.ERR_CIPHER_BADPADDING
import com.lizhi.component.tekiplayer.ERR_CIPHER_ILLEGALBLOCKSIZE
import com.lizhi.component.tekiplayer.ERR_CIPHER_ILLEGALSTATE
import com.lizhi.component.tekiplayer.ERR_CIPHER_SHORTBUFFER
import com.lizhi.component.tekiplayer.util.TekiLog
import java.io.FilterInputStream
import java.io.IOException
import java.io.InputStream
import javax.crypto.AEADBadTagException
import javax.crypto.BadPaddingException
import javax.crypto.Cipher
import javax.crypto.IllegalBlockSizeException
import javax.crypto.NullCipher
import javax.crypto.ShortBufferException


/**
 * A CipherInputStream is composed of an InputStream and a Cipher so
 * that read() methods return data that are read in from the
 * underlying InputStream but have been additionally processed by the
 * Cipher.  The Cipher must be fully initialized before being used by
 * a CipherInputStream.
 *
 *
 *  For example, if the Cipher is initialized for decryption, the
 * CipherInputStream will attempt to read in data and decrypt them,
 * before returning the decrypted data.
 *
 *
 *  This class adheres strictly to the semantics, especially the
 * failure semantics, of its ancestor classes
 * java.io.FilterInputStream and java.io.InputStream.  This class has
 * exactly those methods specified in its ancestor classes, and
 * overrides them all.  Moreover, this class catches all exceptions
 * that are not thrown by its ancestor classes.  In particular, the
 * `skip` method skips, and the `available`
 * method counts only data that have been processed by the encapsulated Cipher.
 *
 *
 *  It is crucial for a programmer using this class not to use
 * methods that are not defined or overriden in this class (such as a
 * new method or constructor that is later added to one of the super
 * classes), because the design and implementation of those methods
 * are unlikely to have considered security impact with regard to
 * CipherInputStream.
 *
 * <AUTHOR> Gong
 * @see InputStream
 *
 * @see FilterInputStream
 *
 * @see Cipher
 *
 * @see CipherOutputStream
 *
 *
 * @since 1.4
 */

class LzCipherInputStream : FilterInputStream {
    private val TAG = "LzCipherInputStream"
    // the cipher engine to use to process stream data
    private var cipher: Cipher?
    private var key: String? = null
    private var iv: String? = null

    // the underlying input stream
    private var input: InputStream

    /* the buffer holding data that have been read in from the
       underlying stream, but have not been processed by the cipher
       engine. the size 512 bytes is somewhat randomly chosen */
    private var ibuffer = ByteArray(512)

    // having reached the end of the underlying input stream
    private var done = false

    /* the buffer holding data that have been processed by the cipher
       engine, but have not been read out */
    private var obuffer: ByteArray? = null

    // the offset pointing to the next "new" byte
    private var ostart = 0

    // the offset pointing to the last "new" byte
    private var ofinish = 0

    // stream status
    private var closed = false
    
    private lateinit var saveEncryptDataCallback: (encryptData: ByteArray) -> Unit ?
    private var encryptedTotalCount = 0
    private var unUseCacheCount = 0 //未使用密文缓存大小

    /**
     * private convenience function.
     *
     * Entry condition: ostart = ofinish
     *
     * Exit condition: ostart <= ofinish
     *
     * return (ofinish-ostart) (we have this many bytes for you)
     * return 0 (no data now, but could have more later)
     * return -1 (absolutely no more data)
     *
     * Note:  Exceptions are only thrown after the stream is completely read.
     * For AEAD ciphers a read() of any length will internally cause the
     * whole stream to be read fully and verify the authentication tag before
     * returning decrypted data or exceptions.
     */
    @Throws(IOException::class)
    private fun getMoreData(): Int {
        // Android-changed: The method was creating a new object every time update(byte[], int, int)
        // or doFinal() was called resulting in the old object being GCed. With do(byte[], int) and
        // update(byte[], int, int, byte[], int), we use already initialized obuffer.
        if (done) {
            return -1
        }
        ofinish = 0
        ostart = 0
        val expectedOutputSize = cipher!!.getOutputSize(ibuffer.size)
        if (obuffer == null || expectedOutputSize > obuffer!!.size) {
            obuffer = ByteArray(expectedOutputSize)
        }
        val readin = input.read(ibuffer)
        if (readin == -1) {
            TekiLog.i(TAG, "getMoreData. already read http encrypt $encryptedTotalCount data")
            done = true
            try {
                // doFinal resets the cipher and it is the final call that is made. If there isn't
                // any more byte available, it returns 0. In case of any exception is raised,
                // obuffer will get reset and therefore, it is equivalent to no bytes returned.
                ofinish = cipher!!.doFinal(obuffer, 0)
            } catch (e: IllegalBlockSizeException) {
                obuffer = null
                throw IOException(CipherException(ERR_CIPHER_ILLEGALBLOCKSIZE, "${e.message}", e.cause))
            } catch (e: BadPaddingException) {
                obuffer = null
                throw IOException(CipherException(ERR_CIPHER_BADPADDING, "${e.message}", e.cause))
            } catch (e: ShortBufferException) {
                obuffer = null
                throw IllegalStateException(CipherException(ERR_CIPHER_SHORTBUFFER, "ShortBufferException is not expected", e.cause))
            }
        } else {
            // update returns number of bytes stored in obuffer.
            encryptedTotalCount += readin
            try {
                val encryptData: ByteArray = ibuffer.copyOfRange(0, readin)
                ofinish = cipher!!.update(ibuffer, 0, readin, obuffer, 0)
                saveEncryptDataCallback?.invoke(encryptData)
            } catch (e: IllegalStateException) {
                obuffer = null
                throw CipherException(ERR_CIPHER_ILLEGALSTATE, "${e.message}", e.cause)
            } catch (e: ShortBufferException) {
                // Should not reset the value of ofinish as the cipher is still not invalidated.
                obuffer = null
                throw IllegalStateException(CipherException(ERR_CIPHER_SHORTBUFFER, "ShortBufferException is not expected", e.cause))
            }
        }
        return ofinish
    }

    /**
     * Constructs a CipherInputStream from an InputStream and a
     * Cipher.
     * <br></br>Note: if the specified input stream or cipher is
     * null, a NullPointerException may be thrown later when
     * they are used.
     * @param is the to-be-processed input stream
     * @param c an initialized Cipher object
     * @param preData local encrypt data
     * @param ignoreLength  the length of local decrypt data
     * @param saveEncryptDataCallback callback for save encrypt data
     * @desc Data reading mechanism: local plaintext data (such as 0~512) -> local ciphertext decrypted to exclude local plaintext data (such as 513~1000) -> data decrypted after http request (such as 1001~1024) => total file Length (the total length of the plaintext is 1024 bytes)
     */
    constructor(`is`: InputStream, key:String?, iv: String?, c: Cipher?, preData: ByteArray? = null, ignoreLength: Long, saveEncryptDataCallback: (encryptData: ByteArray) -> Unit?) : super(`is`) {
        input = `is`
        cipher = c
        this.key = key
        this.iv = iv
        this.saveEncryptDataCallback = saveEncryptDataCallback
        if (preData != null && c != null) {
            val data = cipher!!.update(preData)
            val value:Int = (data.size - ignoreLength).toInt()
            if (value > 0) {
                unUseCacheCount = cipher!!.getOutputSize(value)
                if (obuffer == null || value > obuffer!!.size) {
                    obuffer = ByteArray(value)
                }
                ostart = 0
                ofinish = value
                System.arraycopy(data, ignoreLength.toInt(), obuffer, 0, value)
            }
        }
    }

    fun getUnUseCacheCount(): Int{
        return unUseCacheCount
    }

    /**
     * Constructs a CipherInputStream from an InputStream without
     * specifying a Cipher. This has the effect of constructing a
     * CipherInputStream using a NullCipher.
     * <br></br>Note: if the specified input stream is null, a
     * NullPointerException may be thrown later when it is used.
     * @param is the to-be-processed input stream
     */
    protected constructor(`is`: InputStream) : super(`is`) {
        input = `is`
        cipher = NullCipher()
    }

    /**
     * Reads the next byte of data from this input stream. The value
     * byte is returned as an `int` in the range
     * `0` to `255`. If no byte is available
     * because the end of the stream has been reached, the value
     * `-1` is returned. This method blocks until input data
     * is available, the end of the stream is detected, or an exception
     * is thrown.
     *
     *
     *
     * @return  the next byte of data, or `-1` if the end of the
     * stream is reached.
     * @exception  IOException  if an I/O error occurs.
     * @since JCE1.2
     */
    @Throws(IOException::class)
    override fun read(): Int {
        if (ostart >= ofinish) {
            // we loop for new data as the spec says we are blocking
            var i = 0
            while (i == 0) {
                i = getMoreData()
            }
            if (i == -1) {
                return -1
            }
        }
        return obuffer!![ostart++].toInt() and 0xff
    }

    /**
     * Reads up to `b.length` bytes of data from this input
     * stream into an array of bytes.
     *
     *
     * The `read` method of `InputStream` calls
     * the `read` method of three arguments with the arguments
     * `b`, `0`, and `b.length`.
     *
     * @param      b   the buffer into which the data is read.
     * @return     the total number of bytes read into the buffer, or
     * `-1` is there is no more data because the end of
     * the stream has been reached.
     * @exception  IOException  if an I/O error occurs.
     * @see InputStream.read
     * @since      JCE1.2
     */
    @Throws(IOException::class)
    override fun read(b: ByteArray): Int {
        return read(b, 0, b.size)
    }

    /**
     * Reads up to `len` bytes of data from this input stream
     * into an array of bytes. This method blocks until some input is
     * available. If the first argument is `null,` up to
     * `len` bytes are read and discarded.
     *
     * @param      b     the buffer into which the data is read.
     * @param      off   the start offset in the destination array
     * `buf`
     * @param      len   the maximum number of bytes read.
     * @return     the total number of bytes read into the buffer, or
     * `-1` if there is no more data because the end of
     * the stream has been reached.
     * @exception  IOException  if an I/O error occurs.
     * @see InputStream.read
     * @since      JCE1.2
     */
    @Throws(IOException::class)
    override fun read(b: ByteArray?, off: Int, len: Int): Int {
        if (ostart >= ofinish) {
            // we loop for new data as the spec says we are blocking
            var i = 0
            while (i == 0) {
                i = getMoreData()
            }
            if (i == -1) {
                return -1
            }
        }
        if (len <= 0) {
            return 0
        }
        var needMore = false
        var available = ofinish - ostart
        if (len < available) {
            available = len
        } else {
            needMore = true
        }
        if (b != null) {
            System.arraycopy(obuffer, ostart, b, off, available)
        }
        ostart += available
        if (needMore) {
            val readCount = read(b, off + available, len - available)
            if (readCount != -1) {
                available += readCount
            }
        }
        return available
    }

    /**
     * Skips `n` bytes of input from the bytes that can be read
     * from this input stream without blocking.
     *
     *
     * Fewer bytes than requested might be skipped.
     * The actual number of bytes skipped is equal to `n` or
     * the result of a call to
     * [available][.available],
     * whichever is smaller.
     * If `n` is less than zero, no bytes are skipped.
     *
     *
     * The actual number of bytes skipped is returned.
     *
     * @param      n the number of bytes to be skipped.
     * @return     the actual number of bytes skipped.
     * @exception  IOException  if an I/O error occurs.
     * @since JCE1.2
     */
    @Throws(IOException::class)
    override fun skip(n: Long): Long {
        var n = n
        val available = ofinish - ostart
        if (n > available) {
            n = available.toLong()
        }
        if (n < 0) {
            return 0
        }
        ostart += n.toInt()
        return n
    }

    /**
     * Returns the number of bytes that can be read from this input
     * stream without blocking. The `available` method of
     * `InputStream` returns `0`. This method
     * <B>should</B> be overridden by subclasses.
     *
     * @return     the number of bytes that can be read from this input stream
     * without blocking.
     * @exception  IOException  if an I/O error occurs.
     * @since      JCE1.2
     */
    @Throws(IOException::class)
    override fun available(): Int {
        return ofinish - ostart
    }

    /**
     * Closes this input stream and releases any system resources
     * associated with the stream.
     *
     *
     * The `close` method of `CipherInputStream`
     * calls the `close` method of its underlying input
     * stream.
     *
     * @exception  IOException  if an I/O error occurs.
     * @since JCE1.2
     */
    @Throws(IOException::class)
    override fun close() {
        if (closed) {
            return
        }
        closed = true
        input.close()

        // Android-removed: Removed a now-inaccurate comment
        if (!done) {
            try {
                cipher!!.doFinal()
            } catch (ex: BadPaddingException) {
                // Android-changed: Added throw if bad tag is seen.  See b/31590622.
                if (ex is AEADBadTagException) {
                    throw IOException(ex)
                }
            } catch (ex: IllegalBlockSizeException) {
                if (ex is AEADBadTagException) {
                    throw IOException(ex)
                }
            }
        }
        ostart = 0
        ofinish = 0
    }

    /**
     * Tests if this input stream supports the `mark`
     * and `reset` methods, which it does not.
     *
     * @return  `false`, since this class does not support the
     * `mark` and `reset` methods.
     * @see InputStream.mark
     * @see InputStream.reset
     * @since   JCE1.2
     */
    override fun markSupported(): Boolean {
        return false
    }
}
