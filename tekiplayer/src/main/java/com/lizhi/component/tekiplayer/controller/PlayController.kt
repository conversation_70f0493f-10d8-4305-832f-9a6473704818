package com.lizhi.component.tekiplayer.controller

import android.content.Context
import android.os.Build
import android.os.Looper
import android.os.Process
import com.lizhi.component.tekiplayer.ERR_PROGRAM_NOT_FOUND
import com.lizhi.component.tekiplayer.MediaItem
import com.lizhi.component.tekiplayer.Player
import com.lizhi.component.tekiplayer.PlayerEventDispatcher
import com.lizhi.component.tekiplayer.REPEAT_MODE_OFF
import com.lizhi.component.tekiplayer.audioprogram.AudioProgram
import com.lizhi.component.tekiplayer.audioprogram.AudioProgramFactory
import com.lizhi.component.tekiplayer.audioprogram.Program
import com.lizhi.component.tekiplayer.audioprogram.extractor.ExtractorsFactory
import com.lizhi.component.tekiplayer.audioprogram.listener.AudioProgramListener
import com.lizhi.component.tekiplayer.configuration.BufferSizePolicy
import com.lizhi.component.tekiplayer.controller.cdn.CdnListHolder
import com.lizhi.component.tekiplayer.controller.clock.Clock
import com.lizhi.component.tekiplayer.controller.clock.HandlerClock
import com.lizhi.component.tekiplayer.controller.focus.AudioBecomingNoisyManager
import com.lizhi.component.tekiplayer.controller.focus.AudioFocusManager
import com.lizhi.component.tekiplayer.controller.focus.PlayerAudioFocusManager
import com.lizhi.component.tekiplayer.controller.list.ListManager
import com.lizhi.component.tekiplayer.controller.list.OnPlayListUpdateListener
import com.lizhi.component.tekiplayer.controller.list.PlayerListManager
import com.lizhi.component.tekiplayer.controller.lock.WakeLockManager
import com.lizhi.component.tekiplayer.controller.lock.WifiLockManager
import com.lizhi.component.tekiplayer.controller.state.PlayerEvent
import com.lizhi.component.tekiplayer.controller.state.PlayerStateMachine
import com.lizhi.component.tekiplayer.controller.state.StateChangeListener
import com.lizhi.component.tekiplayer.datasource.BaseDataSource
import com.lizhi.component.tekiplayer.datasource.DataSource.Factory
import com.lizhi.component.tekiplayer.datasource.DataSourceStrategy
import com.lizhi.component.tekiplayer.datasource.cache.CacheMmkvStorage
import com.lizhi.component.tekiplayer.datasource.cache.LruCacheEvictor
import com.lizhi.component.tekiplayer.engine.exception.parseExceptionCodeAndMessage
import com.lizhi.component.tekiplayer.util.DefaultHandlerWrapper
import com.lizhi.component.tekiplayer.util.TekiLog
import com.lizhi.component.tekiplayer.util.Util
import java.io.File
import java.util.concurrent.atomic.AtomicInteger
import kotlin.math.abs

/**
 * 文件名：PlayController
 * 作用：播放总控制层
 * 作者：<EMAIL>
 * 创建日期：2021/2/19
 */
class PlayController(
    context: Context,
    bufferPolicy: BufferSizePolicy,
    recordPlaybackPosition: Boolean,
    internal val onPlayEventListener: PlayerEventDispatcher,
    extractorsFactory: ExtractorsFactory,
    dataSourceFactory: Factory,
    private val cachePath: File,
    cdnUrlPattern: String?,
    cacheDuration: Int
) : Controller, AudioProgramListener, OnPlayListUpdateListener {

    companion object {
        private val instanceCount : AtomicInteger = AtomicInteger(0)
    }

    private val currInstanceNum = instanceCount.incrementAndGet()

    private val TAG = "PlayController-$currInstanceNum"

    private val handler = DefaultHandlerWrapper("tekiPlayer-$currInstanceNum", Process.THREAD_PRIORITY_AUDIO)

    private val clock: Clock by lazy {
        HandlerClock(handler.looper, this).apply {
            onTimeRemainingUpdate = {
                onPlayEventListener.onTimeRemainingUpdate(
                    getCurrentItemIndex(),
                    getCurrentMediaItem(),
                    it
                )
            }
            onItemRemainingUpdate = {
                onPlayEventListener.onItemRemainingUpdate(
                    getCurrentItemIndex(),
                    getCurrentMediaItem(),
                    it
                )
            }
        }
    }

    init {
        (dataSourceFactory as BaseDataSource.BaseFactory).apply {
            // 反射通知预连接
            try {
                val buildConnectionProcessor =
                    Class.forName("com.lizhi.component.tekiplayer.okhttp.prebuild.BuildConnectionProcessor")
                val method = buildConnectionProcessor.getMethod(
                    "getOkHttpClientInstance",
                    DataSourceStrategy::class.java
                )
                method.invoke(
                    buildConnectionProcessor.getDeclaredField("INSTANCE").get(null),
                    this.strategy
                )
                TekiLog.i(TAG, "prebuild connection successfully")
            } catch (e: Exception) {
                TekiLog.e(TAG, "failed to prebuild connection", e)
            }
        }
    }

    private val stateManager = PlayerStateMachine(object : StateChangeListener {
        override fun onStateEnter(
            state: Int,
            causeEvent: Int
        ) {
            // 回调外部状态变更
            onPlayEventListener.onPlaybackStateChange(state)

            // 以下语句可能会影响状态，所以要先回调再调用
            <EMAIL>(state, causeEvent)
        }

        override fun onStateExit(
            state: Int,
            causeEvent: Int
        ) {
            <EMAIL>(state, causeEvent)
        }
    })

    override var pauseWhenAudioFocusLost = true

    override var autoHandleAudioFocus: Boolean
        get() = audioFocusManager.enabled
        set(value) {
            audioFocusManager.enabled = value
        }

    private val audioBecomingNoisyManager by lazy {
        AudioBecomingNoisyManager(context) {
            TekiLog.i(TAG, "pause on receiving ACTION_AUDIO_BECOMING_NOISY（耳机拔出）")
            pause()
        }
    }

    private val onAudioFocusChangeListener = object : AudioFocusManager.AudioFocusChangeListener {
        private var needToResumePlaying = false
        private var originalVolume: Float = -1f

        override fun onAudioFocusGained() {
            if (needToResumePlaying) {
                TekiLog.i(TAG, "onAudioFocusGained")
                resume()
                needToResumePlaying = false
            } else if (originalVolume != -1f) {
                volume = originalVolume
            }
        }

        override fun onAudioFocusLost(
            isTransient: Boolean,
            mayDuck: Boolean
        ) {
            TekiLog.i(
                TAG,
                "onAudioFocusLost isTransient [$isTransient], mayDuck [$mayDuck], pauseWhenAudioFocusLost [$pauseWhenAudioFocusLost]"
            )

            when {
                isTransient && mayDuck -> {
                    originalVolume = volume
                    val downVolume =
                        if (originalVolume < 0.3f) originalVolume else originalVolume - 0.25f
                    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
                        volume = downVolume
                    }
                    needToResumePlaying = false
                }
                isTransient && !mayDuck -> {
                    if (!pauseWhenAudioFocusLost) {
                        return
                    }
                    playWhenReady = false
                    TekiLog.i(TAG, "send EVENT_FOCUS_LOST_PAUSED on [onAudioFocusLost]")
                    stateManager.sendEvent(PlayerEvent.EVENT_FOCUS_LOST_PAUSED)
                    needToResumePlaying = true
                }
                else -> {
                    if (!pauseWhenAudioFocusLost) {
                        return
                    }
                    pause()
                    needToResumePlaying = false
                }
            }
        }
    }

    override val duration: Long
        get() = Util.usToMs(currentProgram?.durationUs)

    override val position: Long
        get() = Util.usToMs(currentProgram?.getPositionUs())

    override val bufferedPosition: Long
        get() = Util.usToMs(currentProgram?.getBufferedPositionUs())

    override var speed: Float = 1F
        set(value) {
            field = value
            currentProgram?.setPlayRate(value)
            programFactory.speed = value
            playListManager.updatePreBufferingProgramSpeed(speed)
        }

    override var volume: Float = 1F
        set(value) {
            field = value
            currentProgram?.volume = value
            programFactory.volume = value
            playListManager.updatePreBufferingProgramVolume(speed)
        }

    override var repeatMode: Int = REPEAT_MODE_OFF
        set(value) {
            field = value
            actionInQueue {
                playListManager.repeatMode = value
            }
        }

    private val playListManager: ListManager

    private val currentProgram: Program?
        get() = playListManager.getCurrentProgram()?.program

    private val audioFocusManager: AudioFocusManager =
        PlayerAudioFocusManager(context, onAudioFocusChangeListener)

    private val wifiLockManager = WifiLockManager(context)
    private val wakeLockManager = WakeLockManager(context)

    private var playWhenReady = false

    private var changeQualityIndex = -1

    private val programFactory = AudioProgramFactory(
        volume,
        speed,
        bufferPolicy,
        this,
        extractorsFactory,
        dataSourceFactory,
        cdnUrlPattern,
        cacheDuration
    )

    init {
        playListManager = PlayerListManager(
            programFactory,
            bufferPolicy.autoPrepareMediaCount(),
            recordPlaybackPosition
        )
        playListManager.setOnPlayListUpdateListener(this)

        wifiLockManager.setEnabled(true)
        wakeLockManager.setEnabled(true)
    }

    private inline fun actionInQueue(crossinline block: () -> Unit) {
        handler.post {
            runCatching(block).onFailure {
                TekiLog.e(TAG, "catch on actionInQueue: ${it.message}", it)
                currentProgram?.reportError(it, false)
            }
        }
    }

    override fun addMediaItem(item: MediaItem) = actionInQueue {
        playListManager.addMediaItem(item)
    }

    override fun addMediaItem(
        position: Int,
        item: MediaItem
    ) = actionInQueue {
        if (position < 0) {
            TekiLog.w(TAG, "addMediaItem position < 0, return")
            return@actionInQueue
        }
        playListManager.addMediaItem(position, item)
    }

    override fun addMediaItem(list: List<MediaItem>) = actionInQueue {
        playListManager.addMediaItem(list)
    }

    override fun addMediaItem(
        position: Int,
        list: List<MediaItem>
    ) = actionInQueue {
        if (position < 0) {
            TekiLog.w(TAG, "addMediaItem position < 0, return")
            return@actionInQueue
        }
        playListManager.addMediaItem(position, list)
    }

    override fun removeRange(
        startPosition: Int,
        length: Int
    ) = actionInQueue {
        if (startPosition < 0 || length < 0) {
            TekiLog.w(TAG, "addMediaItem position < 0, return")
            return@actionInQueue
        }
        playListManager.removeRange(startPosition, length)
    }

    override fun removeItem(item: MediaItem) = actionInQueue {
        playListManager.removeItem(item)
    }

    override fun removeItem(position: Int) = actionInQueue {
        if (position >= 0) {
            playListManager.removeItem(position)
        }
    }

    override fun clear() = actionInQueue {
        playListManager.clear()
    }

    override fun getCurrentMediaItem(): MediaItem? {
        return playListManager.getCurrentProgram()?.mediaItem
    }

    override fun getCurrentItemIndex(): Int {
        return playListManager.getCurrentProgram()?.position ?: -1
    }

    override fun getRemainingTimeBeforeStop(): Long {
        return clock.getRemainingTimeBeforeStop()
    }

    override fun getRemainingItemBeforeStop(): Int {
        return clock.getRemainingItemBeforeStop()
    }

    override fun getLooper(): Looper {
        return handler.looper
    }

    override fun getMediaItemAt(position: Int): MediaItem? {
        // 校验 position
        if (position < 0 || position >= playListManager.getMediaItemList().size) {
            return null
        }
        return playListManager.getMediaItem(position)
    }

    override fun getAllMediaItems(): List<MediaItem> {
        return playListManager.getMediaItemList()
    }

    override fun hasNextItem(): Boolean {
        return playListManager.hasNext()
    }

    override fun hasPreviousItem(): Boolean {
        return playListManager.hasPrevious()
    }

    override fun prepare() = actionInQueue {
        TekiLog.i(TAG, "send EVENT_PREPARE on [prepare]")
        stateManager.sendEvent(PlayerEvent.EVENT_PREPARE)
    }

    override fun play() = actionInQueue {
        playWhenReady = true
        TekiLog.i(TAG, "send EVENT_PLAY on [play] for list")
        stateManager.sendEvent(PlayerEvent.EVENT_PLAY)
    }

    override fun play(position: Int) = actionInQueue {
        TekiLog.i(TAG, "play $position")
        // stop
        stateManager.sendEvent(PlayerEvent.EVENT_STOP)
        playWhenReady = true
        TekiLog.i(TAG, "send EVENT_PLAY on [play] for $position")
        playListManager.moveToProgramOnPosition(position)
        stateManager.sendEvent(PlayerEvent.EVENT_PLAY)
    }

    override fun playNext() = actionInQueue {
        TekiLog.i(TAG, "playNext")
        // stop
        stateManager.sendEvent(PlayerEvent.EVENT_STOP)
        if (playListManager.hasNext()) {
            //
            playWhenReady = true
            TekiLog.i(TAG, "send EVENT_PLAY on [playNext]")
            playListManager.moveToNextProgram(true)
            stateManager.sendEvent(PlayerEvent.EVENT_PLAY)
        } else {
            val errMsg = "has no next program on [playNext]"
            TekiLog.w(TAG, errMsg)
            stateManager.sendEvent(PlayerEvent.EVENT_AUDIO_ERROR)
            // 对外状态回调
            onPlayEventListener.onError(ERR_PROGRAM_NOT_FOUND, errMsg)
        }
    }

    override fun playPrevious() = actionInQueue {
        TekiLog.i(TAG, "playPrevious")
        // stop
        stateManager.sendEvent(PlayerEvent.EVENT_STOP)
        if (playListManager.hasPrevious()) {
            //
            playWhenReady = true
            TekiLog.i(TAG, "send EVENT_PLAY on [playPrevious]")
            playListManager.moveToPreviousProgram()
            stateManager.sendEvent(PlayerEvent.EVENT_PLAY)
        } else {
            val errMsg = "has no previous program on [playPrevious]"
            TekiLog.w(TAG, errMsg)
            stateManager.sendEvent(PlayerEvent.EVENT_AUDIO_ERROR)
            // 对外状态回调
            onPlayEventListener.onError(ERR_PROGRAM_NOT_FOUND, errMsg)
        }
    }

    override fun stop() = actionInQueue {
        playWhenReady = false
        TekiLog.i(TAG, "send EVENT_STOP on [stop]")
        stateManager.sendEvent(PlayerEvent.EVENT_STOP)
    }

    override fun pause() = actionInQueue {
        playWhenReady = false
        TekiLog.i(TAG, "send EVENT_PAUSE on [pause]")
        stateManager.sendEvent(PlayerEvent.EVENT_PAUSE)
    }

    override fun resume() = actionInQueue {
        playWhenReady = true
        TekiLog.i(TAG, "send EVENT_RESUME on [resume]")
        stateManager.sendEvent(PlayerEvent.EVENT_RESUME)
    }

    override fun seekBy(relativeMs: Long) = actionInQueue {
        val program = currentProgram ?: return@actionInQueue

        var back = false
        if (relativeMs < 0) {
            back = true
        }

        var positionUs = if (!back) {
            program.getPositionUs() + Util.msToUs(abs(relativeMs))
        } else {
            program.getPositionUs() - Util.msToUs(abs(relativeMs))
        }

        if (positionUs < 0) {
            positionUs = 0
        }
        program.seek(positionUs)
    }

    override fun seekTo(absoluteMs: Long) = actionInQueue {
        currentProgram?.seek(Util.msToUs(absoluteMs))
    }

    override fun seekTo(index: Int, absoluteMs: Long) = actionInQueue {
        // stop
        stateManager.sendEvent(PlayerEvent.EVENT_STOP)
        //
        val programHolder = playListManager.moveToProgramOnPosition(index)
        if (programHolder != null) {
            programHolder.program.seek(absoluteMs * 1000)
        }
        TekiLog.i(TAG, "send EVENT_PLAY on [seekTo] by long")
        stateManager.sendEvent(PlayerEvent.EVENT_PLAY)
    }

    override fun seekTo(index: Int, percent: Float) = actionInQueue {
        // stop
        stateManager.sendEvent(PlayerEvent.EVENT_STOP)
        //
        val programHolder = playListManager.moveToProgramOnPosition(index)
        if (programHolder != null) {
            programHolder.program.seek(percent)
        }
        TekiLog.i(TAG, "send EVENT_PLAY on [seekTo] by float")
        stateManager.sendEvent(PlayerEvent.EVENT_PLAY)
    }

    override fun getStatus(): Int {
        return stateManager.state
    }

    override fun stopAfterTime(timeMs: Long) {
        clock.stopAfterTime(timeMs)
    }

    override fun stopAtTime(absoluteTimestamp: Long) {
        clock.stopAtTime(absoluteTimestamp)
    }

    override fun stopAfterMediaItemFinish(itemCount: Int) {
        clock.stopAfterItem(itemCount)
    }

    override fun cancelStop() {
        clock.cancelStop()
    }


    override fun clearCache() {
        try {
            CacheMmkvStorage.getInstance(CacheMmkvStorage.TEKI_PLAYER_MMKV_ID).clearAll()
            cachePath.listFiles()?.forEach {
                it.deleteRecursively()
            }
        } catch (e: Exception) {
            TekiLog.e(TAG, "error on clearCache()", e)
        }
        clearAllHighPriorityCache()
    }

    override fun clearAllHighPriorityCache() {
        try {
            CacheMmkvStorage.getInstance(CacheMmkvStorage.TEKI_PLAYER_HIGH_MMKV_ID).clearAll()
            // 高优跟普通同一目录
            cachePath.parent?.let { parent -> 
                File(parent, LruCacheEvictor.CACHE_PRIORITY_PATH).also { newPath ->
                    if (newPath.exists()) {
                        newPath.listFiles()?.forEach {
                            it.deleteRecursively()
                        }
                    }
                }
            }
        } catch (e:Exception) {
            e.printStackTrace()
        }
    }
    
    override fun setCdnList(cdnList: List<String>) {
        CdnListHolder.updateCdnList(cdnList)
    }

    override fun setQuality(quality: Player.Quality) = actionInQueue {
        TekiLog.i(TAG, "setQuality $quality")
        // 更改播放列表的音质
        playListManager.quality = quality
        val currentProgram = playListManager.getCurrentProgram() ?: return@actionInQueue
        // 拿到当前播放的index和当前进度
        val currIndex = currentProgram.position
        val currTimeMs = Util.usToMs(currentProgram.program.getPositionUs())
        changeQualityIndex = currIndex
        // 停止，然后重新播放
        currentProgram.program.stop()
        playListManager.clearPlayingAndPreBufferingProgram()
        playListManager.moveToProgramOnPosition(currIndex, currTimeMs)
        stateManager.sendEvent(PlayerEvent.EVENT_PLAY)
    }

    override fun getQuality(): Player.Quality {
        return playListManager.quality
    }

    override fun getActualQuality(): Player.Quality {
        return playListManager.getCurrentProgram()?.quality ?: playListManager.quality
    }

    private fun onStateExit(
        @PlayerState.State state: Int,
        @PlayerEvent.Event causeEvent: Int
    ) {
        if (state == PlayerState.STATE_BUFFERING && causeEvent == PlayerEvent.EVENT_STOP) {
            currentProgram?.reportForceStop()
        }
    }

    private fun onStateEnter(
        @PlayerState.State state: Int,
        @PlayerEvent.Event causeEvent: Int
    ) {
        when (state) {
            PlayerState.STATE_BUFFERING -> {
                val programHolder = playListManager.getCurrentProgram()

                if (causeEvent == PlayerEvent.EVENT_NEED_MORE_DATA) {
                    currentProgram?.pause()
                }

                // 当前节目结束、失败时，进行位置切换或者重建处理
                currentProgram.also {
                    TekiLog.i(
                        TAG,
                        "play() cur program=${it?.uuid} [is null] ${it == null} [isEnd] ${it?.isEnd()} [isOnError] ${it?.isOnError()}"
                    )
                    if (it == null || it.isEnd()) {
                        if (playListManager.hasNext()) {
                            // 如果结束了，切换至下一首歌
                            playListManager.moveToNextProgram(force = repeatMode == REPEAT_MODE_OFF)
                        } else {
                            playListManager.clearPlayingAndPreBufferingProgram()
                            stateManager.sendEvent(PlayerEvent.EVENT_AUDIO_ERROR)
                            // 对外状态回调
                            onPlayEventListener.onError(ERR_PROGRAM_NOT_FOUND, "has no program can play")
                        }
                    } else if (programHolder != null && it.isOnError()) {
                        // 如果当前节目失败异常，重建当前位置的该节目
                        TekiLog.i(TAG, "play() cur program=${it.uuid} is on error")
                        playListManager.moveToProgramOnPosition(
                            programHolder.position
                            //         Util.usToMs(it.getPositionUs())
                        )
                    }
                }

                currentProgram?.reportBuffering()
            }
            PlayerState.STATE_READY -> {
                TekiLog.i(TAG, "state ready [playWhenReady] = $playWhenReady")
                if (playWhenReady) {
                    // currentProgram?.play()
                    stateManager.sendEvent(PlayerEvent.EVENT_PLAY)
                }
                currentProgram?.reportPlaying()
            }
            PlayerState.STATE_PAUSED -> {
                // 如果是底层回调error导致的切换至暂停状态，这里不需要手动调用audioProgram的暂停
                if (causeEvent == PlayerEvent.EVENT_PAUSE || causeEvent == PlayerEvent.EVENT_FOCUS_LOST_PAUSED) {
                    currentProgram?.pause()
                }
                if (causeEvent != PlayerEvent.EVENT_FOCUS_LOST_PAUSED) {
                    audioFocusManager.abandon()
                }
                audioBecomingNoisyManager.setEnabled(false)
            }
            PlayerState.STATE_PLAYING -> {
                currentProgram?.play()
                audioFocusManager.request()
                audioBecomingNoisyManager.setEnabled(true)
                wifiLockManager.setStayAwake(true)
                wakeLockManager.setStayAwake(true)
            }
            PlayerState.STATE_ENDED -> {
                // 完播打点
                if (causeEvent == PlayerEvent.EVENT_STOP || causeEvent == PlayerEvent.EVENT_ENDED) {
                    currentProgram?.reportFinish()
                }
                //
                if (causeEvent == PlayerEvent.EVENT_STOP || causeEvent == PlayerEvent.EVENT_AUDIO_ERROR) {
                    // 用户输入的stop操作
                    currentProgram?.stop()
                }
                currentProgram?.reportTransactionEnd()
                audioFocusManager.abandon()
                audioBecomingNoisyManager.setEnabled(false)
                wifiLockManager.setStayAwake(false)
                wakeLockManager.setStayAwake(false)
//                onPlayEventListener.onError(0)
            }
        }

    }

    override fun onPlaybackStarted(program: AudioProgram) {
//        TekiLog.i(TAG, "send EVENT_BUFFERED_ENOUGH on [onPlaybackStarted]")
//        stateManager.sendEvent(PlayerEvent.EVENT_BUFFERED_ENOUGH)
//        TekiLog.i(TAG, "send EVENT_PLAY on [onPlaybackStarted]")
//        stateManager.sendEvent(PlayerEvent.EVENT_PLAY)
    }

    override fun onPlaybackStopped(program: AudioProgram) {
        TekiLog.i(TAG, "send EVENT_STOP on [onPlaybackStopped]")
        if (program == currentProgram) {
            stateManager.sendEvent(PlayerEvent.EVENT_STOP)
        }
    }

    override fun onPlaybackBuffering(
        program: AudioProgram,
        reason: Int
    ) {
//        if (reason == AudioProgramListener.REASON_BUFFERING_NOT_ENOUGH && program == currentProgram) {
//            stateManager.sendEvent(PlayerEvent.EVENT_NEED_MORE_DATA)
//        }
    }

    override fun onBufferEnough(program: AudioProgram) {
        if (program == currentProgram) {
            TekiLog.i(TAG, "send EVENT_BUFFERED_ENOUGH on [onBufferEnough]")
            stateManager.sendEvent(PlayerEvent.EVENT_BUFFERED_ENOUGH)
            playListManager.resumePreload()
        }
    }

    override fun onNeedMoreData(
        program: AudioProgram,
        seeking: Boolean
    ) {
        if (program == currentProgram) {
            TekiLog.i(TAG, "send EVENT_NEED_MORE_DATA on [onNeedMoreData]")
            stateManager.sendEvent(PlayerEvent.EVENT_NEED_MORE_DATA)
            playListManager.pausePreload()
        }
    }

    override fun onPlaybackResumed(audioProgram: AudioProgram) {
//        TekiLog.i(TAG, "send EVENT_PLAY on [onPlaybackResumed]")
//        stateManager.sendEvent(PlayerEvent.EVENT_PLAY)
//        TekiLog.i(TAG, "send EVENT_RESUME on [onPlaybackResumed]")
//        stateManager.sendEvent(PlayerEvent.EVENT_RESUME)
    }

    override fun onPlaybackPaused(program: AudioProgram) {
//        TekiLog.i(TAG, "send EVENT_PAUSE on [onPlaybackPaused]")
//        stateManager.sendEvent(PlayerEvent.EVENT_PAUSE)
    }

    override fun onPlaybackSeeking(
        program: AudioProgram,
        currBufferedTimeUs: Long,
        currBufferedPosition: Long,
        destSeekTimeUs: Long,
        destSeekPosition: Long
    ) {
        TekiLog.i(
            TAG,
            "$program onPlaybackSeeking, currBufferedTimeUs=$currBufferedTimeUs, currBufferedPosition=$currBufferedPosition, destSeekTimeUs=$destSeekTimeUs"
        )
    }

    override fun onPlaybackSeekSuccess(
        program: AudioProgram,
        destSeekTimeUs: Long,
        destSeekPosition: Long
    ) {
        TekiLog.i(TAG, "$program onPlaybackSeekSuccess, destSeekTimeUs=$destSeekTimeUs")
    }

    override fun onPlaybackCompletion(program: AudioProgram) {
        TekiLog.i(TAG, "$program onPlaybackCompletion")
        TekiLog.i(TAG, "send EVENT_ENDED on [onPlaybackCompletion]")
        stateManager.sendEvent(PlayerEvent.EVENT_ENDED)
        // 恢复播放进度到0
        playListManager.getCurrentProgram()?.mediaItem?.pendingStartPositionMs = 0L
        // 自然结束
        // 如果已经被时钟暂停了，则不继续播放
        clock.addFinishedItem()
        if (clock.stoped) {
            TekiLog.i(TAG, "clock stop happened, can not play next")
            // 触发后取消当前定时
            clock.cancelStop()
            currentProgram?.pause()
            return
        }

        val next = playListManager.moveToNextProgram(false)
        if (next == null) {
            playWhenReady = false
            onPlayEventListener.onPlayListFinished()
            return
        }
        //
        stateManager.sendEvent(PlayerEvent.EVENT_PREPARE)

        // 播完一首，可以prepare下一首
        if (playListManager.shouldPrepareNextProgram()) {
            playListManager.prepareNextProgram()
        }
    }

    override fun onPlaybackFailed(
        program: AudioProgram,
        error: Throwable,
        recoverable: Boolean
    ) {
        val programHolder = playListManager.getCurrentProgram()
        if (programHolder?.program == program) {
            program.reportError(error, recoverable)
            if (!recoverable) {
                TekiLog.i(TAG, "send EVENT_AUDIO_ERROR on [onPlaybackFailed]")
                programHolder.mediaItem.pendingStartPositionMs = 0
//                    Util.usToMs(programHolder.program.getPositionUs())
                stateManager.sendEvent(PlayerEvent.EVENT_AUDIO_ERROR)
                // 对外状态回调
                val (code, msg) = parseExceptionCodeAndMessage(error)
                onPlayEventListener.onError(code, msg)
            }
        }
    }

    override fun onBufferedComplete(program: AudioProgram) {
    }

    override fun onBufferSizeStateChange(
        program: AudioProgram,
        dataLevel: Int,
        cacheSize: Long,
        firstFullCacheCallback: Boolean
    ) {
        if (firstFullCacheCallback && playListManager.shouldPrepareNextProgram()) {
            playListManager.prepareNextProgram()
            TekiLog.i(TAG, "prepare next program=$program")
        }
    }

    override fun onRetryingStarted(program: AudioProgram) {
    }

    override fun onRetryingSuccess(program: AudioProgram) {
    }

    override fun onRetryingFailed(program: AudioProgram) {
    }

    override fun onStreamErrorOccurred(
        errorCode: Int,
        errorDesc: String
    ) {
    }

    override fun onStreamMetaDataAvailable(metaData: String) {
    }

    override fun onStreamSamplesAvailable(program: AudioProgram) {
    }

    override fun onPlaybackFirstFrameRender(audioProgram: AudioProgram) {

    }

    override fun onBandwidthQualityChanged(program: AudioProgram, qualityLevel: Int, speed: Int) {
        if (program == currentProgram) {
            onPlayEventListener.onWatch(qualityLevel, speed)
        }
    }

    override fun onPlayZero(program: AudioProgram) {
        val mediaItem = playListManager.getCurrentProgram()?.mediaItem
        onPlayEventListener.onPlayZeroItem( mediaItem)
    }

    // 以下是 PlayListManager 的回调

    override fun onPlayPositionChanged(
        position: Int,
        lastPositionUs: Long,
        reason: Int
    ) {
        onPlayEventListener.onPlaybackChange(
            position, playListManager.getMediaItem(position), Util.usToMs(lastPositionUs), reason
        )
    }

    override fun onPlayZero(position: Int) {
        onPlayEventListener.onPlayZeroItem(
            playListManager.getMediaItem(position)
        )
    }

    override fun onPlayListChanged(reason: Int) {
        onPlayEventListener.onPlayListUpdate()
    }
    override fun onPlayingItemRemoved(reason: Int) {
        TekiLog.i(TAG, "send EVENT_STOP on [onPlayingItemRemoved]")
        stateManager.sendEvent(PlayerEvent.EVENT_STOP)
        onPlayEventListener.onPlaybackRemoveOnList()
    }

    override fun onPlayingItemPreloadFailed() {
        TekiLog.i(TAG, "send EVENT_AUDIO_ERROR on [onPlayingItemPreloadFailed]")
        stateManager.sendEvent(PlayerEvent.EVENT_AUDIO_ERROR)
    }

    override fun onItemReady(item: AudioProgramHolder) {
        TekiLog.i(
            TAG,
            "onItemReady item ${item.position}  current ${playListManager.getCurrentProgram()?.position}"
        )
        if (item == playListManager.getCurrentProgram()) {
            if (playWhenReady) {
                TekiLog.i(TAG, "send EVENT_PLAY on [onItemReady]")
                stateManager.sendEvent(PlayerEvent.EVENT_PLAY)
            }
            TekiLog.i(TAG, "send EVENT_BUFFERED_ENOUGH on [onItemReady]")
            stateManager.sendEvent(PlayerEvent.EVENT_BUFFERED_ENOUGH)
            // 更改音质的回调
            if (changeQualityIndex != -1 && changeQualityIndex == item.position) {
                onPlayEventListener.onAudioQualityChange(playListManager.quality)
            }
        }
        // 任意一个program已经ready都更改
        // 如果第一个ready的index是更改音质的index则进行回调，否则说明更改音质时加载失败了
        changeQualityIndex = -1
    }


}