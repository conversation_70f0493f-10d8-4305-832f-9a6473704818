package com.lizhi.component.tekiplayer.datasource

import com.lizhi.component.tekiplayer.datasource.impl.DefaultHttpDataSource
import com.lizhi.component.tekiplayer.util.TekiLog

/**
 * 文件名：HttpDataSourceProvider
 * 作用：获取HttpDataSource工厂类，会通过反射尝试获取OkhttpHttpDataSource的工厂类
 * 作者：huangtianhao
 * 创建日期：2021/4/28
 */
class HttpDataSourceFactoryProvider {
    private var buildFactory: (() -> BaseDataSource.BaseFactory)? = null

    companion object {
        private const val TAG = "HttpDataSourceProvider"
    }

    fun get(): BaseDataSource.BaseFactory {
        // 当前已经有实例
        buildFactory?.let {
            return it()
        }

        // 尝试反射获取OkHttpDataSource的Factory
        try {
            val clazz =
                Class.forName("com.lizhi.component.tekiplayer.okhttp.OkHttpDataSource\$OkHttpDataSourceFactory")
            val baseFactory = clazz.newInstance() as? BaseDataSource.BaseFactory
            if (baseFactory != null) {
                this.buildFactory = {
                    clazz.newInstance() as BaseDataSource.BaseFactory
                }
                TekiLog.i(TAG, "using OkHttpDataSourceFactory")
                return baseFactory
            }
        } catch (e: Exception) {
            TekiLog.d(
                TAG,
                "failed to reflect OkHttpDataSourceFactory, maybe there is no okhttp module depend ${e.stackTraceToString()}"
            )
        }


        // 反射没找到对应的类，用回DefaultHttpDataSource
        val defaultFactory = DefaultHttpDataSource.DefaultHttpDataSourceFactory()
        this.buildFactory = {
            DefaultHttpDataSource.DefaultHttpDataSourceFactory()
        }
        TekiLog.i(TAG, "using DefaultHttpDataSourceFactory")
        return defaultFactory
    }
}