package com.lizhi.component.tekiplayer.util

import android.os.Message

/**
 * 文件名：HandlerWrapper
 * 作用：
 * 作者：luo<PERSON><PERSON><PERSON>@lizhi.fm
 * 创建日期：2021/4/12
 */
interface HandlerWrapper {

    fun post(runnable: () -> Unit)

    fun quitSafely()

    fun sendMessage(message: Message)
    fun removeMessage(what: Int)
    fun removeMessages()
    fun sendMessageAtFront(message: Message)
    fun sendMessageDelay(message: Message, delay: Long)
}