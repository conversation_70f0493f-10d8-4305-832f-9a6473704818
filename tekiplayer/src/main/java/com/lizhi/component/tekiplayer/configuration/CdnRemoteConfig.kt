package com.lizhi.component.tekiplayer.configuration

import com.google.gson.Gson
import com.lizhi.component.cloudconfig.CloudConfig
import com.lizhi.component.tekiplayer.util.TekiLog

class TekiPlayerRemoteConfig(val cdn: Cdn?) {

    companion object {
        private val TAG = "TekiPlayerRemoteConfig"
        internal var instance: TekiPlayerRemoteConfig? = null
        private val gson = Gson()

        init {
            CloudConfig.register("tekiplayer") {cdnConfJson ->
                if (cdnConfJson != null) {
                    val cdnConf = gson.fromJson(cdnConfJson, TekiPlayerRemoteConfig::class.java)
                    instance = cdnConf
                    TekiLog.i(TAG, "remote cdnConf: $cdnConf")
                } else {
                    instance = null
                }
            }
        }
    }

    data class Cdn(val cdnUrlPattern: String = "", val cdnList: List<String> = emptyList())

    override fun toString(): String {
        return "TekiPlayerRemoteConfig(cdn=$cdn)"
    }
}