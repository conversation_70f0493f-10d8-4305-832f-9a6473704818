package com.lizhi.component.tekiplayer.datasource.cache

import com.lizhi.component.tekiplayer.datasource.DataReader
import com.lizhi.component.tekiplayer.datasource.DataWriter
import com.lizhi.component.tekiplayer.datasource.Range

/**
 * 文件名：Cache
 * 作用：缓存控制器接口定义
 * 作者：huangtianhao
 * 创建日期：2021/3/18
 */
interface Cache : DataWriter, DataReader {

    var isOpened: Boolean

    /**
     * 初始化Cache，应只由CacheDataSource进行调用
     *
     * @param url 网络请求地址
     * @param range 缓存seek的位置，暂时只用到start，end不起作用
     */
    fun open(url: String, range: Range? = null)

    /**
     * 释放资源
     */
    fun close()

    /**
     * 获取缓存信息中的ContentLength
     */
    fun getContentLength(): Long?

    /**
     * 设置ContentLength到缓存信息中
     */
    fun setContentLength(contentLength: Long)

    /**
     * 获取大于当前位置的第一个Range
     * end >= pos
     *
     * @param position 指定位置
     */
    fun getNextRangeOfPos(position: Long): Range?
    
    /**
     * 是否缓冲完成
     */
    fun isFullyCached(): Boolean

    /**
     * 删除缓存
     */
    fun deleteCache(): Boolean

    /**
     * 设置加密信息
     */
    fun setAesInfo(aesKey: String?, aesIV: String?, isRealTimeDecrypt:Boolean = false)

    /**
     * 是否是加密缓存
     */
    fun hasAesInfo(): Boolean

    fun hasOfflineEncrypt(): Boolean

    /**
     * 获取本地密文缓存
     */
    fun obtainEncryptCache(): ByteArray?

    /**
     * 保存密文（用于断点下载）
     */
    fun saveEncryptData(ba: ByteArray)

    /**
     * 删除密文（断点下载完毕删除）
     */
    fun deleteEncryptData()

    /**
     * 获取明文缓存长度
     */
    fun getPlainCacheLength(): Long

    /**
     * 获取明文总长度
     */
    fun getPlainCacheTotalLength(): Long

    /**
     * 释放缓存（当前节目使用完）
     */
    fun releaseCache()
}