package com.lizhi.component.tekiplayer.audioprogram.loader

import android.os.Handler
import android.os.Looper
import android.os.Message
import com.lizhi.component.tekiplayer.util.DefaultHandlerWrapper
import java.io.IOException
import java.lang.Exception

abstract class HandlerLoader(name: String, priority: Int) {

    companion object {
        const val TAG = "AudioProgram:HandlerLoader"

        const val MSG_OPEN = 0
        const val MSG_READ = 1
        const val MSG_SEEK = 2
        const val MSG_STOP = 3
    }

    var loadPosition: Long = 0
    private var currentError: IOException? = null
    private var errorCount = 0
    private var fatalError: IOException? = null

    private var canceled: Boolean = false
    private var stop: Boolean = false

    val looper = Looper.myLooper() ?: throw UnsupportedOperationException("当前线程没有lopper")

    private val errorHandler by lazy { Handler(looper) }

    private var loading: Boolean = false

    private val handler = DefaultHandlerWrapper(name, priority) {
        try {
            val next = load(it)

            if (next == null || canceled || stop) {
                loading = false
                onLoadCanceled()
                if (stop) {
                    onLoadReleased()
                }
            } else {
                obtainMessageAndSend(next, 0)
            }
        } catch (e: IOException) {
            val lastPosition = it.obj as Long?
            loading = false
            errorHandler.post {
                handleError(e, lastPosition)
            }
        } catch (e: Exception) {
            val lastPosition = it.obj as Long?
            loading = false
            errorHandler.post {
                handleError(IOException(e), lastPosition)
            }
        } finally {

        }
    }

    private fun onLoadReleased() {
        handler.quitSafely()
    }

    abstract fun onLoadCanceled()

    private fun handleError(currentError: IOException, lastPosition: Long?) {
        errorCount++
//        TekiLog.e(TAG, "error when on message=${message.what}", currentError.cause ?: currentError)
        val action = onLoadError(currentError, errorCount)
        if (action.type == Loader.ACTION_TYPE_DONT_RETRY_FATAL) {
            fatalError = currentError
            handler.removeMessage(MSG_OPEN)
            handler.removeMessage(MSG_SEEK)
        } else if (action.type != Loader.ACTION_TYPE_DONT_RETRY) {
            if (action.type == Loader.ACTION_TYPE_RETRY_AND_RESET_ERROR_COUNT) {
                errorCount = 1
            }
            if (fatalError != null) {
                return
            }
            if (!stop && !canceled) {
                startLoading(lastPosition ?: loadPosition, action.retryDelayMillis)
            }
        }
    }

    abstract fun load(message: Message): Message?

    abstract fun onLoadError(currentError: IOException, errorCount: Int): Loader.LoadErrorAction

    val isLoading: Boolean
        get() = loading
    val isCanceled: Boolean
        get() = canceled

    // ************************ 下面的方法都是向 handler 发出指令，运行在调用者线程 ********************

    open fun startLoading(position: Long, delay: Long = 0) {
        loading = true
        canceled = false
        loadPosition = position
        handler.removeMessage(MSG_OPEN)
        handler.removeMessage(MSG_READ)
        val obtain = Message.obtain()
        obtain.obj = position
        obtain.what = MSG_OPEN
        obtainMessageAndSend(obtain, delay)
    }

    fun cancelLoading() {
        canceled = true
        loading = false
        handler.removeMessages()
    }

    protected fun removeMessage(what: Int) {
        handler.removeMessage(what)
    }

    private fun obtainMessageAndSend(old: Message, delay: Long) {
        loadPosition = old.obj as Long
        if (delay > 0) {
            handler.sendMessageDelay(Message.obtain(old), delay)
        } else {
            handler.sendMessage(Message.obtain(old))
        }
    }

    fun seek(position: Long) {
        loading = true
        canceled = false
        loadPosition = position
        handler.removeMessage(MSG_OPEN)
        handler.removeMessage(MSG_READ)

        val message = Message.obtain()
        message.what = MSG_SEEK
        message.obj = position
        handler.sendMessage(message)
    }

    fun release() {
        handler.removeMessages()
        loading = false
        canceled = true
        stop = true

        val message = Message.obtain()
        message.what = MSG_STOP
        handler.sendMessageAtFront(message)
    }

}