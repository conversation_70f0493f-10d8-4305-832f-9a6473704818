package com.lizhi.component.tekiplayer.engine

/**
 * 虚拟时间轴，根据播放自然时间跟进 buffer 消耗，追踪进度
 */
interface Timeline {

    /**
     * 获取进度
     */
    fun getPositionUs(): Long

    /**
     * 写入 buffer
     */
    fun addBuffer(size: Long, timeUs: Long, isPlaying: Boolean)

    /**
     * 暂停计时
     */
    fun pause()

    /**
     * 跳进度
     */
    fun seek(positionUs: Long): Boolean

    /**
     * 设置播放速度
     */
    fun setSpeed(speed: Float)

    /**
     * 设置 buffer 结束监控
     */
    fun setBufferEndFlowListener(listener: (zeroData:Boolean)-> Unit)

    /**
     * 停止并销毁 timeline
     */
    fun stop()

    /**
     * 等待播放完成，会回调 bufferEnd
     */
    fun playToEnd(zeroData: Boolean)

}