package com.lizhi.component.tekiplayer.datasource.exception

import com.lizhi.component.tekiplayer.ERR_DATASOURCE_NOT_OPEN

/**
 * 文件名：DataSourceNotOpenException
 * 作用：数据源未Open异常
 * 作者：huangtianhao
 * 创建日期：2021/3/25
 */
class DataSourceNotOpenException(
    override val cause: Throwable? = null,
    msg: String? = null
) : DataSourceException(ERR_DATASOURCE_NOT_OPEN, msg ?: "DataSourceNotOpenException", cause)