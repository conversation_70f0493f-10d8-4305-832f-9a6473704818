package com.lizhi.component.tekiplayer

import androidx.annotation.FloatRange
import androidx.annotation.IntDef
import com.lizhi.component.tekiplayer.controller.PlayerState.State
import kotlin.annotation.AnnotationTarget.*

/**
 * 文件名：Player
 * 作用：播放器对外接口定义
 * 作者：<PERSON><PERSON><PERSON><PERSON><PERSON>@lizhi.fm
 * 创建日期：2021/2/19
 */

const val REPEAT_MODE_OFF = 0
const val REPEAT_MODE_ONE = 1
const val REPEAT_MODE_ALL = 2
const val REPEAT_MODE_SHUFFLE = 3

@Suppress("unused")
interface Player {

    /**
     * Repeat modes for playback. One of [REPEAT_MODE_OFF], [REPEAT_MODE_ONE] or [REPEAT_MODE_SHUFFLE] [REPEAT_MODE_ALL].
     */
    @MustBeDocumented
    @kotlin.annotation.Retention(AnnotationRetention.SOURCE)
    @Target(VALUE_PARAMETER, FIELD, PROPERTY, FUNCTION)
    @IntDef(value = [REPEAT_MODE_OFF, REPEAT_MODE_ONE, REPEAT_MODE_ALL, REPEAT_MODE_SHUFFLE])
    annotation class RepeatMode

    @MustBeDocumented
    @kotlin.annotation.Retention(AnnotationRetention.SOURCE)
    @Target(VALUE_PARAMETER, FIELD, PROPERTY, FUNCTION)
    @FloatRange(from = 0.5, to = 2.0)
    annotation class SpeedRange

    @MustBeDocumented
    @kotlin.annotation.Retention(AnnotationRetention.SOURCE)
    @Target(VALUE_PARAMETER, FIELD, PROPERTY, FUNCTION)
    @FloatRange(from = 0.0, to = 1.0)
    annotation class VolumeRange

    /**
     * 列表循环模式。0:顺序播放；1：单曲循环；2：列表循环；3：随机播放
     */
    @RepeatMode
    var repeatMode: Int

    /**
     * 播放速度，范围0.5-2
     */
    @SpeedRange
    var speed: Float

    /**
     * 音量
     */
    @VolumeRange
    var volume: Float

    /**
     * 是否内部自动处理音频焦点
     * 默认为 true
     */
    var autoHandleAudioFocus: Boolean

    /**
     * 焦点丢失时是否暂停播放
     * 默认为 true
     */
    var pauseWhenAudioFocusLost: Boolean

    /**
     * 添加媒体节目到播放列表末尾
     * @param mediaItem 节目
     */
    fun addMediaItem(mediaItem: MediaItem)

    /**
     * 插入节目
     * @param mediaItem 节目
     * @param index 插入位置
     */
    fun addMediaItem(
        index: Int,
        mediaItem: MediaItem
    )

    /**
     * 添加节目列表
     * @param list
     */
    fun addMediaItem(list: List<MediaItem>)

    /**
     * 在指定位置插入节目列表
     * @param index 位置
     * @param list 节目列表
     */
    fun addMediaItem(index: Int, list: List<MediaItem>)

    /**
     * 移除列表中指定范围的节目
     * @param startIndex
     * @param length
     */
    fun removeRange(startIndex: Int, length: Int)

    /**
     * 移除指定节目
     * @param item
     */
    fun removeItem(item: MediaItem)

    /**
     * 播放列表移除媒体节目
     * @param index 移除位置
     */
    fun removeItemAt(index: Int)

    /**
     * 获取列表中的某个item
     * @param index 第几个
     * @return 如果小于0或者超出列表长度，返回null
     */
    fun getMediaItem(index: Int): MediaItem?

    /**
     * 获取当前播放列表
     */
    fun getMediaItemList(): List<MediaItem>

    /**
     * 清除播放列表，如果正在播放会造成停止
     */
    fun clear()

    /**
     * 预加载
     */
    fun prepare()

    /**
     * 开始播放列表
     */
    fun play()

    /**
     * 开始播放列表，从[index]开始
     * @param index 列表位置
     */
    fun play(index: Int)

    /**
     * 切换播放下一个节目
     */
    fun playNext()

    /**
     * 切换播放上一个节目
     */
    fun playPrevious()

    /**
     * 暂停播放
     */
    fun pause()

    /**
     * 从暂停中恢复
     */
    fun resume()

    /**
     * 停止播放器
     */
    fun stop()

    /**
     * 跳转到对应位置，单位：ms
     * @param positionMs 播放位置
     */
    fun seekTo(positionMs: Long)

    /**
     * 跳转到 [index] 首歌曲并从 [positionMs] 位置开始播放
     */
    fun seekTo(index: Int, positionMs: Long)

    /**
     * 跳转到 [index] 首歌曲并从 [percent] % 位置开始播放
     */
    fun seekTo(index: Int, percent: Float)

    /**
     * 基于当前位置往前或者往后跳转 [relativeMs] ms
     * @param relativeMs 要快进/快退的时长，单位：ms
     */
    fun seekBy(relativeMs: Long)

    /**
     * 获取当前的播放进度，单位：ms
     * @return 当前进度
     */
    fun getPosition(): Long

    /**
     * 获取已缓冲的数据位置，单位：ms
     * @return 缓冲进度
     */
    fun getBufferedPosition(): Long

    /**
     * 获取当前播放器状态
     * @return 播放器状态
     */
    @State
    fun getStatus(): Int

    /**
     * 获取当前节目的总时长，单位：ms
     * @return 节目时长，单位：ms
     */
    fun getDuration(): Long

    /**
     * 获取当前的item的index
     */
    fun getCurrentIndexOnList(): Int

    /**
     * 获取当前的mediaitem
     */
    fun getCurrentMediaItem(): MediaItem?

    /**
     * 是否有下一首
     * @return 是否有下一首
     */
    fun hasNext(): Boolean

    /**
     * 是否有上一首
     * @return true：是
     */
    fun hasPrevious(): Boolean

    /**
     * [timeMs]后暂停播放，单位：ms
     */
    fun stopAfterTime(timeMs: Long)

    /**
     * 在指定时间暂停播放
     *
     * @param absoluteTimestamp 时间戳，单位：ms
     */
    fun stopAtTime(absoluteTimestamp: Long)

    /**
     * [itemCount]个节目后自动暂停
     */
    fun stopAfterMediaItemFinish(itemCount: Int = 1)

    /**
     * 取消暂停任务
     */
    fun cancelStop()

    /**
     * 添加播放事件监听
     * @param listener
     */
    fun addPlayEventListener(listener: PlayEventListener)

    /**
     * 移除播放事件监听
     * @param listener
     */
    fun removePlayEventListener(listener: PlayEventListener)

    /**
     * 添加网速监听
     */
    fun addNetworkQualityWatcher(watcher: (qualityLevel: Int, speedBps: Int) -> Unit)

    /**
     * 移除网速监听
     */
    fun removeNetworkQualityWatcher(watcher: (qualityLevel: Int, speedBps: Int) -> Unit)

    /**
     * 清除所有缓存
     */
    fun clearCache()

    /**
     * 清除高优缓存
     */
    fun clearAllHighPriorityCache()

    /**
     * 设置 CDN 列表
     */
    fun setCdnList(cdnList: List<String>)

    /**
     * 设置音质
     */
    fun setAudioQuality(quality: Quality)

    /**
     * 获取设置的音质
     */
    fun getAudioQuality(): Quality

    /**
     * 获取目前播放的真实音质，如果还没播放，则是设置的音质
     */
    fun getActualAudioQuality(): Quality

    /**
     * 获取定时停止前剩余的播放时间，如果没有设置定时，则为0
     */
    fun getTimeRemainingBeforeStop(): Long

    /**
     * 获取定时停止前剩余的节目数，如果没有设置定时，则为0
     */
    fun getItemRemainingBeforeStop(): Int

    enum class Quality(val value: Int) {
        LOW(0), HIGH(1), SUPER(2), LOSSLESS(3)
    }
}

