package com.lizhi.component.tekiplayer

import android.content.Context
import android.os.Handler
import com.lizhi.component.tekiplayer.audioprogram.extractor.DefaultExtractorsFactory
import com.lizhi.component.tekiplayer.audioprogram.extractor.ExtractorsFactory
import com.lizhi.component.tekiplayer.configuration.BufferSizePolicy
import com.lizhi.component.tekiplayer.configuration.NetType
import com.lizhi.component.tekiplayer.configuration.TekiPlayerRemoteConfig
import com.lizhi.component.tekiplayer.controller.CacheController
import com.lizhi.component.tekiplayer.controller.PlayController
import com.lizhi.component.tekiplayer.controller.PlayerState
import com.lizhi.component.tekiplayer.controller.dns.DnsResolver
import com.lizhi.component.tekiplayer.datasource.HttpDataSourceFactoryProvider
import com.lizhi.component.tekiplayer.datasource.entity.DeleteCacheType
import com.lizhi.component.tekiplayer.datasource.entity.MediaInfo
import com.lizhi.component.tekiplayer.datasource.entity.MediaInfoResult
import com.lizhi.component.tekiplayer.datasource.impl.DefaultDataSource.DefaultDataSourceFactory
import com.lizhi.component.tekiplayer.process.PlayerService
import com.lizhi.component.tekiplayer.process.ProcessPlayerBuilder
import com.lizhi.component.tekiplayer.util.KB
import com.lizhi.component.tekiplayer.util.MB
import com.lizhi.component.tekiplayer.util.TekiLog
import com.lizhi.component.tekiplayer.util.countDown
import com.lizhi.component.tekiplayer.util.stopCountDown
import java.io.File
import java.util.concurrent.atomic.AtomicInteger

/**
 * 文件名：TekiPlayer
 * 作用：
 * 作者：<EMAIL>
 * 创建日期：2021/2/19
 */
open class TekiPlayer internal constructor(
    context: Context,
    cachePath: File,
    bufferPolicy: BufferSizePolicy,
    private val recordPlaybackPosition: Boolean,
    extractorsFactory: ExtractorsFactory,
    dnsResolver: DnsResolver?,
    cdnUrlPattern: String?,
    private val cacheDuration: Int
) : Player {

    companion object {
        private val instanceCount : AtomicInteger = AtomicInteger(0)
        suspend fun deleteMediaInfos(context: Context, infos: List<MediaInfo>): List<MediaInfoResult> {
            return CacheController.deleteMediaInfos(context.applicationContext, infos)
        }

        suspend fun deleteCaches(context: Context, type: DeleteCacheType) {
            CacheController.deleteCaches(context.applicationContext, type)
        }

        suspend fun queryMediaInfosCacheSize(context: Context, infos: List<MediaInfo>) : Long {
            return CacheController.queryMediaInfosCacheSize(context.applicationContext, infos)
        }

        suspend fun queryAllCacheSize(context: Context) : Long {
            return CacheController.queryAllCacheSize(context.applicationContext)
        }

        fun enableBuiltinPolicy(isEnable: Boolean) {
            CacheController.enableBuiltinPolicy(isEnable)
        }
    }

    private val currInstanceNum = instanceCount.incrementAndGet()

    val TAG = "TekiPlayer-$currInstanceNum"

    protected val handler by lazy {
        Handler(playController.getLooper())
    }

    protected val playListEventListener by lazy {
        PlayerEventDispatcher()
    }

    private val playController by lazy {
        PlayController(
            context,
            bufferPolicy,
            recordPlaybackPosition,
            playListEventListener,
            extractorsFactory,
            DefaultDataSourceFactory(cachePath, bufferPolicy).apply {
                setContext(context)
                setHttpDataSourceFactory(HttpDataSourceFactoryProvider().get())
                buildStrategy {
                    if (dnsResolver != null) {
                        this.setDnsResolver(dnsResolver)
                    }
                }
            },
            cachePath,
            cdnUrlPattern,
            cacheDuration
        )
    }

    init {
        playListEventListener.addPlayListEventListener(object : PlayEventListener {
            var index = -1
            var position = -1L
            var item: MediaItem? = null

            override fun onPlaybackStateChange(status: Int) {
                if (/*status == PlayerState.STATE_BUFFERING || */status == PlayerState.STATE_PLAYING) {
                    index = playController.getCurrentItemIndex()
                    item = playController.getCurrentMediaItem()
                    handler.stopCountDown()

                    TekiLog.d(TAG, "start CountDown")
                    handler.countDown {
                        position = playController.position
                        playListEventListener.onPlayedPositionUpdate(
                            index,
                            item,
                            playController.position
                        )
                        playListEventListener.onBufferedPositionUpdate(
                            index,
                            item,
                            playController.bufferedPosition
                        )
                        // 下一次回调时间
                        1000L
                    }
                } else if (status in arrayOf(PlayerState.STATE_ENDED, PlayerState.STATE_PAUSED)) {
                    TekiLog.d(TAG, "stop CountDown")
                    playListEventListener.onPlayedPositionUpdate(
                        index,
                        item,
                        playController.position
                    )
                    handler.stopCountDown()
                }
            }

            override fun onError(errCode: Int, message: String) {
            }

            override fun onPlaybackRemoveOnList() {
            }

            override fun onPlayListUpdate() {
            }

            override fun onPlayListFinished() {
            }

            override fun onPlaybackChange(
                index: Int,
                item: MediaItem?,
                lastPosition: Long,
                reason: Int
            ) {
            }

            override fun onPlayedPositionUpdate(index: Int, item: MediaItem?, position: Long) {
            }

            override fun onPlayZeroItem( item: MediaItem?) {

            }
            override fun onBufferedPositionUpdate(
                index: Int,
                item: MediaItem?,
                bufferPosition: Long
            ) {
            }
        })
    }

    override fun seekTo(index: Int, percent: Float) {
        TekiLog.d(TAG, "seekTo $index $percent")
        playController.seekTo(index, percent)
    }

    override fun seekTo(index: Int, positionMs: Long) {
        TekiLog.d(TAG, "seekTo $index $positionMs")

        playController.seekTo(index, positionMs)
    }

    override var repeatMode: Int = 0
        set(value) {
            field = value
            playController.repeatMode = field
        }

    override var speed: Float = 1F
        set(value) {
            if (value > 2.0 || value < 0.5) {
                return
            }
            field = value
            playController.speed = speed
        }

    override var volume: Float = 1F
        set(value) {
            if (value > 1.0 || value < 0) {
                return
            }
            field = value
            playController.volume = field
        }

    override var autoHandleAudioFocus: Boolean = true
        set(value) {
            field = value
            playController.autoHandleAudioFocus = value
        }

    override var pauseWhenAudioFocusLost: Boolean = true
        set(value) {
            field = value
            playController.pauseWhenAudioFocusLost = field
        }

    override fun addMediaItem(mediaItem: MediaItem) {
        TekiLog.d(TAG, "addMediaItem $mediaItem")
        playController.addMediaItem(mediaItem)
    }

    override fun addMediaItem(
        index: Int,
        mediaItem: MediaItem
    ) {
        TekiLog.d(TAG, "addMediaItem ${index} $mediaItem")
        playController.addMediaItem(index, mediaItem)
    }

    override fun addMediaItem(list: List<MediaItem>) {
        TekiLog.d(TAG, "addMediaItem ${list.size}")
        playController.addMediaItem(list)
    }

    override fun addMediaItem(
        index: Int,
        list: List<MediaItem>
    ) {
        TekiLog.d(TAG, "addMediaItem $index ${list.size}")
        playController.addMediaItem(index, list)
    }

    override fun removeRange(
        startIndex: Int,
        length: Int
    ) {
        TekiLog.d(TAG, "removeRange $startIndex")
        playController.removeRange(startIndex, length)
    }

    override fun removeItem(item: MediaItem) {
        TekiLog.d(TAG, "removeItem $item")
        playController.removeItem(item)
    }

    override fun removeItemAt(index: Int) {
        TekiLog.d(TAG, "removeItemAt $index")
        playController.removeItem(index)
    }

    override fun getMediaItem(index: Int): MediaItem? {
        return playController.getMediaItemAt(index)
    }

    override fun getMediaItemList(): List<MediaItem> {
        return playController.getAllMediaItems()
    }

    override fun clear() {
        playController.clear()
    }

    override fun prepare() {
        playController.prepare()
    }

    override fun play() {
        playController.play()
    }

    override fun play(index: Int) {
        playController.play(index)
    }

    override fun playNext() {
        playController.playNext()
    }

    override fun playPrevious() {
        playController.playPrevious()
    }

    override fun pause() {
        playController.pause()
    }

    override fun resume() {
        playController.resume()
    }

    override fun stop() {
        playController.stop()
    }

    override fun seekTo(positionMs: Long) {
        playController.seekTo(positionMs)
    }

    override fun seekBy(relativeMs: Long) {
        playController.seekBy(relativeMs)
    }

    override fun getPosition(): Long {
        return playController.position
    }

    override fun getBufferedPosition(): Long {
        return playController.bufferedPosition
    }

    override fun getStatus(): Int {
        return playController.getStatus()
    }

    override fun getDuration(): Long {
        return playController.duration
    }

    override fun getCurrentIndexOnList(): Int {
        return playController.getCurrentItemIndex()
    }

    override fun getCurrentMediaItem(): MediaItem? {
        return playController.getCurrentMediaItem()
    }

    override fun hasNext(): Boolean {
        return playController.hasNextItem()
    }

    override fun hasPrevious(): Boolean {
        return playController.hasPreviousItem()
    }

    override fun stopAtTime(absoluteTimestamp: Long) {
        playController.stopAtTime(absoluteTimestamp)
    }

    override fun stopAfterTime(timeMs: Long) {
        playController.stopAfterTime(timeMs)
    }

    override fun stopAfterMediaItemFinish(itemCount: Int) {
        playController.stopAfterMediaItemFinish(itemCount)
    }

    override fun cancelStop() {
        playController.cancelStop()
    }

    override fun addPlayEventListener(listener: PlayEventListener) {
        playListEventListener.addPlayListEventListener(listener)
    }

    override fun removePlayEventListener(listener: PlayEventListener) {
        playListEventListener.removePlayListEventListener(listener)
    }

    override fun clearCache() {
        playController.clearCache()
    }

    override fun clearAllHighPriorityCache() {
        playController.clearAllHighPriorityCache()
    }

    override fun setCdnList(cdnList: List<String>) {
        if (TekiPlayerRemoteConfig.instance?.cdn != null) {
            TekiLog.w(TAG, "setCdnList is not allowed because CdnRemoteConfig is not empty")
            return
        }
        playController.setCdnList(cdnList)
    }

    override fun setAudioQuality(quality: Player.Quality) {
        playController.setQuality(quality)
    }

    override fun getAudioQuality(): Player.Quality {
        return playController.getQuality()
    }

    override fun getActualAudioQuality(): Player.Quality {
        return playController.getActualQuality()
    }

    override fun getTimeRemainingBeforeStop(): Long {
        return playController.getRemainingTimeBeforeStop()
    }

    override fun getItemRemainingBeforeStop(): Int {
        return playController.getRemainingItemBeforeStop()
    }

    override fun addNetworkQualityWatcher(watcher: (qualityLevel: Int, speedBps: Int) -> Unit) {
        playListEventListener.addNetworkWatcher(watcher)
    }

    override fun removeNetworkQualityWatcher(watcher: (qualityLevel: Int, speedBps: Int) -> Unit) {
        playListEventListener.removeNetworkWatcher(watcher)
    }

    open class Builder(internal val context: Context) {

        // 缓存路径，目前不允许对外修改
        internal val cachePath: File =
            context.externalCacheDir?.let { File(it, "tekiPlayer").apply { mkdirs() } }
                ?: context.getExternalFilesDir("tekiPlayer").apply { this?.mkdirs() }
                ?: context.cacheDir.let { File(it, "tekiPlayer").apply { mkdirs() } }
        internal var extractorsFactory: ExtractorsFactory = DefaultExtractorsFactory()

        // 是否记录播放位置
        internal var recordPosition: Boolean = false

        // 预缓冲大小，暂不允许修改
        internal var preBufferSize = 200 * 1024L

        // 卡顿时恢复播放要至少可以播放多长时间，单位：秒
        internal var cacheDuration = 10

        // 自动预缓冲节目数量
        internal var autoPrepareMediaCount = 3

        // WIFI下当前节目的最大缓冲值
        internal var maxBufferSizeOnWifi = 10.MB

        // 5G下当前节目的最大缓冲值
        internal var maxBufferSizeOn5G = 10.MB

        // 4G下当前节目的最大缓冲值
        internal var maxBufferSizeOn4G = 8.MB

        // 3G下当前节目的最大缓冲值
        internal var maxBufferSizeOn3G = 5.MB

        // 2G下当前节目的最大缓冲值
        internal var maxBufferSizeOn2G = 3.MB

        // 当前节目缓冲少于此值，就必须开始继续缓冲
        internal var shouldLoadingThresholds = -1L

        // DNS 解析实现方式，可不实现
        internal var dnsResolver: DnsResolver? = null

        // 高优缓存最大可用空间
        internal var limitMaxDiskHighPrioritySpaceUsage = 150.MB

        // 默认缓存最大可用空间
        internal var limitMaxDiskSpaceUsage = 300.MB

        // 系统剩下可用空间
        internal var limitFreeDiskSpaceUsage = 300.MB

        // CDN 替换正则匹配（需原地址符合该规则才进行域名替换）
        internal var cdnUrlPattern: String? =
            "^\\w+://\\w*cdn\\w*\\.(lizhi\\.fm|gzlzfm\\.com)([/\\?#&].+)?$"

        internal var bufferPolicy: BufferSizePolicy = object : BufferSizePolicy {
            override fun playingProgramBufferSize(net: NetType): Long {
                return when (net) {
                    NetType.TYPE_WIFI -> maxBufferSizeOnWifi
                    NetType.TYPE_5G -> maxBufferSizeOn5G
                    NetType.TYPE_4G -> maxBufferSizeOn4G
                    NetType.TYPE_3G -> maxBufferSizeOn3G
                    NetType.TYPE_2G -> maxBufferSizeOn2G
                }
            }

            override fun preloadBufferSize(): Long {
                return preBufferSize
            }

            override fun autoPrepareMediaCount(): Int {
                return autoPrepareMediaCount
            }

            override fun shouldLoadingThresholds(net: NetType): Long {
                return if (shouldLoadingThresholds <= -1) {
                    (playingProgramBufferSize(net) * 0.8).toLong()
                } else {
                    shouldLoadingThresholds
                }
            }

            override fun limitMaxDiskHighPrioritySpaceUsage(): Long {
                return limitMaxDiskHighPrioritySpaceUsage
            }

            override fun limitMaxDiskSpaceUsage(): Long {
                return limitMaxDiskSpaceUsage
            }

            override fun limitFreeDiskSpaceUsage(): Long {
                return limitFreeDiskSpaceUsage
            }
        }

        fun setAutoPrepareMediaCount(count: Int): Builder {
            if (count >= 0) {
                this.autoPrepareMediaCount = count
            }
            return this
        }

        fun setLimitMaxDiskHighPrioritySpaceUsage(count: Long): Builder {
            if (count >= 1.KB) {
                limitMaxDiskHighPrioritySpaceUsage = count
            }
            return this
        }

        fun setLimitMaxDiskSpaceUsage(count: Long): Builder {
            if (count >= 1.KB) {
                limitMaxDiskSpaceUsage = count
            }
            return this
        }

        fun setLimitFreeDiskSpaceUsage(count: Long): Builder {
            if (count >= 100.MB) {
                limitFreeDiskSpaceUsage = count
            }
            return this
        }

        fun setShouldLoadingThresholds(size: Long): Builder {
            if (size > 100.KB) {
                shouldLoadingThresholds = size
            }
            return this
        }

        fun setCacheDuration(duration: Int): Builder {
            if (duration > 0) {
                cacheDuration = duration
            }
            return this
        }

        fun setMaxBufferSizeOn5G(size: Long): Builder {
            if (size > 100.KB) {
                maxBufferSizeOn5G = size
            }
            return this
        }

        fun setMaxBufferSizeOnWifi(size: Long): Builder {
            if (size > 100.KB) {
                maxBufferSizeOnWifi = size
            }
            return this
        }

        fun setMaxBufferSizeOn4G(size: Long): Builder {
            if (size > 100.KB) {
                maxBufferSizeOn4G = size
            }
            return this
        }

        fun setMaxBufferSizeOn3G(size: Long): Builder {
            if (size > 100.KB) {
                maxBufferSizeOn3G = size
            }
            return this
        }

        fun setMaxBufferSizeOn2G(size: Long): Builder {
            if (size > 100.KB) {
                maxBufferSizeOn2G = size
            }
            return this
        }

        fun recordPlaybackPosition(recordOrNot: Boolean): Builder {
            this.recordPosition = recordOrNot
            return this
        }

        fun setDnsResolver(dnsResolver: DnsResolver): Builder {
            this.dnsResolver = dnsResolver
            return this
        }

        fun setCdnUrlPattern(urlPattern: String): Builder {
            this.cdnUrlPattern = urlPattern
            return this
        }

        fun bindService(clazz: Class<out PlayerService>): ProcessPlayerBuilder {
            return ProcessPlayerBuilder(this, clazz)
        }

        fun build(): TekiPlayer {
            return TekiPlayer(
                context.applicationContext ?: context,
                cachePath,
                bufferPolicy,
                recordPosition,
                extractorsFactory,
                dnsResolver,
                TekiPlayerRemoteConfig.instance?.cdn?.cdnUrlPattern ?: cdnUrlPattern,
                cacheDuration
            ).apply {
                playController.setCdnList(TekiPlayerRemoteConfig.instance?.cdn?.cdnList ?: emptyList())
            }
        }
    }

}