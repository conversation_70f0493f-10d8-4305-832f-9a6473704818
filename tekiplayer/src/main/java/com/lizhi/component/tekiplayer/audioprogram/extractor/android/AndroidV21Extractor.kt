//package com.lizhi.component.tekiplayer.audioprogram.extractor.android
//
//import android.media.MediaExtractor
//import android.media.MediaFormat
//import com.lizhi.component.tekiplayer.audioprogram.extractor.Extractor
//import com.lizhi.component.tekiplayer.audioprogram.extractor.ExtractorInput
//import com.lizhi.component.tekiplayer.datasource.DataSource
//import com.lizhi.component.tekiplayer.engine.DataQueue
//import com.lizhi.component.tekiplayer.util.TekiLog
//import java.io.File
//import java.io.RandomAccessFile
//import java.nio.ByteBuffer
//import kotlin.math.abs
//import kotlin.math.max
//
///**
// * 文件名：V21Extractor
// * 作用：
// * 作者：<EMAIL>
// * 创建日期：2021/3/24
// */
//class AndroidV21Extractor(
//    private val dataQueue: DataQueue,
//    private val reInitOnV21: (Extractor, Long) -> Unit
//) : Extractor {
//
//    companion object {
//        const val MAX_FRAME_SIZE = 4096
//        const val TAG = "AndroidV21Extractor"
//    }
//
//    private var dataSource: DataSource? = null
//    private var filePath: String? = null
//    private var mediaExtractor = MediaExtractor()
//    private var frameSize = 0
//    private var sampleTime = 0L
//    private var dataSourceToEnd = false
//    private var initMediaSource = false
//    override var mediaFormat: MediaFormat? = null
//    override val sampleTimeUs: Long
//        get() = sampleTime
//    private var completeInit = false
//    private var currSourceSize = 0L
//
//    override fun init(
//        filePath: DataQueue?,
//        dataSource: DataSource
//    ) {
//        this.filePath = filePath
//        this.dataSource = dataSource
//    }
//
//    override fun sample(dataReader: ExtractorInput?): Int {
//        val dataSourceBuffer = ByteArray(
//            MAX_FRAME_SIZE
//        )
//        var readLength: Int = -1
//        if (!dataSourceToEnd) {
//            readLength = dataReader.read(dataSourceBuffer, 0, dataSourceBuffer.size)
//            if (readLength == -1) {
//                dataSourceToEnd = true
//            }
//        }
//
//        if (!initMediaSource) {
//            filePath?.let {
//                format(it, dataReader.contentLength)
//            }
//            return 0
//        }
//
//        return if (completeInit && dataSourceToEnd) -1 else if (readLength == -1) 0 else readLength
//    }
//
//    override fun seek(positionUs: Long): Int {
//        mediaExtractor.seekTo(positionUs, MediaExtractor.SEEK_TO_PREVIOUS_SYNC)
//        var canSeek: Boolean = mediaExtractor.advance()
//        canSeek = canSeek && abs(mediaExtractor.sampleTime - positionUs) < 50000
//
//        if (!canSeek && !completeInit) {
//            mediaExtractor.release()
//            mediaExtractor = MediaExtractor()
//            initMediaSource = false
//            init(filePath!!, dataSource!!)
//            mediaExtractor.seekTo(positionUs, MediaExtractor.SEEK_TO_PREVIOUS_SYNC)
//            canSeek = mediaExtractor.advance()
//            canSeek = canSeek && abs(mediaExtractor.sampleTime - positionUs) < 10000
//        }
//        return if (canSeek) 0 else if (completeInit) -1 else -2
//    }
//
//    override fun readSample(buffer: ByteBuffer): Int {
//        val readSampleData = mediaExtractor.readSampleData(buffer, 0)
//
//        sampleTime = max(mediaExtractor.sampleTime, sampleTime)
//        frameSize = max(frameSize, readSampleData)
//
//        if (!completeInit && !dataSourceToEnd && readSampleData <= 0) {
//            TekiLog.i("readSampleData", "recreate MediaExtractor, sampleTime=${sampleTime}")
//            mediaExtractor.release()
//            mediaExtractor = MediaExtractor()
//            initMediaSource = false
//            reInitOnV21(this@AndroidV21Extractor, sampleTime)
//        } else {
//            mediaExtractor.advance()
//        }
//        return if (completeInit && dataSourceToEnd && readSampleData == -1) -1 else if (readSampleData == -1) 0 else readSampleData
//    }
//
//    override val durationUs: Long?
//        get() = mediaFormat?.getLong(MediaFormat.KEY_DURATION)
//
//    override fun getTimeUs(position: Long): Long {
//        val mediaFormat = mediaFormat ?: return 0
//        val bitRate = mediaFormat.getLong(MediaFormat.KEY_BIT_RATE)
//        return (position * 8.0F / 1000 / bitRate).toLong()
//    }
//
//    private fun format(
//        filePath: String,
//        sourceSize: Long?
//    ) {
//        if (!initMediaSource) {
//            try {
//                val file = File(filePath)
//                currSourceSize = file.length()
//                completeInit = currSourceSize == sourceSize
//                mediaExtractor.setDataSource(RandomAccessFile(file, "r").fd)
//                mediaFormat = mediaExtractor.getTrackFormat(0)
//                mediaExtractor.selectTrack(0)
//                initMediaSource = true
//            } catch (ignore: Exception) {
//                ignore.printStackTrace()
//            }
//        }
//    }
//
//    override fun timeToPosition(positionUs: Long): Long {
//        return 0
//    }
//}