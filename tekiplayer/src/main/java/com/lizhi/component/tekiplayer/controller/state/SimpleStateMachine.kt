package com.lizhi.component.tekiplayer.controller.state


/**
 * 文件名：SimpleStateMachine
 * 作用：简单的状态机封装，支持DSL形式调用，线程不安全
 * 作者：huangtianhao
 * 创建日期：2021/4/2
 */

class SimpleStateMachine<State : Any, Event : Any, SideEffect : Any>(
    initState: State,
    private val stateDefinitions: LinkedHashMap<State, InnerState<State, Event, SideEffect>>,
    private val onStateChangeListener: StateChangeListener<State, Event, SideEffect>
) {

    /**
     * 当前状态
     */
    var curState: State = initState
        private set

    /**
     * 接收事件
     * 统一回调[onStateExit], [onStateEnter], [onSideEffectOccur]事件
     */
    fun sendEvent(event: Event) {
        val state = stateDefinitions[curState]
        val transition = state?.transitions?.get(event)
        if (transition != null && transition.toState != state) {
            // 找到转换关系，切换至新状态
            val oldState = curState
            this.curState = transition.toState
            onStateChangeListener.onStateExit(oldState, event)
            onStateChangeListener.onStateEnter(transition.toState, event)
            transition.sideEffect?.let {
                onStateChangeListener.onSideEffectOccur(it)
            }
        } else {
            // 找不到转换关系，维持当前状态，不做任何处理
        }
    }

    /**
     * 状态变更监听
     */
    interface StateChangeListener<State : Any, Event : Any, SideEffect : Any> {
        /**
         * 退出某个状态时的回调
         * [state] 退出的状态
         * [causeEvent] 导致退出的事件
         */
        fun onStateExit(state: State, causeEvent: Event)

        /**
         * 进入某个状态时的回调
         * [state] 进入的状态
         * [causeEvent] 导致进入的事件
         */
        fun onStateEnter(state: State, causeEvent: Event)

        /**
         * 发生由状态变更导致的事件
         */
        fun onSideEffectOccur(sideEffect: SideEffect)
    }

    class InnerState<State : Any, Event : Any, SideEffect : Any> {
        val transitions = LinkedHashMap<Event, Transition<State, Event, SideEffect>>()
    }

    data class Transition<State : Any, Event : Any, SideEffect : Any>(
        val toState: State,
        val event: Event,
        val sideEffect: SideEffect?
    )

    data class TransitionTo<out STATE : Any, out SIDE_EFFECT : Any> internal constructor(
        val toState: STATE,
        val sideEffect: SIDE_EFFECT?
    )


    class StateMachineBuilder<State : Any, Event : Any, SideEffect : Any> {
        private var initState: State? = null
        private val stateDefinitions = LinkedHashMap<State, InnerState<State, Event, SideEffect>>()
        private var onStateEnterBlock: ((state: State, causeEvent: Event) -> Unit)? = null
        private var onStateExitBlock: ((state: State, causeEvent: Event) -> Unit)? = null
        private var onSideEffectOccurBlock: ((sideEffect: SideEffect) -> Unit)? = null

        fun setInitState(state: State) {
            this.initState = state
        }

        fun state(state: State, block: StateDefinitionBuilder<State>.() -> Unit) {
            stateDefinitions[state] = StateDefinitionBuilder(state).also(block).build()
        }

        fun onStateEnter(block: (state: State, causeEvent: Event) -> Unit) {
            this.onStateEnterBlock = block
        }

        fun onStateExit(block: (state: State, causeEvent: Event) -> Unit) {
            this.onStateExitBlock = block
        }

        fun onSideEffectOccurBlock(block: (sideEffect: SideEffect) -> Unit) {
            this.onSideEffectOccurBlock = block
        }

        internal fun build() = SimpleStateMachine(
            initState ?: throw IllegalStateException("initState must be def"),
            stateDefinitions,
            object : StateChangeListener<State, Event, SideEffect> {
                override fun onStateExit(state: State, causeEvent: Event) {
                    onStateExitBlock?.invoke(state, causeEvent)
                }

                override fun onStateEnter(state: State, causeEvent: Event) {
                    onStateEnterBlock?.invoke(state, causeEvent)
                }

                override fun onSideEffectOccur(sideEffect: SideEffect) {
                    onSideEffectOccurBlock?.invoke(sideEffect)
                }
            }
        )


        inner class StateDefinitionBuilder<S : State>(
            private val state: S
        ) {
            private val stateDefinition = InnerState<State, Event, SideEffect>()

            fun on(event: Event, createTransitionTo: S.() -> TransitionTo<State, SideEffect>) {
                val transitionTo = createTransitionTo.invoke(state)
                stateDefinition.transitions[event] =
                    Transition(transitionTo.toState, event, transitionTo.sideEffect)
            }

            fun on(
                vararg events: Event,
                createTransitionTo: S.() -> TransitionTo<State, SideEffect>
            ) {
                events.forEach {
                    on(it, createTransitionTo)
                }
            }

            @Suppress("unused")
            fun S.transitionTo(state: State, sideEffect: SideEffect? = null) =
                TransitionTo(state, sideEffect)

            @Suppress("unused")
            fun S.dontTransition(sideEffect: SideEffect? = null) = TransitionTo(this, sideEffect)

            internal fun build() = stateDefinition
        }
    }


    companion object {
        fun <State : Any, Event : Any, SideEffect : Any> create(block: StateMachineBuilder<State, Event, SideEffect>.() -> Unit): SimpleStateMachine<State, Event, SideEffect> {
            return StateMachineBuilder<State, Event, SideEffect>().also(block)
                .build()
        }
    }
}
