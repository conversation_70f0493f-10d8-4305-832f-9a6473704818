package com.lizhi.component.tekiplayer.engine

import android.media.MediaFormat
import java.nio.ByteBuffer
import kotlin.jvm.Throws

/**
 * 文件名：AudioSink
 * 作用：
 * 作者：luo<PERSON><PERSON><EMAIL>
 * 创建日期：2021/2/22
 */
interface AudioSink {

    val bufferEndListener: (zeroData:Boolean) -> Unit

    fun configure(
        sampleRate: Int,
        channels: Int,
        speed: Float,
        volume: Float
    )

    fun setOutputFormat(format: MediaFormat)

    fun play()

    fun playToEnd(zeroData: Boolean)

    fun setSpeed(speed: Float)

    @Throws(IllegalStateException::class)
    fun handleBuffer(outputBuffer: ByteBuffer, timeUs: Long): Boolean

    fun pause()

    fun release()

    fun getCurrentPositionUs(): Long

    fun resetPositionUs(positionUs: Long): Boolean

    fun setVolume(volume: Float)

    fun flush()
}