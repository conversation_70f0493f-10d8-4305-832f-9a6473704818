//package com.lizhi.component.tekiplayer.audioprogram.extractor.android
//
//import android.content.Context
//import android.media.MediaFormat
//import android.os.Build.VERSION
//import android.os.Build.VERSION_CODES
//import com.lizhi.component.tekiplayer.audioprogram.extractor.Extractor
//import com.lizhi.component.tekiplayer.audioprogram.extractor.ExtractorInput
//import com.lizhi.component.tekiplayer.datasource.DataSource
//import com.lizhi.component.tekiplayer.engine.DataQueue
//import com.lizhi.component.tekiplayer.util.TekiLog
//import java.nio.ByteBuffer
//
///**
// * 文件名：AndroidExtractor
// * 作用：
// * 作者：<EMAIL>
// * 创建日期：2021/3/22
// */
//class AndroidExtractor(
//    private val context: Context,
//    dataQueue: DataQueue,
//    private val reInitOnV21: (Extractor, Long) -> Unit
//) : Extractor {
//
//    companion object {
//        const val MAX_FRAME_SIZE = 4096
//        const val TAG = "AndroidExtractor"
//    }
//
//    override val sampleTimeUs: Long?
//        get() = extractor.sampleTimeUs
//
//    override val mediaFormat: MediaFormat?
//        get() = extractor.mediaFormat
//
//    override val durationUs: Long?
//        get() = extractor.durationUs
//
//    override fun getTimeUs(position: Long): Long {
//        return extractor.getTimeUs(position)
//    }
//
//    override fun timeToPosition(positionUs: Long): Long {
//        return extractor.timeToPosition(positionUs)
//    }
//
//    private val extractor: Extractor = if (VERSION.SDK_INT >= VERSION_CODES.M) {
////        AndroidV21Extractor(dataQueue, reInitOnV21)
//        AndroidV23Extractor()
//    } else {
//        AndroidV21Extractor(
//            dataQueue, reInitOnV21
//        )
//    }
//
//    override fun init(
//        filePath: DataQueue?,
//        dataSource: DataSource
//    ) {
//        extractor.init(filePath, dataSource)
//    }
//
//    override fun sample(dataReader: ExtractorInput?): Int {
//        return extractor.sample(dataReader)
//    }
//
//    override fun seek(positionUs: Long): Int {
//        TekiLog.i(TAG, "seek positon=$positionUs")
//        return extractor.seek(positionUs)
//    }
//
//    override fun readSample(buffer: ByteBuffer): Int {
//        return extractor.readSample(buffer)
//    }
//
//}