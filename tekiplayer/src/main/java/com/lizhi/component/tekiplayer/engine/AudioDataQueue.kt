package com.lizhi.component.tekiplayer.engine

import com.lizhi.component.tekiplayer.audioprogram.extractor.ExtractorInput
import com.lizhi.component.tekiplayer.audioprogram.extractor.ParsableByteArray
import com.lizhi.component.tekiplayer.configuration.BufferSizePolicy
import com.lizhi.component.tekiplayer.configuration.NetType.TYPE_WIFI
import com.lizhi.component.tekiplayer.engine.DataQueue.DataLevel
import com.lizhi.component.tekiplayer.engine.DataQueue.DataLevelWatcher
import com.lizhi.component.tekiplayer.util.TekiLog
import java.io.IOException
import java.util.concurrent.atomic.AtomicLong

/**
 * 文件名：AudioDataQueue
 * 作用：音频数据采样缓存队列
 * 作者：<EMAIL>
 * 创建日期：2021/3/15
 */
class AudioDataQueue(
    private val bufferPolicy: BufferSizePolicy,
    private val watcher: DataLevelWatcher
) : DataQueue {

    companion object {
        const val TAG = "AudioDataQueue"
        const val CHANGE_REASON_ENQUEUE = 0
        const val CHANGE_REASON_DEQUEUE = 1
        const val CHANGE_REASON_CLEAR = 2
        const val CHANGE_REASON_SEEK = 3
    }

    /**
     * 同一个链表中不同的位置节点，一个是正在读的位置，一个是正在写的位置。
     * 实际上就是这个链表的头和尾，中间这段就是数据的水位。
     */
    private var readingFrameData: FrameDataNode? = null
    private var writtenFrameData: FrameDataNode? = null

    private var waterLine = AtomicLong()

    private var currReadingPosition: Long = 0
    private var currWrittenPosition: Long = 0
    private var currWrittenBufferPositionUs: Long = 0

    override fun seek(position: Long): Boolean {
        // TODO 内存中 seek 功能暂时有问题，未完全适配各种格式，返回 false 让数据重新读取一次
//        TekiLog.w(TAG, "seek($position). dataQueue.clear")
        clear()
        notifyWaterLineChange(CHANGE_REASON_CLEAR)
        return false

//        if (currWrittenPosition <= position || currReadingPosition > position) {
//            clear()
//            notifyWaterLineChange(CHANGE_REASON_CLEAR)
//            return false
//        }
//        var reading: FrameDataNode = readingFrameData ?: return false
//        /*
//         * readingData是向后增长的音频采样缓存链表的头部节点
//         * 通过抛弃readingData的节点，找到包含指定位置的节点，让当前节点成为readingData的新头部
//         */
//        while (reading.endPosition < position) {
//            reading = reading.clear()?.also {
//                waterLine.addAndGet(-(it.endPosition - it.startPosition))
//            } ?: break
//        }
//        reading.findSeekIndex(position)
//        // 重置offset，读取的时候从offset开始，达到seek的效果
//        readingFrameData = reading
//        currReadingPosition = reading.startPosition
//
//        notifyWaterLineChange(CHANGE_REASON_SEEK)
//        return true
    }

    /**
     * 需要先录入元数据，生成数据的占位，再录入音频数据
     */
    override fun sampleMetadata(presentationTimeUs: Long, frameSize: Int, position: Long) {
        val writeFrameData = writtenFrameData
        if (writeFrameData == null) {
            writtenFrameData = FrameDataNode.obtain(position).apply {
                sampleMetadata(frameSize, presentationTimeUs)
            }
//            TekiLog.d(TAG, "sampleMetadata $writtenFrameData presentationTimeUs=$presentationTimeUs, frameSize=$frameSize, position=$position")
        } else {
//            TekiLog.d(TAG, "sampleMetadata else")
            val canFullyWrite = writeFrameData.sampleMetadata(frameSize, presentationTimeUs)
            if (!canFullyWrite) {
                val obtain = FrameDataNode.obtain(position)
                obtain.sampleMetadata(frameSize, presentationTimeUs)
                writtenFrameData?.next = obtain
            }
        }
    }

    @Throws(IOException::class)
    override fun readToQueue(
        reader: ExtractorInput,
        remaining: Int,
        presentationTimeUs: Long
    ): Int {
        if (remaining == -1) {
            TekiLog.w(TAG, "threadName=${Thread.currentThread().name}, readToQueue end")
//            TekiLog.i(TAG, "readToQueue end, fileSize=$size")
            return -1
        }
//        TekiLog.i(TAG, "threadName=${Thread.currentThread().name}, readToQueue frameSize=$frameSize, waterline=$waterLine")
        // 如果当前缓存块满了，换另外一块
        val writeAdvance = writtenFrameData?.isFull()
        if (writeAdvance == true) {
            writtenFrameData = writtenFrameData?.next
        }
        if (null == writtenFrameData) {
            TekiLog.w(TAG, "readToQueue final writtenFrameData is null")
        }
        val writeFrameData = writtenFrameData ?: return -1

        val len = reader.read(writeFrameData.data, writeFrameData.writtenBytes, remaining)
        if (len == -1 && reader.position >= reader.length) {
            writeFrameData.endOfStream()
            TekiLog.i(TAG, "readToQueue end")
        } else {
//            TekiLog.i(TAG, "readToQueue, fileSize=$size")
            if (len < 0) {
                TekiLog.w(TAG, "readToQueue len < 0")
            }
            val currPresentationTimeUs = writeFrameData.currPresentationTimeUs
            writeFrameData.endSample(len)

            waterLine.getAndAdd(len.toLong())
            currWrittenPosition = writeFrameData.endPosition
            if (currPresentationTimeUs > 0) {
                currWrittenBufferPositionUs = currPresentationTimeUs
            }
        }

        if (readingFrameData == null) {
            readingFrameData = writtenFrameData
//            TekiLog.w(TAG, "new readingFrameData startPosition=${writtenFrameData?.startPosition}")
        }

        notifyWaterLineChange(CHANGE_REASON_ENQUEUE)
        return len
    }

    override fun readToQueue(data: ParsableByteArray, bytesToRead: Int, timeUs: Long): Int {

//        TekiLog.i(TAG, "threadName=${Thread.currentThread().name}, readToQueue frameSize=$frameSize, waterline=$waterLine")
        // 如果当前缓存块满了，换另外一块
        val writeAdvance = writtenFrameData?.isFull()
        if (writeAdvance == true) {
            writtenFrameData = writtenFrameData?.next
        }
        val writeFrameData = writtenFrameData ?: return -1

//        TekiLog.i(TAG, " readToQueue , fileSize=$size")
        if (bytesToRead == -1) {
            TekiLog.i(TAG, "readToQueue end")
            writeFrameData.endOfStream()
            return -1
        }
        data.readBytes(writeFrameData.data, writeFrameData.writtenBytes, bytesToRead)
        val currPresentationTimeUs = writeFrameData.currPresentationTimeUs
        writeFrameData.endSample(bytesToRead)

        waterLine.getAndAdd(bytesToRead.toLong())
        currWrittenPosition = writeFrameData.endPosition
        if (currPresentationTimeUs > 0) {
            currWrittenBufferPositionUs = currPresentationTimeUs
        }

        if (readingFrameData == null) {
            readingFrameData = writtenFrameData
//            TekiLog.w(TAG, "new readingFrameData startPosition=${writtenFrameData?.startPosition}")
        }

        notifyWaterLineChange(CHANGE_REASON_ENQUEUE)
        return bytesToRead
    }

    override fun setEndFlag() {
        TekiLog.i(TAG, "setEndFlag")
        writtenFrameData?.endOfStream(true)
    }

    override fun getBufferPositionUs(): Long {
        return currWrittenBufferPositionUs
    }

    override fun blockDequeue(
        buffer: BufferHolder
    ): Int {
        val reading = readingFrameData ?: return -1
        if (reading.readEndOfStream) {
            readingFrameData = null
            return -1
        }
        if (reading.readSampleIndex == reading.writeSampleIndex && reading.maxSampleIndex != reading.writeSampleIndex) {
            return 0
        }

        val data = reading.data
        val copyLength = reading.sizes[reading.readSampleIndex]
        val offset = reading.offsets[reading.readSampleIndex]
//        TekiLog.d(
//            TAG,
//            "reading position=${offset + reading.startPosition} copyLength=${copyLength} timePosition=${reading.presentationTimeUss[reading.readSampleIndex]}"
//        )
//        buffer.data?.put(data, offset, copyLength)
//        buffer.data?.flip()
        buffer.setData(data, offset, copyLength)
        buffer.timePositionUs = reading.presentationTimeUss[reading.readSampleIndex]
        buffer.position = (offset + reading.startPosition).toInt()
        buffer.size = copyLength

        val shouldAdvance = reading.endRead()
        waterLine.addAndGet((-copyLength).toLong())

        if (shouldAdvance) {
            readingFrameData = reading.next
        }
        currReadingPosition = if (shouldAdvance) {
            reading.endPosition
        } else {
            reading.offsets[reading.readSampleIndex] + reading.startPosition
        }
//        TekiLog.d(TAG, "blockDequeue, currReadPosition=$currReadingPosition")
        if (shouldAdvance) {
            reading.recycle()
        }

        notifyWaterLineChange(CHANGE_REASON_DEQUEUE)
        return copyLength
    }

    override fun allowInput(allow: Boolean) {
        TekiLog.i(TAG, "allowInput:$allow")
    }

    override fun getWrittenPosition(): Long {
        return currWrittenPosition
    }

    private fun notifyWaterLineChange(reason: Int) {
        when {
            waterLine.get() >= bufferPolicy.playingProgramBufferSize(TYPE_WIFI) -> {
                watcher.onLevelChange(DataLevel.HIGH, waterLine.get(), reason)
            }
            waterLine.get() in 1..bufferPolicy.shouldLoadingThresholds(TYPE_WIFI) -> {
                watcher.onLevelChange(DataLevel.LOW, waterLine.get(), reason)
            }
            waterLine.get() <= 0 -> {
                watcher.onLevelChange(DataLevel.EMPTY, waterLine.get(), reason)
                waterLine.set(0)
            }
        }
    }

    override fun getBufferSize(): Long {
        return waterLine.get()
    }

    override fun clear() {
        waterLine.set(0)
        currReadingPosition = 0
        currWrittenPosition = 0
        currWrittenBufferPositionUs = 0
        readingFrameData = null
        writtenFrameData = null
        TekiLog.w(TAG, "clear")
    }

}