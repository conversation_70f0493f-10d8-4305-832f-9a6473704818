package com.lizhi.component.tekiplayer.configuration

/**
 * 文件名：BufferSizeConfig
 * 作用：
 * 作者：l<PERSON><PERSON><PERSON><PERSON>@lizhi.fm
 * 创建日期：2021/4/3
 */
interface BufferSizePolicy {

    fun playingProgramBufferSize(net: NetType): Long

    fun preloadBufferSize(): Long

    fun shouldLoadingThresholds(net: NetType): Long

    fun autoPrepareMediaCount(): Int

    fun limitMaxDiskHighPrioritySpaceUsage(): Long

    fun limitMaxDiskSpaceUsage(): Long

    fun limitFreeDiskSpaceUsage(): Long
}

enum class NetType {
    TYPE_5G, TYPE_4G, TYPE_3G, TYPE_WIFI, TYPE_2G
}