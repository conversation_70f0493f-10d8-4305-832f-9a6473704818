package com.lizhi.component.tekiplayer.engine

import android.media.MediaFormat
import android.os.Handler

/**
 * 文件名：Engine
 * 作用：
 * 作者：luo<PERSON><EMAIL>
 * 创建日期：2021/2/19
 */
interface Engine {

    var decoderName: String?

    fun isPlaying(): Boolean

    fun start(restart: Boolean = false)

    fun stop()

    fun resume()

    fun pause()

    fun configFormat(format: MediaFormat)

    fun getPositionUs(): Long

    fun resetPositionUs(positionUs: Long)

    fun setSpeed(speed: Float)

    fun setCallback(callback: Callback, handler: Handler)

    fun setVolume(volume: Float)

    interface Callback {

        fun onNeedMoreData()

        fun onShouldContinueFeedData()

        fun onPlaybackFirstFrameRender()

        fun onPlaybackPlayed()

        fun onPlaybackPaused()

        fun onPlaybackStopped()

        fun onPlaybackResumed()

        fun onPlaybackFlush()

        fun onPlaybackEnd(zeroData:Boolean)

        fun onPlaybackException(exception: Exception, recoverable: Boolean)

    }

    fun flush()
}