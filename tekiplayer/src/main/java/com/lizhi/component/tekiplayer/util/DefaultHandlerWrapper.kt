package com.lizhi.component.tekiplayer.util

import android.os.Handler
import android.os.HandlerThread
import android.os.Looper
import android.os.Message

class DefaultHandlerWrapper(
    threadName: String,
    priority: Int,
    handlerFunc: (Message) -> Unit = {}
) : HandlerWrapper {

    val looper: Looper by lazy {
        threadHandler.looper
    }

    private val threadHandler = HandlerThread(threadName, priority).apply { start() }

    private var handler: Handler = Handler(threadHandler.looper) {
        handlerFunc(it)
        true
    }

    override fun post(runnable: () -> Unit) {
        handler.post(runnable)
    }

    override fun sendMessage(message: Message) {
        handler.sendMessage(message)
    }


    override fun sendMessageDelay(message: Message, delay: Long) {
        handler.sendMessageDelayed(message, delay)
    }

    override fun sendMessageAtFront(message: Message) {
        handler.sendMessageAtFrontOfQueue(message)
    }

    override fun quitSafely() {
        threadHandler.quitSafely()
    }

    override fun removeMessage(what: Int) {
        handler.removeMessages(what)
    }

    override fun removeMessages() {
        handler.removeCallbacksAndMessages(null)
    }
}