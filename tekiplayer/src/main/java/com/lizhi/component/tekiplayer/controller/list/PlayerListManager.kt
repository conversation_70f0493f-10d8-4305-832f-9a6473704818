package com.lizhi.component.tekiplayer.controller.list

import com.lizhi.component.tekiplayer.MediaItem
import com.lizhi.component.tekiplayer.Player
import com.lizhi.component.tekiplayer.REPEAT_MODE_ALL
import com.lizhi.component.tekiplayer.REPEAT_MODE_OFF
import com.lizhi.component.tekiplayer.REPEAT_MODE_ONE
import com.lizhi.component.tekiplayer.REPEAT_MODE_SHUFFLE
import com.lizhi.component.tekiplayer.audioprogram.Program.Factory
import com.lizhi.component.tekiplayer.controller.AudioProgramHolder
import com.lizhi.component.tekiplayer.controller.exception.IllegalRepeatModeException
import com.lizhi.component.tekiplayer.util.TekiLog
import com.lizhi.component.tekiplayer.util.Util
import java.util.Collections

/**
 * 文件名：PlayerListManager
 * 作用：播放列表管理器
 * 作者：huangtianhao
 * 创建日期：2021/3/26
 */
class PlayerListManager(
    private val factory: Factory,
    private val autoPrepareMediaCount: Int,
    private val recordPlaybackPosition: Boolean = false
) : ListManager {

    private val mutablePlayItemList: MutableList<MediaItem> = Collections.synchronizedList(ArrayList())
    private val playItemList: List<MediaItem> = mutablePlayItemList
    private val randomPositionList: MutableList<Int> by lazy {
        ArrayList<Int>()
    }

    private var currentProgram: AudioProgramHolder? = null

    /**
     * 预加载中的链表
     */
    private var loading: AudioProgramHolder? = null

    /**
     * 预加载成功的链表
     */
    private var ready: AudioProgramHolder? = null

    /**
     * 循环模式
     */
    @Player.RepeatMode
    override var repeatMode: Int = REPEAT_MODE_OFF
        set(value) {
            field = value
        }

    private var currentPosition = UNSET
    private var preparedPosition = UNSET

    private var listener: OnPlayListUpdateListener? = null

    companion object {
        private const val TAG = "PlayerListManager"
        private const val UNSET = -1

        private const val REASON_ADD_ITEM = 0
        private const val REASON_REMOVE_RANGE = 1
        private const val REASON_REMOVE_ITEM = 2
        private const val REASON_CLEAR = 3
        private const val REASON_COMPLETE = 4
        private const val REASON_USER = 5
        private const val REASON_ERROR = 6
    }

    override fun addMediaItem(item: MediaItem) = updatePositionFunction(REASON_ADD_ITEM) {
        mutablePlayItemList.add(item)
    }

    override fun addMediaItem(
        position: Int,
        item: MediaItem
    ) =
        updatePositionFunction(REASON_ADD_ITEM) {
            mutablePlayItemList.add(position, item)
        }

    override fun addMediaItem(list: List<MediaItem>) = updatePositionFunction(REASON_ADD_ITEM) {
        mutablePlayItemList.addAll(list)
    }

    override fun addMediaItem(
        position: Int,
        list: List<MediaItem>
    ) =
        updatePositionFunction(REASON_ADD_ITEM) {
            mutablePlayItemList.addAll(position, list)
        }

    override fun removeRange(
        startPosition: Int,
        length: Int
    ) =
        updatePositionFunction(REASON_REMOVE_RANGE) {
            mutablePlayItemList.removeAll(mutablePlayItemList.subList(startPosition, length))
        }

    override fun removeItem(item: MediaItem) = updatePositionFunction(REASON_REMOVE_ITEM) {
        mutablePlayItemList.remove(item)
    }

    override fun removeItem(position: Int) = updatePositionFunction(REASON_REMOVE_ITEM) {
        mutablePlayItemList.removeAt(position)
    }

    override fun getMediaItem(position: Int): MediaItem? {
        return mutablePlayItemList.getOrNull(position)
    }

    override fun getMediaItemList() = playItemList

    override fun clear() = updatePositionFunction(REASON_CLEAR) {
        mutablePlayItemList.clear()
    }

    override fun shouldPrepareNextProgram(): Boolean {
        TekiLog.d(TAG, "check should prepare next program $loading $ready")
        if (this.loading != null) {
            TekiLog.d(TAG, "$loading loading is not empty, should not prepare next..")
            return false
        }
        val count = this.ready?.count { it.position > currentPosition } ?: 0
        if (count >= autoPrepareMediaCount) {
            TekiLog.d(TAG, "[count] size is $count, bigger than $autoPrepareMediaCount")
            return false
        }
        if (preparedPosition < currentPosition) {
            preparedPosition = currentPosition
        }
        if (preparedPosition + 1 == mutablePlayItemList.size) {
            TekiLog.d(TAG, "reach the end of the list, should not prepare next")
            return false
        }

        TekiLog.d(TAG, "should prepare next program, start preload $preparedPosition")
        return true
    }

    override var quality: Player.Quality = Player.Quality.HIGH

    /**
     * 调用下一首，如果是用户输入时，[force]为true；自然播放结束时，[force]应为false
     * 此时忽略单曲循环、顺序播放的规则（这两个规则只适用于自动结束的情形）
     * 除了随机播放仍走随机逻辑外，其余都执行类似列表循环的逻辑
     */
    override fun moveToNextProgram(force: Boolean): AudioProgramHolder? {
        if (!force) {
            return autoMoveToNextProgram()
        }

//        TekiLog.i(TAG, "moveToNextProgram $currentPosition")
        val program = findOrCreateAudioProgramHolderAt(
            getNextPositionOrReset(
                currentPosition
            )
        )
        updateCurrentProgram(program)
        return program
    }

    private fun autoMoveToNextProgram(): AudioProgramHolder? {
        return when (repeatMode) {
            REPEAT_MODE_OFF -> {
                // 顺序播放，正常取下一首，但到达末尾后，停止
                if (currentPosition != playItemList.lastIndex) {
                    moveToNextProgram()
                } else {
                    updateCurrentProgram(null)
                    null
                }
            }
            REPEAT_MODE_ONE -> {
                // 单曲循环，继续播放当前歌曲
                if (currentProgram == null) {
                    moveToNextProgram()
                } else {
                    stopAndRemovePreviousProgram(currentProgram)
                    updateCurrentProgram(findOrCreateAudioProgramHolderAt(currentPosition))
                    currentProgram
                }
            }
            REPEAT_MODE_ALL -> {
                // 列表循环
                moveToNextProgram()
            }
            REPEAT_MODE_SHUFFLE -> {
                // 随机播放
                moveToNextProgram()
            }
            else -> throw IllegalRepeatModeException(repeatMode)
        }
    }

    private fun getNextPositionOrReset(currentPos: Int): Int {
        if (playItemList.isEmpty()) {
            return UNSET
        }

        val index = if (repeatMode == REPEAT_MODE_SHUFFLE) {
            randomPositionList.indexOf(currentPos)
        } else {
            currentPos
        }

        val pos = if (isPositionInRange(index + 1)) {
            index + 1
        } else {
            updateShuffleList()
            0
        }

        return wrapRandomPosition(pos)
    }

    private fun isPositionInRange(position: Int): Boolean {
        return position >= 0 && position < playItemList.size
    }

    /**
     * 外部调用上一首
     * 此时忽略单曲循环、顺序播放的规则（这两个规则只适用于自动结束的情形）
     * 除了随机播放仍走随机逻辑外，其余都执行类似列表循环的逻辑
     */
    override fun moveToPreviousProgram(): AudioProgramHolder? {
        val program =
            findOrCreateAudioProgramHolderAt(getPreviousPositionOrReset(currentPosition))
        updateCurrentProgram(program)
        return program
    }

    private fun getPreviousPositionOrReset(currentPos: Int): Int {
        if (playItemList.isEmpty()) {
            return UNSET
        }

        val index = if (repeatMode == REPEAT_MODE_SHUFFLE) {
            randomPositionList.indexOf(currentPos)
        } else {
            currentPos
        }

        val pos = if (isPositionInRange(index - 1)) {
            index - 1
        } else {
            updateShuffleList()
            playItemList.lastIndex
        }

        return wrapRandomPosition(pos)
    }

    /**
     * 移动到指定位置，且设置初始播放位置
     */
    override fun moveToProgramOnPosition(
        position: Int,
        pendingSeekPositionMs: Long
    ): AudioProgramHolder? {
        if (currentPosition == position) {
            // 处理切换到当前节目的情况，需要重建audioProgram
            stopAndRemovePreviousProgram(currentProgram)
        }
        if (pendingSeekPositionMs > -1) {
            getMediaItem(position)?.pendingStartPositionMs = pendingSeekPositionMs
        }
        val audioProgramHolder = findOrCreateAudioProgramHolderAt(position)
        updateCurrentProgram(audioProgramHolder)
        return audioProgramHolder
    }

    override fun prepareNextProgram(callback: (() -> Unit)?): AudioProgramHolder? {
        if (preparedPosition == UNSET && currentPosition != UNSET) {
            preparedPosition = currentPosition
        }
        if (preparedPosition < currentPosition) {
            preparedPosition = currentPosition
        }
        if (preparedPosition + 1 == mutablePlayItemList.size) {
            return null
        }
        val createdAudioProgramHolder = when (repeatMode) {
            REPEAT_MODE_OFF -> {
                findOrCreateAudioProgramHolderAt(++preparedPosition, true)
            }
            REPEAT_MODE_ONE -> {
                if (preparedPosition == UNSET) {
                    findOrCreateAudioProgramHolderAt(++preparedPosition, true)
                } else {
                    return null
                }
            }
            REPEAT_MODE_ALL -> {
                // 列表循环
                findOrCreateAudioProgramHolderAt(
                    getNextPositionOrReset(++preparedPosition), true
                )
            }
            REPEAT_MODE_SHUFFLE -> {
                // 随机播放
                findOrCreateAudioProgramHolderAt(
                    getNextPositionOrReset(++preparedPosition), true
                )
            }
            else -> throw IllegalRepeatModeException(repeatMode)
        }

        TekiLog.i(TAG, "try to prepare item at $preparedPosition")
//        if (currentPosition == UNSET) {
//            updateCurrentProgram(createdAudioProgramHolder)
//        }

        return createdAudioProgramHolder
    }

    override fun hasNext(): Boolean {
        return when (repeatMode) {
            REPEAT_MODE_OFF -> {
                isPositionInRange(currentPosition + 1)
            }
            REPEAT_MODE_SHUFFLE, REPEAT_MODE_ALL, REPEAT_MODE_ONE -> {
                // 随机播放
                playItemList.isNotEmpty()
            }
            else -> throw IllegalRepeatModeException(repeatMode)
        }
    }

    override fun hasPrevious(): Boolean {
        return when (repeatMode) {
            REPEAT_MODE_OFF -> {
                isPositionInRange(currentPosition - 1)
            }
            REPEAT_MODE_SHUFFLE, REPEAT_MODE_ALL, REPEAT_MODE_ONE -> {
                // 随机播放
                playItemList.isNotEmpty()
            }
            else -> throw IllegalRepeatModeException(repeatMode)
        }
    }


    override fun setOnPlayListUpdateListener(listener: OnPlayListUpdateListener) {
        this.listener = listener
    }

    private fun updatePositionWhenPlayListChanged(reason: Int) {
        updateAllProgramPosition()
        updateShuffleList()
        listener?.onPlayListChanged(reason)
        TekiLog.i(
            TAG,
            "updatePositionWhenPlayListChanged currentProgram = $currentProgram, reason = $reason"
        )
        val cur = currentProgram ?: return
        val position = getMediaItemPointerIndexOf(cur.mediaItem)
        TekiLog.i(TAG, "updatePositionWhenPlayListChanged new pos = $position")
        cur.position = position
        cur.clearLink()
        currentPosition = position

        if (cur.position == -1) {
            listener?.onPlayingItemRemoved(reason)
            updateCurrentProgram(null)
        } else {
            listener?.onPlayPositionChanged(currentPosition, cur.program.getPositionUs(), reason)
        }
    }

    private fun findOrCreateAudioProgramHolderAt(
        position: Int,
        preBuffering: Boolean = false,
        forceCreate: Boolean = true
    ): AudioProgramHolder? {
        TekiLog.i(
            TAG,
            "finding audioProgram [$position] on loading [${loading}], on ready [${ready}] "
        )

        var needToRemoveItem: AudioProgramHolder? = null
        loading?.forEach {
            if (it.position == position) {
                if (it.position == UNSET) {
                    needToRemoveItem = it
                } else if (it.program.isReady() || it.program.isBuffering()) {
                    TekiLog.i(TAG, "find usable holder for position=$position on loading=$loading")
                    return it
                } else {
                    if (!forceCreate) {
                        return it
                    }
                    needToRemoveItem = it
                }
            }
        }
        needToRemoveItem?.let { removeFromLoading(it) }

        ready?.forEach {
            if (it.position == position) {
                if (it.position == UNSET) {
                    needToRemoveItem = it
                } else if (it.program.isReady() || it.program.isBuffering()) {
                    TekiLog.i(TAG, "find usable holder for position=$position on ready=$ready")
                    return it
                } else {
                    if (!forceCreate) {
                        return it
                    }
                    needToRemoveItem = it
                }
            }
        }
        needToRemoveItem?.let { removeFromReady(it) }
        TekiLog.i(TAG, "can not find usable holder for position=$position onloading or ready")
        if (!forceCreate) {
            return null
        }
        return createAudioProgramHolder(position, preBuffering)
    }

    private fun createAudioProgramHolder(
        position: Int,
        preBuffering: Boolean = false
    ): AudioProgramHolder? {
        if (position < 0 || position >= getMediaItemList().size) {
            return null
        }

        val mediaItem = getMediaItem(position) ?: return null

        val (quality, uri) = mediaItem.getUriOnQuality(quality)
        val audioProgram =
            factory.createProgram(
                uri,
                Util.msToUs(mediaItem.pendingStartPositionMs),
                preBuffering,
                Util.msToUs(mediaItem.fixedDuration),
                mediaItem.category,
                mediaItem.extraData
            )

        // FIXME: 这里应该总跟 position 等值
        val indexOfMediaItem = getMediaItemPointerIndexOf(mediaItem)
        if (indexOfMediaItem == -1) {
            TekiLog.w(
                TAG,
                "createAudioProgramHolder on position=$position, indexOfMediaItem=$indexOfMediaItem"
            )
            return null
        }
        val holder =
            AudioProgramHolder(indexOfMediaItem, mediaItem, audioProgram, quality)

        if (this.loading != null) {
            this.loading?.add(holder)
        } else {
            this.loading = holder
        }

        TekiLog.i(TAG, "createAudioProgramHolder on position=$position")

        if (audioProgram.isReady()) {
            listener?.onItemReady(holder)
        } else {
            audioProgram.preload(
                {
                    TekiLog.i(TAG, "preload completed on position=$position")

                    if (loading?.isExisted(holder) == true) {
                        removeFromLoading(holder)

                        if (currentProgram != holder) {
                            val ready = ready
                            if (ready != null) {
                                if (ready != holder && !ready.isExisted(holder)) {
                                    ready.add(holder)
                                }
                            } else {
                                holder.clearPrevious()
                                this.ready = holder
                            }
                        }

                        listener?.onItemReady(holder)
                    }
                },
                error = { _, throwable ->
                    TekiLog.i(TAG, "preload fail on position=$position error=$throwable")
//                    if (this.loading != null && loading!!.isExisted(holder)) {
                    if (loading?.isExisted(holder) == true) {
                        removeFromLoading(holder)
                        if (currentProgram == holder) {
                            listener?.onPlayingItemPreloadFailed()
                            // currentProgram = null
                        }
                    }
                })
        }
        return holder
    }

    private fun removeFromLoading(holder: AudioProgramHolder) {
        if (this.loading == holder) {
            this.loading = this.loading?.next
        } else {
            this.loading?.removeItem(holder)
        }
    }

    private fun removeFromReady(holder: AudioProgramHolder) {
        if (this.ready == holder) {
            this.ready = this.ready?.next
        } else {
            this.ready?.removeItem(holder)
        }
    }

    override fun getCurrentProgram(): AudioProgramHolder? {
        return currentProgram ?: findOrCreateAudioProgramHolderAt(
            currentPosition,
            forceCreate = false
        )
    }

    override fun updatePreBufferingProgramSpeed(speed: Float) {
        ready?.forEach {
            it.program.setPlayRate(speed)
        }
        loading?.forEach {
            it.program.setPlayRate(speed)
        }
    }

    override fun updatePreBufferingProgramVolume(volume: Float) {
        ready?.forEach {
            it.program.volume = volume
        }
        loading?.forEach {
            it.program.volume = volume
        }
    }

    override fun clearPlayingAndPreBufferingProgram() {
        ready?.forEach {
            it.program.stop()
        }
        ready = null
        loading?.forEach {
            it.program.stop()
        }
        loading = null
        currentProgram = null
        currentPosition = UNSET
        preparedPosition = UNSET
    }


    private fun updateCurrentProgram(audioProgramHolder: AudioProgramHolder?) {
        TekiLog.i(TAG, "updateCurrentProgram ${audioProgramHolder?.position}")
        TekiLog.i(TAG, "updateCurrentProgram ready ${ready.toString()}")

        val previousProgram = currentProgram
        if (previousProgram != audioProgramHolder) {
            stopAndRemovePreviousProgram(previousProgram)
        }

        removeProgramFromList(audioProgramHolder)

        currentProgram = audioProgramHolder
        currentPosition = audioProgramHolder?.position ?: UNSET
        // 给出播放位置变化的原因
        val reason = when {
            previousProgram?.position == -1 -> REASON_REMOVE_ITEM
            previousProgram?.program?.isOnError() == true -> REASON_ERROR
            previousProgram?.program?.isEnd() == true -> REASON_COMPLETE
            else -> REASON_USER
        }

        val lastPositionUs = previousProgram?.program?.getPositionUs() ?: -1
        if (null != audioProgramHolder) {
            listener?.onPlayPositionChanged(currentPosition, lastPositionUs, reason)
        }

        resetLastMediaItemPendingSeek(previousProgram, reason, lastPositionUs)

        if (currentProgram?.program?.isReady() == true) {
            if (audioProgramHolder != null) {
                listener?.onItemReady(audioProgramHolder)
            }
        }
    }

    private fun resetLastMediaItemPendingSeek(
        previousProgram: AudioProgramHolder?,
        reason: Int,
        lastPositionUs: Long
    ) {
        previousProgram ?: return
        // 如果是用户切歌，下次从当前位置开始播放
        // 如果是播放完成，下次从0开始播放
        // 错误情况下忽略开关进行记录
        val pendingSeek =
            if (reason == REASON_USER && recordPlaybackPosition) {
                lastPositionUs
            } else {
                0
            }
        TekiLog.i(TAG, "recordPlaybackPosition, pendingSeek=$pendingSeek, program=$previousProgram")
        previousProgram.mediaItem.pendingStartPositionMs = Util.usToMs(pendingSeek)
    }

    /**
     * 停止并且移除上一个节目
     */
    private fun stopAndRemovePreviousProgram(audioProgramHolder: AudioProgramHolder?) {
        TekiLog.i(TAG, "stopAndRemovePreviousProgram ${audioProgramHolder?.position}")
        audioProgramHolder ?: return
        if (audioProgramHolder.program.isReady() || audioProgramHolder.program.isBuffering()) {
            audioProgramHolder.program.stop()
        }
        removeProgramFromList(audioProgramHolder)
    }

    private fun removeProgramFromList(audioProgramHolder: AudioProgramHolder?) {
        audioProgramHolder ?: return
            TekiLog.i(TAG, "loading list = ${loading.toString()} ready list = ${ready.toString()}")
        if (loading?.isExisted(audioProgramHolder) == true) {
            removeFromLoading(audioProgramHolder)
        }
        if (ready?.isExisted(audioProgramHolder) == true) {
            removeFromReady(audioProgramHolder)
        }
    }

    private fun updatePositionFunction(
        reason: Int,
        block: () -> Unit
    ) {
        block()
        updatePositionWhenPlayListChanged(reason)
    }

    /**
     * 重新洗牌随机播放列表
     */
    private fun updateShuffleList() {
        randomPositionList.clear()
        randomPositionList.addAll(getMediaItemList().mapIndexed { index, mediaItem ->
            index
        }.shuffled())
        TekiLog.i(TAG, "updateShuffleList list = $randomPositionList")
        removeUselessPreBufferingProgram()
    }

    /**
     * playItemList有增加/删除下，需要同步更新audioProgram
     */
    private fun updateAllProgramPosition() {
        ready?.forEach {
            it.position = getMediaItemPointerIndexOf(it.mediaItem)
        }
        loading?.forEach {
            it.position = getMediaItemPointerIndexOf(it.mediaItem)
        }
    }

    /**
     * 播放列表有更新下，需要对无效的audioProgram进行移除
     */
    private fun removeUselessPreBufferingProgram() {
        val shouldPreBufAudioProgramSet = mutableSetOf<Int>()
        // 1. 将可缓存的audioProgram加入set, 这里区分是否列表循环模式
        if (repeatMode == REPEAT_MODE_SHUFFLE) {
            if (currentProgram != null && currentProgram!!.position >= 0) {
                val beg = randomPositionList.indexOf(currentProgram!!.position)
                val end = minOf(beg + autoPrepareMediaCount, randomPositionList.size - 1)
                TekiLog.d(
                    TAG,
                    "removeUselessPreBufferingProgram randomPositionList=$randomPositionList beg=$beg end=$end cur=${currentProgram!!.position} curAudioProgramPos=${
                        getMediaItemPointerIndexOf(currentProgram!!.mediaItem)
                    }"
                )
                shouldPreBufAudioProgramSet += randomPositionList.subList(beg, end + 1).toSet()
                val gap = minOf(beg + autoPrepareMediaCount - end, beg)
                if (gap > 0) {
                    shouldPreBufAudioProgramSet += randomPositionList.subList(0, gap).toSet()
                }
            } else {
                val end = minOf(autoPrepareMediaCount, randomPositionList.size)
                shouldPreBufAudioProgramSet += randomPositionList.subList(0, end).toSet()
            }
        } else if (repeatMode == REPEAT_MODE_OFF) {
            if (currentProgram != null && currentProgram!!.position >= 0) {
                val beg = currentProgram!!.position
                val end = minOf(beg + autoPrepareMediaCount, playItemList.size - 1)
                for (i in beg..end) {
                    shouldPreBufAudioProgramSet += i
                }
            } else {
                val end = minOf(autoPrepareMediaCount - 1, playItemList.size - 1)
                for (i in 0..end) {
                    shouldPreBufAudioProgramSet += i
                }
            }
        } else if (repeatMode == REPEAT_MODE_ONE) {
            if (currentProgram != null && currentProgram!!.position >= 0) {
                shouldPreBufAudioProgramSet += currentProgram!!.position
            }
        } else if (repeatMode == REPEAT_MODE_ALL) {
            if (currentProgram != null && currentProgram!!.position >= 0) {
                val beg = currentProgram!!.position
                val end = minOf(beg + autoPrepareMediaCount, playItemList.size - 1)
                for (i in beg..end) {
                    shouldPreBufAudioProgramSet += i
                }
                val gap = minOf(beg + autoPrepareMediaCount - end, beg)
                if (gap > 0) {
                    for (i in 0 until gap) {
                        shouldPreBufAudioProgramSet += i
                    }
                }
            } else {
                val end = minOf(autoPrepareMediaCount - 1, playItemList.size - 1)
                for (i in 0..end) {
                    shouldPreBufAudioProgramSet += i
                }
            }
        } else {
            // clean all
        }

        // 2. 根据shouldPreBufAudioProgramSet对不需要用到的audioProgram进行stop并移除
        TekiLog.d(
            TAG,
            "removeUselessPreBufferingProgram [begin] list = $randomPositionList set=$shouldPreBufAudioProgramSet load=$loading ready=$ready"
        )

        // 头节点处理
        while (loading != null && !shouldPreBufAudioProgramSet.contains(loading!!.position)) {
            val del = loading
            loading = loading?.next
            del?.program?.stop()
            del?.clearLink()
        }
        // 中继节点处理
        var cur = loading
        while (cur?.next != null) {
            val next = cur.next
            if (!shouldPreBufAudioProgramSet.contains(next!!.position)) {
                val del = next
                cur.setNextItem(next.next)
                del.program.stop()
                del.clearLink()
            } else {
                cur = next
            }
        }

        // 头节点处理
        while (ready != null && !shouldPreBufAudioProgramSet.contains(ready?.position)) {
            val del = ready
            ready = ready?.next
            del?.program?.stop()
            del?.clearLink()
        }
        // 中继节点处理
        cur = ready
        while (cur?.next != null) {
            val next = cur.next
            if (!shouldPreBufAudioProgramSet.contains(next!!.position)) {
                val del = next
                cur.setNextItem(next.next)
                del.program.stop()
                del.clearLink()
            } else {
                cur = next
            }
        }

        TekiLog.d(
            TAG,
            "removeUselessPreBufferingProgram [after] list = $randomPositionList set=$shouldPreBufAudioProgramSet load=$loading ready=$ready"
        )
    }

    /**
     * 获取随机播放情况下真正的位置，
     * 如果非随机播放，直接返回传入的位置
     */
    private fun wrapRandomPosition(pos: Int): Int {
        if (repeatMode == REPEAT_MODE_SHUFFLE) {
            return (randomPositionList.getOrNull(pos) ?: UNSET).also {
                TekiLog.i(TAG, "wrapRandomPosition ori $pos res $it")
            }
        }
        return pos
    }

    /**
     * 暂停预加载
     */
    override fun pausePreload() {
        TekiLog.d(TAG, "pausePreload loading=$loading")
        loading?.forEach {
            if (it != currentProgram) {
                it.isPausedPreload = true
                it.program.stopLoading()
            }
        }
    }

    /**
     * 恢复预加载
     */
    override fun resumePreload() {
        TekiLog.d(TAG, "resumePreload loading=$loading")
        loading?.forEach {
            if (it.isPausedPreload) {
                it.isPausedPreload = false
                it.program.continueLoading()
            }
        }
    }

    /**
     * 根据内存地址获取[mediaItem]对应的 index，如果不存在返回-1
     */
    private fun getMediaItemPointerIndexOf(mediaItem: MediaItem): Int {
        return getMediaItemList().indexOfFirst { it === mediaItem }
    }
}