package com.lizhi.component.tekiplayer.engine

import androidx.annotation.IntDef
import com.lizhi.component.tekiplayer.audioprogram.extractor.ExtractorInput
import com.lizhi.component.tekiplayer.audioprogram.extractor.ParsableByteArray
import com.lizhi.component.tekiplayer.engine.DataQueue.DataLevel.Companion.EMPTY
import com.lizhi.component.tekiplayer.engine.DataQueue.DataLevel.Companion.HIGH
import com.lizhi.component.tekiplayer.engine.DataQueue.DataLevel.Companion.LOW
import java.io.IOException
import kotlin.annotation.AnnotationTarget.FIELD
import kotlin.annotation.AnnotationTarget.FUNCTION
import kotlin.annotation.AnnotationTarget.PROPERTY
import kotlin.annotation.AnnotationTarget.VALUE_PARAMETER
import kotlin.jvm.Throws

/**
 * 文件名：DataQueue
 * 作用：
 * 作者：luo<PERSON><PERSON><EMAIL>
 * 创建日期：2021/3/15
 */
interface DataQueue {

    fun seek(position: Long): Boolean

    fun getWrittenPosition(): Long

    fun sampleMetadata(presentationTimeUs: Long, frameSize: Int, position: Long)

    @Throws(IOException::class)
    fun readToQueue(
        reader: ExtractorInput,
        remaining: Int,
        presentationTimeUs: Long = 0
    ): Int

    fun readToQueue(data: ParsableByteArray, bytesToRead: Int, timeUs: Long): Int

    @Throws(IOException::class)
    fun blockDequeue(
        buffer: BufferHolder
    ): Int

    fun setEndFlag()

    fun clear()

    fun getBufferSize(): Long

    fun getBufferPositionUs(): Long

    @MustBeDocumented
    @kotlin.annotation.Retention(AnnotationRetention.SOURCE)
    @Target(VALUE_PARAMETER, FIELD, PROPERTY, FUNCTION)
    @IntDef(value = [EMPTY, LOW, HIGH])
    annotation class DataLevel {

        companion object {
            const val EMPTY = 0
            const val LOW = 1
            const val HIGH = 2
        }
    }

    /**
     * 数据缓存水位线
     */
    interface DataLevelWatcher {

        fun onLevelChange(
            @DataLevel level: Int,
            cacheSize: Long,
            reason: Int
        )
    }

    /**
     * 是否允许写入
     * @param allow true，则可以正常写入；false，写入线程会block
     */
    fun allowInput(allow: Boolean)

}