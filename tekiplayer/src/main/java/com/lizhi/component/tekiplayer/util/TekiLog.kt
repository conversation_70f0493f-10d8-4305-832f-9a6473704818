package com.lizhi.component.tekiplayer.util

import android.util.Log
import com.lizhi.component.basetool.common.Logger

/**
 * 文件名：TekiLog
 * 作用：日志工具类
 * 作者：l<PERSON><PERSON><PERSON><EMAIL>
 * 创建日期：2021/3/16
 */
object TekiLog {

    private const val TAG = "TekiPlayer"

    @JvmStatic
    fun d(
        tag: String,
        msg: String
    ) {
        Logger.out.log(Log.DEBUG, TAG, "$tag: $msg on Thread:${Thread.currentThread().name}")
    }

    @JvmStatic
    fun i(
        tag: String,
        msg: String
    ) {
        Logger.out.log(Log.INFO, TAG, "$tag: $msg on Thread:${Thread.currentThread().name}")
    }

    @JvmStatic
    fun w(
        tag: String,
        msg: String
    ) {
        Logger.out.log(Log.WARN, TAG, "$tag: $msg Thread:${Thread.currentThread().name}")
    }

    @JvmStatic
    fun e(
        tag: String,
        msg: String,
        exception: Throwable? = null
    ) {
        Logger.out.log(
            Log.ERROR,
            TAG,
            "$tag: $msg Thread:${Thread.currentThread().name}",
            exception
        )
    }

}

internal const val KB = 1024L
internal const val MB = 1024 * KB

internal val Int.KB: Long
    get() = this * 1024L

internal val Int.MB: Long
    get() = this * 1024L * 1024


fun Long.UNIT(): String {
    val kiloByte = 1024
    val megaByte = kiloByte * 1024
    val gigaByte = megaByte * 1024
    return when {
        this >= gigaByte -> {
            val gb = this.toDouble() / gigaByte
            String.format("%.2f GB", gb)
        }
        this >= megaByte -> {
            val mb = this.toDouble() / megaByte
            String.format("%.2f MB", mb)
        }
        this >= kiloByte -> {
            val kb = this.toDouble() / kiloByte
            String.format("%.2f KB", kb)
        }
        else -> {
            String.format("%d B", this)
        }
    }
}