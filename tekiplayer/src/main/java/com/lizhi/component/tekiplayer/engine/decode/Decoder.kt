package com.lizhi.component.tekiplayer.engine.decode

import android.media.MediaFormat
import com.lizhi.component.tekiplayer.engine.InputBufferHolder
import com.lizhi.component.tekiplayer.engine.OutputBufferHolder

interface Decoder<I: InputBufferHolder, O: OutputBufferHolder> {

    val name: String?

    var decoderCallback: DecoderCallback?

    fun start()

    fun dequeueInputBuffer(): I?

    fun queueInputBuffer(buffer: I)

    fun dequeueOutputBuffer(): O?

    fun releaseOutputBuffer(outputBuffer: O)

    fun flush()

    fun release()

}


interface DecoderCallback {

    fun onFormatOutput(format: MediaFormat)

    fun onDecodeError(e: Exception)

}

