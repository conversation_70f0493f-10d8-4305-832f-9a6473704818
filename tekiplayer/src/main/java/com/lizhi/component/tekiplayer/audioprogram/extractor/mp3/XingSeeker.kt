/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.lizhi.component.tekiplayer.audioprogram.extractor.mp3

import android.util.Log
import com.lizhi.component.tekiplayer.audioprogram.extractor.ParsableByteArray
import com.lizhi.component.tekiplayer.audioprogram.extractor.SeekPoint
import com.lizhi.component.tekiplayer.audioprogram.extractor.Seeker
import com.lizhi.component.tekiplayer.util.Util
import com.lizhi.component.tekiplayer.util.audio.MpegAudioUtil.Header
import kotlin.math.roundToLong

/** MP3 seeker that uses metadata from a Xing header.  */ /* package */
internal class XingSeeker private constructor(
    private val dataStartPosition: Long,
    private val xingFrameSize: Int,
    override val durationUs: Long,
    /** Data size, including the XING frame.  */
    private val dataSize: Long =  -1,
    /**
     * Entries are in the range [0, 255], but are stored as long integers for convenience. Null if the
     * table of contents was missing from the header, in which case seeking is not be supported.
     */
    private val tableOfContents: LongArray = LongArray(0)
) : Seeker {

    override val dataEndPosition: Long = if (dataSize == -1L) -1 else dataStartPosition + dataSize
    private val seekable = dataSize != -1L

    override fun getPositionAtTimeUs(timeUs: Long): SeekPoint {
        if (!seekable) {
            return SeekPoint(0, dataStartPosition + xingFrameSize)
        }
        var time = timeUs
        time = Util.constrainValue(time, 0, durationUs)
        val percent = time * 100.0 / durationUs
        val scaledPosition: Double
        scaledPosition = when {
            percent <= 0 -> {
                0.0
            }
            percent >= 100 -> {
                256.0
            }
            else -> {
                val prevTableIndex = percent.toInt()
                val tableOfContents: LongArray = tableOfContents
                val prevScaledPosition = tableOfContents[prevTableIndex].toDouble()
                val nextScaledPosition: Double =
                    if (prevTableIndex == 99) 256.0 else tableOfContents[prevTableIndex + 1].toDouble()
                // Linearly interpolate between the two scaled positions.
                val interpolateFraction = percent - prevTableIndex
                (prevScaledPosition + interpolateFraction * (nextScaledPosition - prevScaledPosition))
            }
        }
        var positionOffset = (scaledPosition / 256 * dataSize).roundToLong()
        // Ensure returned positions skip the frame containing the XING header.
        positionOffset = Util.constrainValue(
            positionOffset, xingFrameSize.toLong(), dataSize - 1
        )
        return SeekPoint(time, dataStartPosition + positionOffset)
    }

    override fun getTimeUs(position: Long): Long {
        val positionOffset = position - dataStartPosition
        if (!seekable || positionOffset <= xingFrameSize) {
            return 0L
        }
        val tableOfContents: LongArray = tableOfContents
        val scaledPosition = positionOffset * 256.0 / dataSize
        val prevTableIndex = Util.binarySearchFloor(
            tableOfContents, scaledPosition.toLong(), true, true
        )
        val prevTimeUs = getTimeUsForTableIndex(prevTableIndex)
        val prevScaledPosition = tableOfContents[prevTableIndex]
        val nextTimeUs = getTimeUsForTableIndex(prevTableIndex + 1)
        val nextScaledPosition = if (prevTableIndex == 99) 256 else tableOfContents[prevTableIndex + 1]
        // Linearly interpolate between the two table entries.
        val interpolateFraction =
            (if (prevScaledPosition == nextScaledPosition) 0 else (scaledPosition - prevScaledPosition) / (nextScaledPosition - prevScaledPosition)).toDouble()
        return prevTimeUs + Math.round(interpolateFraction * (nextTimeUs - prevTimeUs))
    }

    /**
     * Returns the time in microseconds for a given table index.
     *
     * @param tableIndex A table index in the range [0, 100].
     * @return The corresponding time in microseconds.
     */
    private fun getTimeUsForTableIndex(tableIndex: Int): Long {
        return durationUs * tableIndex / 100
    }

    companion object {
        private const val TAG = "XingSeeker"

        /**
         * Returns a [XingSeeker] for seeking in the stream, if required information is present.
         * Returns `null` if not. On returning, `frame`'s position is not specified so the
         * caller should reset it.
         *
         * @param inputLength The length of the stream in bytes, or -1 if unknown.
         * @param position The position of the start of this frame in the stream.
         * @param mpegAudioHeader The MPEG audio header associated with the frame.
         * @param frame The data in this audio frame, with its position set to immediately after the
         * 'Xing' or 'Info' tag.
         * @return A [XingSeeker] for seeking in the stream, or `null` if the required
         * information is not present.
         */
        fun create(
            inputLength: Long,
            position: Long,
            mpegAudioHeader: Header,
            frame: ParsableByteArray
        ): XingSeeker? {
            val samplesPerFrame = mpegAudioHeader.samplesPerFrame
            val sampleRate = mpegAudioHeader.sampleRate
            val flags = frame.readInt()
            var frameCount = 0
            if (flags and 0x01 != 0x01 || frame.readUnsignedIntToInt().also { frameCount = it } == 0) {
                // If the frame count is missing/invalid, the header can't be used to determine the duration.
                return null
            }
            val durationUs = Util.scaleLargeTimestamp(
                frameCount.toLong(), samplesPerFrame * Util.MICROS_PER_SECOND,
                sampleRate.toLong()
            )
            if (flags and 0x06 != 0x06) {
                // If the size in bytes or table of contents is missing, the stream is not seekable.
                return XingSeeker(position, mpegAudioHeader.frameSize, durationUs)
            }
            val dataSize = frame.readUnsignedInt()
            val tableOfContents = LongArray(100)
            for (i in 0..99) {
                tableOfContents[i] = frame.readUnsignedByte().toLong()
            }

            // TODO: Handle encoder delay and padding in 3 bytes offset by xingBase + 213 bytes:
            // delay = (frame.readUnsignedByte() << 4) + (frame.readUnsignedByte() >> 4);
            // padding = ((frame.readUnsignedByte() & 0x0F) << 8) + frame.readUnsignedByte();
            if (inputLength != -1L && inputLength != position + dataSize) {
                Log.w(
                    TAG, "XING data size mismatch: " + inputLength + ", " + (position + dataSize)
                )
            }
            return XingSeeker(
                position, mpegAudioHeader.frameSize, durationUs, dataSize, tableOfContents
            )
        }
    }

}