package com.lizhi.component.tekiplayer.datasource.impl

import android.content.Context
import android.net.Uri
import android.os.Bundle
import com.lizhi.component.tekiplayer.datasource.*
import com.lizhi.component.tekiplayer.datasource.DataReader.Companion.READ_END_OF_INPUT
import com.lizhi.component.tekiplayer.datasource.DataReader.Companion.READ_ERROR
import com.lizhi.component.tekiplayer.datasource.exception.HttpDataSourceException
import com.lizhi.component.tekiplayer.datasource.exception.IllegalContentTypeException
import com.lizhi.component.tekiplayer.datasource.exception.InvalidResponseCodeException
import com.lizhi.component.tekiplayer.datasource.exception.OutOfRangeException
import com.lizhi.component.tekiplayer.util.TekiLog
import java.io.EOFException
import java.io.IOException
import java.io.InputStream
import java.net.HttpURLConnection
import java.net.URL
import java.util.regex.Matcher
import java.util.regex.Pattern

/**
 * 文件名：DefaultHttpDataSource
 * 作用：默认HTTP请求数据源
 * 作者：huangtianhao
 * 创建日期：2021/2/22
 */
class DefaultHttpDataSource(
    context: Context,
    url: String,
    override var dataSourceCallback: DataSourceCallback?,
    override val strategy: DataSourceStrategy
) : BaseDataSource(context, url, dataSourceCallback, strategy) {

    class DefaultHttpDataSourceFactory : BaseDataSource.BaseFactory() {
        override fun create(uri: Uri, extraData: Bundle?): DataSource {
            return DefaultHttpDataSource(
                checkNotNull(context),
                this.url ?: uri.toString(),
                this.dataSourceCallback,
                this.strategy
            )
        }
    }

    companion object {
        private const val TAG = "DefaultHttpDataSource"
        private val CONTENT_RANGE_HEADER = Pattern.compile("^bytes (\\d+)-(\\d+)/(\\d+)$")
    }

    private var bytesRemaining = -1L
    private var connection: HttpURLConnection? = null
    private var inputStream: InputStream? = null

    override var contentLength: Long? = null

    override var responseHeaders: Map<String, List<String>>? = null
        private set

    override fun open(range: Range): Boolean {
        // 参数校验
        if (!checkArgs(range)) {
            return false
        }

        close()

        return try {
            internalOpen(range) >= 0
        } catch (e: Exception) {
            TekiLog.e(TAG, e.message ?: "", e)
            dataSourceCallback?.onErrorOccurred(e)
            false
        }
    }

    private fun internalOpen(range: Range): Long {
        try {
            connection = makeConnection(range)
        } catch (e: Exception) {
            throw HttpDataSourceException(cause = e)
        }

        connection?.run {
            val responseCode = try {
                responseCode
            } catch (e: Exception) {
                throw HttpDataSourceException(cause = e)
            }
            val responseMessage = try {
                responseMessage
            } catch (e: Exception) {
                throw HttpDataSourceException(cause = e)
            }

            val fields = headerFields.filter { it.key != null && it.value != null }
            responseHeaders = fields

            if (responseCode < 200 || responseCode > 299) {
                if (responseCode == 416) {
                    throw OutOfRangeException(originUrl, range)
                }
                throw InvalidResponseCodeException(
                    originUrl,
                    responseCode,
                    responseMessage
                )
            }

            val contentType = contentType
            if (strategy.rejectedContentType.find { contentType.contains(it) } != null) {
                throw IllegalContentTypeException(originUrl, contentType)
            }

            val contentLength = getContentLength(this)
            <EMAIL> = contentLength + range.start

            val length = if (range.end == null) {
                if (contentLength == -1L) {
                    -1L
                } else {
                    contentLength
                }
            } else {
                range.end - range.start
            }.also {
                bytesRemaining = it
            }

            TekiLog.d(TAG, "bytesRemaining = $length")

            try {
                if (hasAesInfo() && !isRealTimeDecrypt()) {
                    <EMAIL> = aesComponent!!.convertDecryptInputStream(aesKey!!, aesIV!!, inputStream, localEncryptCache, plainCacheLength, saveEncryptDataCallback)
                    bytesRemaining += aesComponent!!.getUnUseCacheCount()
                } else {
                    <EMAIL> = inputStream
                }
            } catch (e: IOException) {
                throw HttpDataSourceException(cause = e)
            }

            dataSourceCallback?.onReadyRead()

            return length
        }

        return LENGTH_UNSET
    }


    private fun makeConnection(range: Range): HttpURLConnection {
        val url = URL(originUrl)
        val connection = url.openConnection() as HttpURLConnection
        connection.run {
            setRequestProperty("Range", range.getRangeRequestStr())
            requestMethod = "GET"
            setRequestProperty("Connection", "Keep-Alive")
            connectTimeout = strategy.connectTimeout
            readTimeout = strategy.readTimeout
            setRequestProperty("User-agent", strategy.userAgent)

            strategy.extraRequestProperties.forEach {
                setRequestProperty(it.key, it.value)
            }

            connect()
        }
        return connection
    }

    override fun read(
        buffer: ByteArray,
        offset: Int,
        readLength: Int
    ): Int {
        return try {
            readInternal(buffer, offset, readLength)
        } catch (e: Exception) {
            TekiLog.e(TAG, e.message ?: "", e)
            dataSourceCallback?.onErrorOccurred(e)
            READ_ERROR
        }
    }

    override fun updateDataSourceCallback(dataSourceCallback: DataSourceCallback) {
        this.dataSourceCallback = dataSourceCallback
    }

    @Throws(IOException::class)
    private fun readInternal(buffer: ByteArray, offset: Int, readLength: Int): Int {
        if (readLength == 0) {
            return 0
        }
        if (bytesRemaining == -1L || inputStream == null) {
            throw HttpDataSourceException(message = "open connection failed")
        }
        if (bytesRemaining == 0L) {
            dataSourceCallback?.onEndEncountered()
            return READ_END_OF_INPUT
        }

        val minOf = minOf(bytesRemaining.toInt(), readLength)
        val read = inputStream?.read(buffer, offset, minOf)
            ?: throw HttpDataSourceException(message = "null input stream, offset=$offset, len=$minOf")

        if (read == -1) {
            if (hasAesInfo()) {
                bytesRemaining = 0
            } else {
                // 正常不该出现的EOF，可能是bytesRemaining计算错误
                dataSourceCallback?.onEndEncountered()
                throw EOFException()
            }
        } else {
            bytesRemaining -= read.toLong()
        }

//        TekiLog.i(TAG, "after read bytes remaining = $bytesRemaining")
        return read
    }

    private fun checkArgs(range: Range): Boolean {
        if (range.start < 0) {
            this.dataSourceCallback?.onErrorOccurred(
                HttpDataSourceException(message = "illegal range ${range.start} < 0")
            )
            return false
        }
        if (originUrl.isBlank()) {
            this.dataSourceCallback?.onErrorOccurred(
                HttpDataSourceException(message = "url is blank")
            )
            return false
        }
        return true
    }


    override fun close() {
        TekiLog.i(TAG, "close")
        try {
            inputStream?.close()
        } catch (ignore: Exception) {
        } finally {
            inputStream = null
            bytesRemaining = -1L
            try {
                connection?.disconnect()
            } catch (e: Exception) {
                TekiLog.e(TAG, e.message ?: "", e)
            }
            connection = null
        }
    }

    override fun getUrl(): String {
        return connection?.url.toString()
    }

    /**
     * 从HttpUrlConnection中获取ContentLength
     *
     * 系统API获取ContentLength需要24以上
     * 优先获取ContentLength，其次ContentRange
     */
    @Suppress("RECEIVER_NULLABILITY_MISMATCH_BASED_ON_JAVA_ANNOTATIONS")
    private fun getContentLength(connection: HttpURLConnection): Long {
        var contentLength: Long = -1L
        val contentLengthHeader = connection.getHeaderField("Content-Length")
        if (!contentLengthHeader.isNullOrBlank()) {
            try {
                contentLength = contentLengthHeader.toLong()
            } catch (e: NumberFormatException) {
                TekiLog.e(TAG, "Unexpected Content-Length [$contentLengthHeader]")
            }
        }
        val contentRangeHeader = connection.getHeaderField("Content-Range")
        if (!contentRangeHeader.isNullOrBlank()) {
            val matcher: Matcher =
                CONTENT_RANGE_HEADER.matcher(contentRangeHeader)
            if (matcher.find()) {
                try {
                    val contentLengthFromRange =
                        matcher.group(2).toLong() - matcher.group(1).toLong() + 1
                    if (contentLength < 0) {
                        // 一些代理服务器可能会移除Content-Length字段，此时使用Range
                        contentLength = contentLengthFromRange
                    } else if (contentLength != contentLengthFromRange) {
                        // 两个字段都有且不一的时候，假定较长为正确数值
                        TekiLog.w(
                            TAG,
                            "Inconsistent headers [$contentLengthHeader] [$contentRangeHeader]"
                        )
                        contentLength = maxOf(contentLength, contentLengthFromRange)
                    }
                } catch (e: NumberFormatException) {
                    TekiLog.e(TAG, "Unexpected Content-Range [$contentRangeHeader]")
                }
            }
        }
        return contentLength
    }

}