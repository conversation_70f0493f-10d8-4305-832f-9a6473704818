package com.lizhi.component.tekiplayer.datasource.cache

import com.lizhi.component.tekiplayer.controller.CacheController
import com.lizhi.component.tekiplayer.util.TekiLog
import java.io.File
import kotlin.math.abs

/**
 * 文件名：CacheEvictor
 * 作用：LRU缓存清理器
 * 作者：huangtianhao
 * 创建日期：2021/4/12
 */
class LruCacheEvictor(
    private val cacheStorage: CacheStorage,
    private val dir: File,
    // 最大缓存空间限制，当缓存文件大于该数值时，开始LRU删除
    private val maxCacheSize: Long,
    // 对磁盘剩余空间的判断，如果剩余空间小于该数值，删除所有缓存文件
    private val freeSpaceLimit: Long,
    // 忽略对最近一段时间访问的文件的删除（目前未启用，为0L）
    private val minTouchTime: Long = DEFAULT_MIN_TOUCH_TIME
) : CacheEvictor {

    companion object {
        private const val TAG = "LruCacheEvictor"
        private val lockedFileList = ArrayList<String>()

        private const val DEFAULT_MIN_TOUCH_TIME = 0L
        const val CACHE_PRIORITY = "highPriority"
        const val CACHE_PRIORITY_PATH = "TekiPlayerHighPriority"
    }

    /**
     * 是否第一次接触缓存，
     * 因cache可能会多次open/close，只在第一次更新访问时间
     * 增加该判断也能防止当前播放节目的访问时间新于预缓冲文件
     */
    private var isUpdatedTouchTime: Boolean = false

    /**
     * 获取磁盘剩余空间
     */
    private fun getDirAvailableSize(): Long {
        return dir.freeSpace
    }

    override fun onTouchNewCache(key: String, currentCacheInfo: CacheStorage.CacheInfo) {
        val fileName = currentCacheInfo.fileName

        TekiLog.d(TAG, "onTouchNewCache [key] $key [fileName] $fileName")

        // 将当前文件名加入锁定列表
        if (!lockedFileList.contains(fileName)) {
            lockedFileList.add(fileName)
        }

        if (!isUpdatedTouchTime) {
            // 初次访问时，更新访问时间
            isUpdatedTouchTime = true
            currentCacheInfo.lastTouchTimeStamp = System.currentTimeMillis()
            cacheStorage.save(key, currentCacheInfo)
        }
        val sumSize = sumDirSize()

        val availableSize = getDirAvailableSize()
        TekiLog.d(TAG, "getDirAvailableSize $availableSize")
        // 触发限制时，开始LRU删除
        if (sumSize >= maxCacheSize || availableSize <= freeSpaceLimit) {
            TekiLog.d(TAG, "[delete] currentSize:$sumSize maxCacheSize:$maxCacheSize availableSize:$availableSize " +
                    "freeSpaceLimit:$freeSpaceLimit dir:${dir.absolutePath}")
            deleteOldestCacheFile(sumSize, fileName, availableSize > freeSpaceLimit)
        }
    }

    override fun onLeaveCache(currentCacheInfo: CacheStorage.CacheInfo) {
        val fileName = currentCacheInfo.fileName
        TekiLog.d(TAG, "onLeaveCache fileName ${currentCacheInfo.fileName}")
        // 退出访问时，移除该文件锁定
        lockedFileList.remove(fileName)
    }

    @Synchronized
    private fun deleteOldestCacheFile(
        sumSize: Long,
        currentFileName: String,
        pauseOnSatisfyMaxCacheSize: Boolean
    ) {
        if (!CacheController.enableBuiltinPolicy) {
            TekiLog.w(TAG, "deleteOldestCacheFile fail, because enableBuiltinPolicy is false")
            return
        }

        var currentSize = sumSize
        val currentTime = System.currentTimeMillis()

        cacheStorage.getAll().sortedBy { it.lastTouchTimeStamp }.forEach {
            // 时间判断（目前未启用）
            if (abs(currentTime - it.lastTouchTimeStamp) <= minTouchTime) {
                return
            }

            // 忽略当前文件
            if (it.fileName != currentFileName) {
                // 删除至符合限制后，退出删除
                if (pauseOnSatisfyMaxCacheSize && currentSize < maxCacheSize) {
                    TekiLog.i(
                        TAG,
                        "stop delete file, already not exceed the limit, [current] $currentSize [max] $maxCacheSize"
                    )
                    return
                }

                // 文件锁定，忽略
                if (lockedFileList.contains(it.fileName)) {
                    TekiLog.i(TAG, "${it.fileName} locked, cancelled delete")
                    return
                }

                try {
                    File(dir, it.fileName).run {
                        if (exists()) {
                            // 删除文件
                            val fileSize = length()
                            TekiLog.i(TAG, "[delete] deleted file ${it.fileName} with ${this.length()}")
                            delete()
                            currentSize -= fileSize
                        }
                    }
                } catch (e: Exception) {
                    TekiLog.e(TAG, "failed on deleting file ${it.fileName}", e)
                }
            } else {
                TekiLog.i(TAG, "same file as current file, skipping delete")
            }
        }
    }


    /**
     * 计算缓存路径目前总占用空间
     */
    private fun sumDirSize(): Long {
        return dir.walkBottomUp().sumOf { it.length() }
    }
}