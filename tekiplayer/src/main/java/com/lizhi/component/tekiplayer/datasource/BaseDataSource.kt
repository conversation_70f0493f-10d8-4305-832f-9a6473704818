package com.lizhi.component.tekiplayer.datasource

import android.content.Context
import com.lizhi.component.tekiplayer.analyzer.Sampler
import com.lizhi.component.tekiplayer.util.TekiLog
import com.lizhi.component.tekiplayer.util.aes.AesComponent

/**
 * 文件名：BaseDataSource
 * 作用：
 * 作者：huangtianhao
 * 创建日期：2021/2/22
 */
abstract class BaseDataSource(
    protected val context: Context,
    protected open val originUrl: String,
    open var dataSourceCallback: DataSourceCallback?,
    protected open val strategy: DataSourceStrategy
) : DataSource {

    companion object {
        const val LENGTH_UNSET = -1L
        private const val TAG = "BaseDataSource"
    }

    var sampler: Sampler? = null

    var aesComponent: AesComponent? = null
    var aesKey: String? = null
    var aesIV: String? = null
    var isRealTimeDecryption: Boolean = false
    var localEncryptCache:ByteArray? = null // 本地缓存密文
    var plainCacheLength:Long = 0 // 明文缓存长度
    lateinit var saveEncryptDataCallback: (encryptData: ByteArray) -> Unit ? // 用于保存密文的回调

    init {
        TekiLog.i(TAG, "create data source with [url] $originUrl")
    }

    override var replacedCdnHost: String? = null
        set(value) {
            field = value
            TekiLog.d(TAG, "[url] $originUrl [replacedCdnHost] is $value")
        }

    abstract class BaseFactory : DataSource.Factory {

        protected var context: Context? = null

        protected var url: String? = null

        protected var dataSourceCallback: DataSourceCallback? = null

        var strategy: DataSourceStrategy = DataSourceStrategy.Builder()
            .build()
            private set

        fun setUrl(url: String) = apply {
            this.url = url
        }

        fun setDataSourceCallback(dataSourceCallback: DataSourceCallback?) = apply {
            this.dataSourceCallback = dataSourceCallback
        }

        fun setStrategy(strategy: DataSourceStrategy) = apply {
            this.strategy = strategy
        }

        fun setContext(context: Context) = apply {
            this.context = context
        }

        fun buildStrategy(block: DataSourceStrategy.Builder.() -> Unit): DataSourceStrategy {
            val builder = DataSourceStrategy.Builder()
            builder.block()
            this.strategy = builder.build()
            return this.strategy
        }

    }

    override fun setAesInfo(aesKey: String?, aesIV: String?, isRealTimeDecrypt: Boolean, mode:Int) {
        if (null != aesKey && null != aesIV) {
            this.aesKey = aesKey
            this.aesIV = aesIV
            this.isRealTimeDecryption = isRealTimeDecrypt
            aesComponent = AesComponent().apply {
                setCipher(aesKey, aesIV, mode)
            }
        }
    }

    override fun getAesInfo(): Pair<String?, String?> {
        return Pair(aesKey, aesIV)
    }

    override fun hasAesInfo(): Boolean {
        return null != aesComponent
    }

    override fun isRealTimeDecrypt(): Boolean {
        return isRealTimeDecryption
    }

    override fun setCacheInfo(encryptData: ByteArray, plainCacheLength:Long) {
        localEncryptCache = encryptData
        this.plainCacheLength = plainCacheLength
    }

    override fun setEncryptCallback(callback: (encryptData: ByteArray) -> Unit) {
        this.saveEncryptDataCallback = callback
    }
}