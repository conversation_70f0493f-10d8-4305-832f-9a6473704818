package com.lizhi.component.tekiplayer.controller.state

import androidx.annotation.IntDef
import kotlin.annotation.AnnotationTarget.*

/**
 * 文件名：PlayerEvent
 * 作用：播放器相关事件
 * 作者：h<PERSON><PERSON><PERSON>@lizhi.fm
 * 创建日期：2021/04/01
 */
class PlayerEvent {

    companion object {
        /**
         * 调用PREPARE接口
         */
        const val EVENT_PREPARE = 0

        /**
         * 调用播放
         */
        const val EVENT_PLAY = 1

        /**
         * 缓存足够播放
         */
        const val EVENT_BUFFERED_ENOUGH = 2

        /**
         * 缓存为空，暂停播放
         */
        const val EVENT_NEED_MORE_DATA = 3

        /**
         * 停止播放
         */
        const val EVENT_STOP = 4

        /**
         * 播放结束
         */
        const val EVENT_ENDED = 5

        /**
         * 错误
         */
        const val EVENT_AUDIO_ERROR = 6

        /**
         * 暂停
         */
        const val EVENT_PAUSE = 7

        /**
         * 恢复播放
         */
        const val EVENT_RESUME = 8

        /**
         * 因焦点丢失而暂停
         */
        const val EVENT_FOCUS_LOST_PAUSED = 9

        fun getEventText(@PlayerEvent.Event event: Int) =
            when (event) {
                EVENT_PREPARE -> "EVENT_PREPARE"
                EVENT_PLAY -> "EVENT_PLAY"
                EVENT_BUFFERED_ENOUGH -> "EVENT_BUFFERED_ENOUGH"
                EVENT_NEED_MORE_DATA -> "EVENT_NEED_MORE_DATA"
                EVENT_STOP -> "EVENT_STOP"
                EVENT_ENDED -> "EVENT_ENDED"
                EVENT_AUDIO_ERROR -> "EVENT_AUDIO_ERROR"
                EVENT_PAUSE -> "EVENT_PAUSE"
                EVENT_RESUME -> "EVENT_RESUME"
                EVENT_FOCUS_LOST_PAUSED -> "EVENT_FOCUS_LOST_PAUSED"
                else -> ""
            }
    }

    /**
     * 播放器状态. One of [EVENT_PREPARE], [EVENT_PLAY], [EVENT_BUFFERED_ENOUGH], [EVENT_NEED_MORE_DATA], [EVENT_STOP], [EVENT_ENDED], [EVENT_AUDIO_ERROR], [EVENT_PAUSE], [EVENT_RESUME], [EVENT_FOCUS_LOST_PAUSED].
     */
    @MustBeDocumented
    @kotlin.annotation.Retention(AnnotationRetention.SOURCE)
    @Target(VALUE_PARAMETER, FIELD, PROPERTY, FUNCTION)
    @IntDef(value = [EVENT_PREPARE, EVENT_PLAY, EVENT_BUFFERED_ENOUGH, EVENT_NEED_MORE_DATA, EVENT_STOP, EVENT_ENDED, EVENT_AUDIO_ERROR, EVENT_PAUSE, EVENT_RESUME, EVENT_FOCUS_LOST_PAUSED])
    annotation class Event

}