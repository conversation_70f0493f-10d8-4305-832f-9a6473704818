package com.lizhi.component.tekiplayer.engine.decode

import android.media.MediaFormat
import com.lizhi.component.tekiplayer.engine.InputBufferHolder
import com.lizhi.component.tekiplayer.engine.OutputBufferHolder
import com.lizhi.component.tekiplayer.engine.decode.codec.MediaCodecDecoder
import com.lizhi.component.tekiplayer.engine.decode.opus.OpusDecoder

class DecoderFactoryImpl : DecoderFactory {

    override fun createDecoder(format: MediaFormat): Decoder<InputBufferHolder, OutputBufferHolder>? {
        format.getString(MediaFormat.KEY_MIME)?.let {
            return when(it) {
                "audio/opus" -> OpusDecoder(format) as Decoder<InputBufferHolder, OutputBufferHolder>
                else -> MediaCodecDecoder(format) as Decoder<InputBufferHolder, OutputBufferHolder>
            }
        }
        return null
    }
}