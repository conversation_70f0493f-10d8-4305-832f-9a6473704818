package com.lizhi.component.tekiplayer.process

import android.app.Service
import android.content.Intent
import android.os.IBinder
import com.lizhi.component.basetool.common.NetStateWatcher
import com.lizhi.component.tekiplayer.IPlayer
import com.lizhi.component.tekiplayer.MediaItem
import com.lizhi.component.tekiplayer.PlayEventListener
import com.lizhi.component.tekiplayer.controller.dns.DnsResolver
import com.lizhi.component.tekiplayer.util.TekiLog
import com.tencent.mmkv.MMKV

/**
 * 文件名：PlayerServices
 * 作用：
 * 作者：<EMAIL>
 * 创建日期：2021/4/6
 */
abstract class PlayerService : Service(), PlayEventListener, DnsResolver {

    protected var player: IPlayer? = null

    override fun onCreate() {
        super.onCreate()
        MMKV.initialize(this)
        NetStateWatcher.registerNetWatcherInternal(this)
    }

    override fun onBind(intent: Intent?): IBinder? {
        TekiLog.i("PlayerService", "onBind")
        val playerStub = PlayerStub(this, this)
        player = playerStub
        playerStub.internalDnsResolver = this
        return playerStub
    }

    override fun onUnbind(intent: Intent?): Boolean {
        player = null
        return super.onUnbind(intent)
    }

}