/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.lizhi.component.tekiplayer.audioprogram.extractor

import android.net.Uri
import com.lizhi.component.tekiplayer.audioprogram.extractor.adts.AdtsExtractor
import com.lizhi.component.tekiplayer.audioprogram.extractor.mp3.Mp3Extractor
import com.lizhi.component.tekiplayer.audioprogram.extractor.mpeg4.M4aExtractor
import com.lizhi.component.tekiplayer.audioprogram.extractor.opus.OpusExtractor
import com.lizhi.component.tekiplayer.util.audio.FileTypes
import com.lizhi.component.tekiplayer.util.audio.FileTypes.Type
import java.util.ArrayList

/**
 * An [ExtractorsFactory] that provides an array of extractors for the following formats:
 *
 *
 *  * MP4, including M4A ([Mp4Extractor])
 *  * fMP4 ([FragmentedMp4Extractor])
 *  * Matroska and WebM ([MatroskaExtractor])
 *  * Ogg Vorbis/FLAC ([OggExtractor]
 *  * MP3 ([Mp3Extractor])
 *  * AAC ([AdtsExtractor])
 *  * MPEG TS ([TsExtractor])
 *  * MPEG PS ([PsExtractor])
 *  * FLV ([FlvExtractor])
 *  * WAV ([WavExtractor])
 *  * AC3 ([Ac3Extractor])
 *  * AC4 ([Ac4Extractor])
 *  * AMR ([AmrExtractor])
 *  * FLAC
 *
 *  * If available, the FLAC extension's `com.google.android.exoplayer2.ext.flac.FlacExtractor` is used.
 *  * Otherwise, the core [FlacExtractor] is used. Note that Android devices do not
 * generally include a FLAC decoder before API 27. This can be worked around by using
 * the FLAC extension or the FFmpeg extension.
 *
 *  * JPEG ([JpegExtractor])
 *
 */
class DefaultExtractorsFactory : ExtractorsFactory {
    private val constantBitrateSeekingEnabled = false

    @Synchronized
    override fun createExtractors(): Array<Extractor?> {
        return createExtractors(Uri.EMPTY, hashMapOf())
    }

    @Synchronized
    override fun createExtractors(
        uri: Uri,
        responseHeaders: Map<String, List<String?>?>?
    ): Array<Extractor?> {
        val extractors: MutableList<Extractor> = ArrayList( /* initialCapacity= */14)
        @Type
        val responseHeadersInferredFileType = FileTypes.inferFileTypeFromResponseHeaders(responseHeaders)
        if (responseHeadersInferredFileType != FileTypes.UNKNOWN) {
            addExtractorsForFileType(responseHeadersInferredFileType, extractors)
        }
        @Type
        val uriInferredFileType = FileTypes.inferFileTypeFromUri(uri)
        if (uriInferredFileType != FileTypes.UNKNOWN
            && uriInferredFileType != responseHeadersInferredFileType
        ) {
            addExtractorsForFileType(uriInferredFileType, extractors)
        }
        for (fileType in DEFAULT_EXTRACTOR_ORDER) {
            if (fileType != responseHeadersInferredFileType && fileType != uriInferredFileType) {
                addExtractorsForFileType(fileType, extractors)
            }
        }
        return extractors.toTypedArray()
    }

    private fun addExtractorsForFileType(
        @Type fileType: Int,
        extractors: MutableList<Extractor>
    ) {
        when (fileType) {
            FileTypes.MP3 -> extractors.add(Mp3Extractor())
            FileTypes.MP4 -> extractors.add(M4aExtractor())
            FileTypes.ADTS -> extractors.add(AdtsExtractor())
            FileTypes.OPUS -> extractors.add(OpusExtractor())
            else -> {
            }
        }
    }

    companion object {
        // Extractors order is optimized according to
        // https://docs.google.com/document/d/1w2mKaWMxfz2Ei8-LdxqbPs1VLe_oudB-eryXXw9OvQQ.
        // The JPEG extractor appears after audio/video extractors because we expect audio/video input to
        // be more common.
        private val DEFAULT_EXTRACTOR_ORDER = intArrayOf(
            FileTypes.FLV,
            FileTypes.FLAC,
            FileTypes.WAV,
            FileTypes.MP4,
            FileTypes.AMR,
            FileTypes.PS,
            FileTypes.OGG,
            FileTypes.OPUS,
            FileTypes.TS,
            FileTypes.MATROSKA,
            FileTypes.ADTS,
            FileTypes.AC3,
            FileTypes.AC4,
            FileTypes.MP3,
            FileTypes.JPEG
        )
    }
}