package com.lizhi.component.tekiplayer.controller.dns

import java.net.InetAddress

/**
 * 文件名：DnsResolver
 * 作用：DNS解析相关接口
 * 作者：huangtianhao
 * 创建日期：2021/4/29
 */
interface DnsResolver {
    // 域名解析
    fun lookup(hostname: String): List<InetAddress>

    // 反馈结果
    fun mark(
        domain: String,
        ip: String,
        connResult: Boolean,
        requestResult: Boolean,
        path: String?,
        connCost: Long,
        requestCost: Long
    )

    fun isEnableCustomDns(): <PERSON>olean
}