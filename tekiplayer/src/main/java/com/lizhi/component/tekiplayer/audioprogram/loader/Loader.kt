/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
@file:Suppress("unused")

package com.lizhi.component.tekiplayer.audioprogram.loader

import android.annotation.SuppressLint
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.os.SystemClock
import androidx.annotation.IntDef
import com.lizhi.component.tekiplayer.audioprogram.loader.Loader.Loadable
import com.lizhi.component.tekiplayer.util.TekiLog
import com.lizhi.component.tekiplayer.util.TekiLog.e
import java.io.IOException
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import kotlin.jvm.Throws
import kotlin.math.min

/**
 * Manages the background loading of [Loadable]s.
 */
class Loader(threadName: String) {
    /**
     * Thrown when an unexpected exception or error is encountered during loading.
     */
    class UnexpectedLoaderException(cause: Throwable) : IOException(
        "Unexpected " + cause.javaClass.simpleName + ": " + cause.message, cause
    )

    /**
     * An object that can be loaded using a [Loader].
     */
    interface Loadable {
        /**
         * Cancels the load.
         *
         *
         * Loadable implementations should ensure that a currently executing [load] call
         * will exit reasonably quickly after this method is called. The [load] call may exit
         * either by returning or by throwing an [IOException].
         *
         *
         * If there is a currently executing [load] call, then the thread on which that call
         * is being made will be interrupted immediately after the call to this method. Hence
         * implementations do not need to (and should not attempt to) interrupt the loading thread
         * themselves.
         *
         *
         * Although the loading thread will be interrupted, Loadable implementations should not use
         * the interrupted status of the loading thread in [load] to determine whether the load
         * has been canceled. This approach is not robust [Internal ref: b/79223737]. Instead,
         * implementations should use their own flag to signal cancelation (for example, using [ ]).
         */
        fun cancelLoad()

        /**
         * Performs the load, returning on completion or cancellation.
         *
         * @throws IOException If the input could not be loaded.
         */
        @Throws(IOException::class)
        fun load()
    }

    /**
     * A callback to be notified of [Loader] events.
     */
    interface Callback<T : Loadable?> {
        /**
         * Called when a load has completed.
         *
         *
         * Note: There is guaranteed to be a memory barrier between [Loadable.load] exiting
         * and this callback being called.
         *
         * @param loadable The loadable whose load has completed.
         * @param elapsedRealtimeMs [SystemClock.elapsedRealtime] when the load ended.
         * @param loadDurationMs The duration in milliseconds of the load since [.startLoading]
         * was called.
         */
        fun onLoadCompleted(
            loadable: T,
            elapsedRealtimeMs: Long,
            loadDurationMs: Long
        )

        /**
         * Called when a load has been canceled.
         *
         *
         * Note: If the [Loader] has not been released then there is guaranteed to be a memory
         * barrier between [Loadable.load] exiting and this callback being called. If the [ ] has been released then this callback may be called before [Loadable.load]
         * exits.
         *
         * @param loadable The loadable whose load has been canceled.
         * @param elapsedRealtimeMs [SystemClock.elapsedRealtime] when the load was canceled.
         * @param loadDurationMs The duration in milliseconds of the load since [.startLoading]
         * was called up to the point at which it was canceled.
         * @param released True if the load was canceled because the [Loader] was released. False
         * otherwise.
         */
        fun onLoadCanceled(
            loadable: T,
            elapsedRealtimeMs: Long,
            loadDurationMs: Long,
            released: Boolean
        )

        /**
         * Called when a load encounters an error.
         *
         *
         * Note: There is guaranteed to be a memory barrier between [Loadable.load] exiting
         * and this callback being called.
         *
         * @param loadable The loadable whose load has encountered an error.
         * @param elapsedRealtimeMs [SystemClock.elapsedRealtime] when the error occurred.
         * @param loadDurationMs The duration in milliseconds of the load since [.startLoading]
         * was called up to the point at which the error occurred.
         * @param error The load error.
         * @param errorCount The number of errors this load has encountered, including this one.
         * @return The desired error handling action. One of [Loader.RETRY], [     ][Loader.RETRY_RESET_ERROR_COUNT], [Loader.DONT_RETRY], [     ][Loader.DONT_RETRY_FATAL] or a retry action created by [.createRetryAction].
         */
        fun onLoadError(
            loadable: T,
            elapsedRealtimeMs: Long,
            loadDurationMs: Long,
            error: IOException?,
            errorCount: Int
        ): LoadErrorAction
    }

    /**
     * A callback to be notified when a [Loader] has finished being released.
     */
    interface ReleaseCallback {
        /**
         * Called when the [Loader] has finished being released.
         */
        fun onLoaderReleased()
    }

    /** Types of action that can be taken in response to a load error.  */
    @MustBeDocumented
    @kotlin.annotation.Retention(AnnotationRetention.SOURCE)
    @IntDef(
        ACTION_TYPE_RETRY,
        ACTION_TYPE_RETRY_AND_RESET_ERROR_COUNT,
        ACTION_TYPE_DONT_RETRY,
        ACTION_TYPE_DONT_RETRY_FATAL
    )
    private annotation class RetryActionType()

    /**
     * Action that can be taken in response to [Callback.onLoadError].
     */
    class LoadErrorAction(
        @field:RetryActionType @param:RetryActionType val type: Int,
        var retryDelayMillis: Long
    ) {

        /** Returns whether this is a retry action.  */
        val isRetry: Boolean
            get() = type == ACTION_TYPE_RETRY || type == ACTION_TYPE_RETRY_AND_RESET_ERROR_COUNT

    }

    private val downloadExecutorService: ExecutorService by lazy {
        Executors.newSingleThreadExecutor { runnable -> Thread(runnable, threadName) }
    }
    private var _currentTask: LoadTask<out Loadable>? = null

    val currentTask: LoadTask<out Loadable>?
        get() = _currentTask
    private var fatalError: IOException? = null

    /**
     * Whether the last call to [.startLoading] resulted in a fatal error. Calling [ ][.maybeThrowError] will throw the fatal error.
     */
    fun hasFatalError(): Boolean {
        return fatalError != null
    }

    /** Clears any stored fatal error.  */
    fun clearFatalError() {
        fatalError = null
    }

    /**
     * Starts loading a [Loadable].
     *
     *
     * The calling thread must be a [Looper] thread, which is the thread on which the [ ] will be called.
     *
     * @param <T> The type of the loadable.
     * @param loadable The [Loadable] to load.
     * @param callback A callback to be called when the load ends.
     * @param defaultMinRetryCount The minimum number of times the load must be retried before [     ][.maybeThrowError] will propagate an error.
     * @throws IllegalStateException If the calling thread does not have an associated [Looper].
     * @return [SystemClock.elapsedRealtime] when the load started.
    </T> */
    fun <T : Loadable> startLoading(
        loadable: T,
        callback: Callback<T>?,
        defaultMinRetryCount: Int
    ): Long {
        val looper = Looper.myLooper() ?: throw UnsupportedOperationException("当前线程没有lopper")
        fatalError = null
        val startTimeMs = SystemClock.elapsedRealtime()
        LoadTask(
            looper, loadable, callback, startTimeMs
        ).start(0)
        return startTimeMs
    }

    /** Returns whether the loader is currently loading.  */
    private var _loading: Boolean = false

    val isLoading: Boolean
        get() = _currentTask != null || _loading

    /**
     * Cancels the current load.
     *
     * @throws IllegalStateException If the loader is not currently loading.
     */
    fun cancelLoading() {
        _loading = false
        _currentTask?.cancel(false) ?: TekiLog.w("Loader", "cancelLoading null")
    }

    /**
     * Releases the loader. This method should be called when the loader is no longer required.
     *
     * @param callback An optional callback to be called on the loading thread once the loader has
     * been released.
     */
    @JvmOverloads
    fun release(callback: ReleaseCallback? = null) {
        if (_currentTask != null) {
            _currentTask?.cancel(true)
        }
        if (callback != null) {
            downloadExecutorService.execute(ReleaseTask(callback))
        }
        downloadExecutorService.shutdown()
    }

    // Internal classes.
    @SuppressLint("HandlerLeak")
    inner class LoadTask<T : Loadable>(
        looper: Looper,
        val loadable: T,
        private var callback: Loader.Callback<T>?,
        private val startTimeMs: Long
    ) : Handler(looper), Runnable {

        private val TAG = "LoadTask"
        private val MSG_START = 0
        private val MSG_FINISH = 1
        private val MSG_IO_EXCEPTION = 2
        private val MSG_FATAL_ERROR = 3

        private var currentError: IOException? = null
        private var errorCount = 0
        private var executorThread: Thread? = null
        private var canceled = false

        @Volatile
        private var released = false

        fun start(delayMillis: Long) {
            _currentTask = this
            if (delayMillis > 0) {
                sendEmptyMessageDelayed(
                    MSG_START, delayMillis
                )
            } else {
                execute()
            }
        }

        fun cancel(released: Boolean) {
            this.released = released
            _loading = false
            currentError = null
            if (hasMessages(MSG_START)) {
                // The task has not been given to the executor yet.
                canceled = true
                removeMessages(MSG_START)
                if (!released) {
                    sendEmptyMessage(MSG_FINISH)
                }
            } else {
                // The task has been given to the executor.
                synchronized(this) {
                    canceled = true
                    loadable.cancelLoad()
                    this.executorThread?.interrupt()
                }
            }
            if (released) {
                finish(this)
                val nowMs = SystemClock.elapsedRealtime()
                callback?.onLoadCanceled(loadable, nowMs, nowMs - startTimeMs, true)
                // If loading, this task will be referenced from a GC root (the loading thread) until
                // cancellation completes. The time taken for cancellation to complete depends on the
                // implementation of the Loadable that the task is loading. We null the callback reference
                // here so that it doesn't prevent garbage collection whilst cancellation is ongoing.
                callback = null
            }
        }

        override fun run() {
            try {
                var shouldLoad: Boolean
                synchronized(this) {
                    shouldLoad = !canceled
                    executorThread = Thread.currentThread()
                }
                if (shouldLoad) {
                    try {
                        loadable.load()
                    } finally {
                    }
                }
                synchronized(this) {
                    executorThread = null
                    // Clear the interrupted flag if set, to avoid it leaking into a subsequent task.
                    Thread.interrupted()
                }
                if (!released) {
                    sendEmptyMessage(MSG_FINISH)
                }
            } catch (e: IOException) {
                if (!released) {
                    obtainMessage(MSG_IO_EXCEPTION, e).sendToTarget()
                }
            } catch (e: Exception) {
                // This should never happen, but handle it anyway.
                e(
                    TAG,
                    "Unexpected exception loading stream", e
                )
                if (!released) {
                    obtainMessage(
                        MSG_IO_EXCEPTION,
                        UnexpectedLoaderException(e)
                    ).sendToTarget()
                }
            } catch (e: OutOfMemoryError) {
                // This can occur if a stream is malformed in a way that causes an extractor to think it
                // needs to allocate a large amount of memory. We don't want the process to die in this
                // case, but we do want the playback to fail.
                e(
                    TAG,
                    "OutOfMemory error loading stream", e
                )
                if (!released) {
                    obtainMessage(
                        MSG_IO_EXCEPTION,
                        UnexpectedLoaderException(e)
                    ).sendToTarget()
                }
            } catch (e: Error) {
                // We'd hope that the platform would kill the process if an Error is thrown here, but the
                // executor may catch the error (b/20616433). Throw it here, but also pass and throw it from
                // the handler thread so that the process dies even if the executor behaves in this way.
                e(
                    TAG,
                    "Unexpected error loading stream", e
                )
                if (!released) {
                    obtainMessage(
                        MSG_FATAL_ERROR, e
                    ).sendToTarget()
                }
                throw e
            }
        }

        override fun handleMessage(msg: Message) {
            if (released) {
                return
            }
            if (msg.what == MSG_START) {
                execute()
                return
            }
            if (msg.what == MSG_FATAL_ERROR) {
                throw (msg.obj as Error)
            }
            finish(this)
            val nowMs = SystemClock.elapsedRealtime()
            val durationMs = nowMs - startTimeMs
            if (canceled) {
                callback?.onLoadCanceled(loadable, nowMs, durationMs, false)
                return
            }
            when (msg.what) {
                MSG_FINISH -> try {
                    callback?.onLoadCompleted(loadable, nowMs, durationMs)
                } catch (e: RuntimeException) {
                    // This should never happen, but handle it anyway.
                    e(
                        TAG,
                        "Unexpected exception handling load completed", e
                    )
                    fatalError = UnexpectedLoaderException(e)
                }
                MSG_IO_EXCEPTION -> {
                    currentError = msg.obj as IOException
                    errorCount++
                    val action =
                        callback?.onLoadError(loadable, nowMs, durationMs, currentError, errorCount)
                            ?: return
                    if (action.type == ACTION_TYPE_DONT_RETRY_FATAL) {
                        fatalError = currentError
                    } else if (action.type != ACTION_TYPE_DONT_RETRY) {
                        if (action.type == ACTION_TYPE_RETRY_AND_RESET_ERROR_COUNT) {
                            errorCount = 1
                        }
                        start(
                            if (action.retryDelayMillis != -1L) action.retryDelayMillis else retryDelayMillis
                        )
                    }
                }
                else -> {
                }
            }
        }

        private fun execute() {
            currentError = null
            _loading = true
            try {
                downloadExecutorService.execute(_currentTask)
            } catch (ignore: Exception) {
                TekiLog.w(TAG, ignore.toString())
            }
        }

        private fun finish(loadTask: LoadTask<T>) {
//            TekiLog.w("Loader", "finish loading task=$loadTask currentTask=${currentTask?.loadable}")
            if (loadTask == _currentTask) {
                _currentTask = null
                _loading = false
            }
        }

        private val retryDelayMillis: Long
            get() = min((errorCount - 1) * 1000, 5000).toLong()

    }

    private class ReleaseTask(private val callback: ReleaseCallback) : Runnable {
        override fun run() {
            callback.onLoaderReleased()
        }

    }

    companion object {
        const val ACTION_TYPE_RETRY = 0
        const val ACTION_TYPE_RETRY_AND_RESET_ERROR_COUNT = 1
        const val ACTION_TYPE_DONT_RETRY = 2
        const val ACTION_TYPE_DONT_RETRY_FATAL = 3

        /** Retries the load using the default delay.  */
        val RETRY =
            createRetryAction( /* resetErrorCount= */
                false, -1
            )

        /** Retries the load using the default delay and resets the error count.  */
        val RETRY_RESET_ERROR_COUNT =
            createRetryAction( /* resetErrorCount= */
                true, -1
            )

        /** Discards the failed [Loadable] and ignores any errors that have occurred.  */
        val DONT_RETRY = LoadErrorAction(
            ACTION_TYPE_DONT_RETRY, -1
        )

        /**
         * Discards the failed [Loadable]. The next call to [.maybeThrowError] will throw
         * the last load error.
         */
        val DONT_RETRY_FATAL = LoadErrorAction(
            ACTION_TYPE_DONT_RETRY_FATAL, -1
        )

        /**
         * Creates a [LoadErrorAction] for retrying with the given parameters.
         *
         * @param resetErrorCount Whether the previous error count should be set to zero.
         * @param retryDelayMillis The number of milliseconds to wait before retrying.
         * @return A [LoadErrorAction] for retrying with the given parameters.
         */
        fun createRetryAction(
            resetErrorCount: Boolean,
            retryDelayMillis: Long
        ): LoadErrorAction {
            return LoadErrorAction(
                if (resetErrorCount) ACTION_TYPE_RETRY_AND_RESET_ERROR_COUNT else ACTION_TYPE_RETRY,
                retryDelayMillis
            )
        }
    }

}