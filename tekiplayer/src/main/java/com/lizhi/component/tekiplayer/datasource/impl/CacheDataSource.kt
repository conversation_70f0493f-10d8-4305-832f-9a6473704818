package com.lizhi.component.tekiplayer.datasource.impl

import android.content.Context
import android.net.Uri
import android.os.Bundle
import com.lizhi.component.tekiplayer.datasource.BaseDataSource
import com.lizhi.component.tekiplayer.datasource.CacheableDataSource
import com.lizhi.component.tekiplayer.datasource.DataReader.Companion.READ_END_OF_INPUT
import com.lizhi.component.tekiplayer.datasource.DataReader.Companion.READ_ERROR
import com.lizhi.component.tekiplayer.datasource.DataSource
import com.lizhi.component.tekiplayer.datasource.DataSourceCallback
import com.lizhi.component.tekiplayer.datasource.DataSourceStrategy
import com.lizhi.component.tekiplayer.datasource.Range
import com.lizhi.component.tekiplayer.datasource.cache.Cache
import com.lizhi.component.tekiplayer.util.TekiLog

/**
 * 文件名：CacheDataSource
 * 作用：带有缓存功能的数据源
 *
 * [dataSourceCallback] 数据源回调
 * [strategy] 数据源策略参数的定义类
 * [upstreamDataSource] 上游数据源，当没有缓存时，从该数据源获取数据并写入缓存
 * [cache] 缓存功能的实际实现类
 *
 * 作者：huangtianhao
 * 创建日期：2021/3/18
 */
class CacheDataSource(
    context: Context,
    url: String,
    override var dataSourceCallback: DataSourceCallback?,
    override val strategy: DataSourceStrategy,
    private val upstreamDataSource: DataSource,
    private val cache: Cache?,
) : BaseDataSource(context, url, dataSourceCallback, strategy), CacheableDataSource {

    class CacheDataSourceFactory : BaseDataSource.BaseFactory() {

        private var upstreamDataSource: DataSource? = null

        private var cache: Cache? = null

        fun setCache(cache: Cache) = apply {
            this.cache = cache
        }

        override fun create(uri: Uri, extraData: Bundle?): DataSource {
            return CacheDataSource(
                checkNotNull(this.context),
                this.url ?: uri.toString(),
                this.dataSourceCallback,
                this.strategy,
                upstreamDataSource ?: defaultHttpDataSource(uri),
                cache
            )
        }

        private fun defaultHttpDataSource(uri: Uri): DefaultHttpDataSource {
            return DefaultHttpDataSource(
                checkNotNull(this.context),
                this.url ?: uri.toString(),
                this.dataSourceCallback,
                this.strategy
            )
        }

        fun create(buildBlock: CacheDataSourceFactory.() -> Unit): DataSource {
            this.buildBlock()
            return this.create(Uri.parse(url))
        }
    }

    companion object {
        private const val TAG = "CacheDataSource"
    }

    var rethrowException: Boolean = false

    override var contentLength: Long? = null
        get() {
            val cacheContentLength = cache?.getContentLength()
            return if (cacheContentLength != null && cacheContentLength > 0L) {
                cacheContentLength
            } else {
                upstreamDataSource.contentLength
            }
        }

    override var responseHeaders: Map<String, List<String>>? = null
        get() = upstreamDataSource.responseHeaders
        private set

    private var startPos: Long = 0L
    private var bytesRead: Long = 0L
    private var endPos: Long? = null
    private var needToOpenNextUpstreamDataSource = false

    override fun open(range: Range): Boolean {
        //val upstreamResult = upstreamDataSource.open(range)
        startPos = range.start
        endPos = range.end
        bytesRead = 0L
        cache?.open(originUrl, range)
        // 调用open方法后，强制下一次从dataSource读取都需要重新open
        needToOpenNextUpstreamDataSource = true
        if (cache?.getNextRangeOfPos(range.start) == null) {
            tryOpenNextUpstreamDataSource()
        }
        // needToOpenNextUpstreamDataSource = false
        return true
    }


    override fun read(
        buffer: ByteArray,
        offset: Int,
        readLength: Int
    ): Int {
        return try {
            if (cache == null || !cache.isOpened) {
                return readFromUpstreamAndWrite(null, buffer, offset, readLength)
            }

            val position = startPos + bytesRead
            // 获取已经缓存的下一个Range
            val nextRange = cache.getNextRangeOfPos(position)

//             TekiLog.d(TAG, "startPos=${startPos}, bytesRead=${bytesRead} nextRange =${nextRange} url:$originUrl")

            val contentLength = contentLength
            val realRead = when {
                nextRange != null && position >= nextRange.end!! && contentLength != null && position >= contentLength -> {
                    dataSourceCallback?.onEndEncountered()
                    READ_END_OF_INPUT
                }
                nextRange == null || position == nextRange.end!! -> {
                    // 后续没有缓存过的，整块缓存
//                    TekiLog.d(
//                        TAG,
//                        "Read from upstream and write to cache totally nextRange=${nextRange} pos=$position"
//                    )
                    readFromUpstreamAndWrite(cache, buffer, offset, readLength)
                }
                position in nextRange.start until nextRange.end -> {
                    // 当前位置在一段缓存中
//                    TekiLog.d(TAG, "Read from cache nextRange=${nextRange} pos=$position")
                    needToOpenNextUpstreamDataSource = true
                    // 停止网络采样
                    sampler?.endSample()

                    val cacheRead = try {
                        cache.read(
                            buffer,
                            offset,
                            minOf((nextRange.end - position).toInt(), readLength)
                        )
                    } catch (e: Exception) {
                        TekiLog.e(TAG, "read from cache error", e)
                        -1
                    }

                    if (cacheRead == -1) {
                        TekiLog.e(
                            TAG,
                            "read from cache -1 pos = $position length = ${
                                minOf(
                                    (nextRange.end - position).toInt(),
                                    readLength
                                )
                            } "
                        )
                        // 按缓存信息来说，这一段应该有缓存信息，但实际上读取失败，应该再从上游读取进行兜底
                        readFromUpstreamAndWrite(
                            cache,
                            buffer,
                            offset,
                            minOf((nextRange.end - position).toInt(), readLength)
                        )
                    } else {
                        cacheRead
                    }
                }
                position < nextRange.start -> {
                    // 当前位置不在缓存中，但后面有个缓存片段，先直接尽量用上游读取
//                    TekiLog.d(
//                        TAG,
//                        "The whole part is not in cache, but there is a cache after, read from cache first offset=$offset length=${(nextRange.start - position).toInt()}"
//                    )
                    readFromUpstreamAndWrite(
                        cache,
                        buffer,
                        offset,
                        minOf(readLength, (nextRange.start - position).toInt())
                    )
                }
                else -> throw IllegalStateException("Unexpected situation pos = ${position}, nextRange = $nextRange")
            }
//            TekiLog.i(TAG, "read bytes $realRead")
            if (realRead > 0) {
                bytesRead += realRead
            }
            realRead
        } catch (e: Exception) {
            TekiLog.e(TAG, e.message ?: "", e)
            dataSourceCallback?.onErrorOccurred(e)
            READ_ERROR
        }
    }

    override fun updateDataSourceCallback(dataSourceCallback: DataSourceCallback) {
        this.dataSourceCallback = dataSourceCallback
        upstreamDataSource.updateDataSourceCallback(dataSourceCallback)
    }

    /**
     * 从上游DataSource读取数据并写入缓存
     */
    private fun readFromUpstreamAndWrite(
        cache: Cache?,
        buffer: ByteArray,
        offset: Int,
        readLength: Int
    ): Int {
        tryOpenNextUpstreamDataSource()
        val read = upstreamDataSource.read(buffer, offset, readLength)
        sampler?.sampleData(0, read.toLong())

        if (read > 0) {
            try {
                cache?.write(buffer, offset, read)
            } catch (e: Exception) {
                TekiLog.e(TAG, "error when write to cache file", e)
                if (rethrowException) {
                    throw e
                }
            }
        } else if (read == -1 && hasAesInfo() && !isRealTimeDecrypt()) {
            cache?.let {
                it.getPlainCacheTotalLength().also { len ->
                    upstreamDataSource?.contentLength = len
                    with(it) {
                        setContentLength(len)
                        deleteEncryptData()
                        TekiLog.w(TAG, "readFromUpstreamAndWrite setContentLength $len and deleteEncryptData")
                    }
                }
            }
        } else {
            TekiLog.w(TAG, "readFromUpstreamAndWrite -1")
        }
        return read
    }

    private fun tryOpenNextUpstreamDataSource() {
        if (needToOpenNextUpstreamDataSource) {
            sampler?.startSample()
            with(upstreamDataSource){
                setAesInfo(aesKey, aesIV, isRealTimeDecryption)
                setEncryptCallback { ba ->
                    cache?.saveEncryptData(ba)
                }
            }
            val position = startPos + bytesRead
            var start = startPos + bytesRead
            cache?.let {
                it.setAesInfo(aesKey, aesIV, isRealTimeDecryption)
                if (hasAesInfo() && !isRealTimeDecrypt() && position > 0) { // 填充解密上文
                    it.obtainEncryptCache()?.let { cache ->
                        upstreamDataSource.setCacheInfo(cache, it.getPlainCacheLength())
                        start = cache.size.toLong()
                    }
                }
            }
            val dotIndex = originUrl.lastIndexOf("/")
            TekiLog.w(TAG, "tryOpenNextUpstreamDataSource start:$start url:${originUrl.substring(dotIndex + 1)}")
            val openResult = upstreamDataSource.open(Range(start, endPos))
            if (openResult) {
                contentLength?.let {
                    if (it > 0L) {
                        cache?.setContentLength(it)
                        TekiLog.i(TAG, "tryOpenNextUpstreamDataSource setContentLength $it")
                    }
                }
                needToOpenNextUpstreamDataSource = false
            }
        }
    }

    override fun close() {
        upstreamDataSource.close()
        cache?.close()
    }

    override fun getUrl(): String {
        return upstreamDataSource.getUrl()
    }

    override fun deleteCache(): Boolean {
        return cache?.deleteCache() ?: false
    }

    override fun releaseCache() {
        cache?.releaseCache()
    }

    override fun hasCacheOnPosition(position: Long): Boolean {
        val nextRangeOfPos = cache?.getNextRangeOfPos(position) ?: return false
        val end = nextRangeOfPos.end
        return nextRangeOfPos.start <= position && (end == null || position <= end)
    }

}