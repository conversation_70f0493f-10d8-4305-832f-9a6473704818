package com.lizhi.component.tekiplayer.datasource.exception

import com.lizhi.component.tekiplayer.ERR_DATASOURCE_ILLEGAL_CONTENT_TYPE

/**
 * 文件名：IllegalResponseCodeException
 * 作用：非正常响应类型
 * 作者：huangtianhao
 * 创建日期：2021/3/17
 */
class IllegalContentTypeException(
    val url: String,
    contentType: String
) : HttpDataSourceException(code = ERR_DATASOURCE_ILLEGAL_CONTENT_TYPE, message = "Connecting $url response content-type is $contentType") {

}