package com.lizhi.component.tekiplayer.engine.decode.codec

import android.media.MediaCodec
import com.lizhi.component.tekiplayer.engine.OutputBufferHolder
import java.nio.ByteBuffer

class MediaCodecOutputBufferHolder : OutputBufferHolder() {

    var info: MediaCodec.BufferInfo = MediaCodec.BufferInfo()
    var index: Int = -1
    private var data: ByteBuffer? = null

    override fun setData(data: ByteArray, offset: Int, size: Int) {
        this.data?.put(data, offset, size)
    }

    override fun setData(data: ByteBuffer) {
        this.data = data
    }

    override fun getDataByteBuffer(): ByteBuffer? {
        return data
    }

    override fun clearData() {
        data?.clear()
        data = null
    }
}
