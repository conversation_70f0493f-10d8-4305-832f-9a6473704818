package com.lizhi.component.tekiplayer.engine

import android.media.MediaCodec
import android.media.MediaFormat
import android.os.Handler
import android.os.Message
import android.os.Process
import android.os.SystemClock
import com.lizhi.audiocore.CipherCallback
import com.lizhi.component.tekiplayer.audioprogram.extractor.Extractor
import com.lizhi.component.tekiplayer.configuration.BufferSizePolicy
import com.lizhi.component.tekiplayer.configuration.NetType.TYPE_WIFI
import com.lizhi.component.tekiplayer.engine.Engine.Callback
import com.lizhi.component.tekiplayer.engine.decode.Decoder
import com.lizhi.component.tekiplayer.engine.decode.DecoderCallback
import com.lizhi.component.tekiplayer.engine.decode.DecoderFactoryImpl
import com.lizhi.component.tekiplayer.engine.decode.opus.OpusDecoder
import com.lizhi.component.tekiplayer.engine.exception.EngineException
import com.lizhi.component.tekiplayer.engine.exception.RecoverableEngineException
import com.lizhi.component.tekiplayer.engine.exception.UnSupportFormatException
import com.lizhi.component.tekiplayer.util.DefaultHandlerWrapper
import com.lizhi.component.tekiplayer.util.TekiLog
import com.lizhi.component.tekiplayer.util.aes.AesComponent
import java.nio.ByteBuffer
import java.util.concurrent.atomic.AtomicBoolean

class MediaAudioEngine(
    private val extractor: Extractor,
    private val bufferPolicy: BufferSizePolicy,
    private val threadName: String,
    private var volume: Float,
    private var speed: Float
) : Engine, DecoderCallback, CipherCallback {

    companion object {
        const val TAG = "MediaAudioEngine"
        const val MSG_INPUT_BUFFER = 1
        const val MSG_OUTPUT_BUFFER = 2
        const val MSG_FLUSH = 3
        const val MSG_RESUME = 4
        const val MSG_RELEASE = 5

        /**
         * 解码一帧耗时超过此值，认为解码器状态异常，需要重新创建
         */
        const val RETRY_INTERVAL = 300
    }

    override var decoderName: String? = null

    private var seekPosition: Long = 0L

    private val lock = Any()
    private var format: MediaFormat? = null
    private var audioSink: AudioSink? = null

    private var lastEnqueueFullTime: Long? = null
    // 自动纠错次数，超过3次后抛出错误
    private var correctionTime = 0
    private var endOfStream = AtomicBoolean(false)
    private var hasOutputFormat = false
    private var decoder: Decoder<InputBufferHolder, OutputBufferHolder>? = null

    private var aesKey: String? = null
    private var aesIV: String? = null

    private val queueBufferHandler = DefaultHandlerWrapper("TekiPlayer:Engine:$threadName", Process.THREAD_PRIORITY_AUDIO) {

        synchronized(lock) {
            when (it.what) {
                MSG_INPUT_BUFFER -> {
                    if (enqueueBufferIntoCodec() && started.get()) {
                        sendQueueMessage(MSG_INPUT_BUFFER)
                    } else if (started.get()) {
                        if (lastEnqueueFullTime == null) {
                            lastEnqueueFullTime = SystemClock.elapsedRealtime()
                        }
                        sendQueueMessage(MSG_OUTPUT_BUFFER, delayed = 10)
                    }
                }
                MSG_OUTPUT_BUFFER -> {
                    if (handleOutBufferFromCodec() && started.get()) {
                        lastEnqueueFullTime = null
                        sendQueueMessage(MSG_OUTPUT_BUFFER)
                    } else if (started.get() && !release.get()) {
                        val lastEnqueueBufferTime = lastEnqueueFullTime
                        if (lastEnqueueBufferTime == null) {
                            sendQueueMessage(MSG_INPUT_BUFFER, delayed = 20)
                            return@DefaultHandlerWrapper
                        }
                        val noSuccessDecodedBufferDuration = SystemClock.elapsedRealtime() - lastEnqueueBufferTime
                        // 有时候 10ms 的间隔不一定有已成功解码的 buffer 返回，可以适当再等待
                        if (noSuccessDecodedBufferDuration > RETRY_INTERVAL) {
                            // 如果长时间处于没有buffer可以播放的情况，可以认为当前codec已经坏了，重新创建
                            lastEnqueueFullTime = null
                            correctionTime++
                            if (correctionTime > 3) {
                                callbackHandler?.post {
                                    callback?.onPlaybackException(
                                        EngineException(message = "引擎解析失败，重试次数以达上限"),
                                        false
                                    )
                                }
                                return@DefaultHandlerWrapper
                            } else {
                                onError(
                                    decoder!!,
                                    IllegalStateException("decode frame cost too much time, restart codec"),
                                    false
                                )
                            }
                        }
                        sendQueueMessage(MSG_INPUT_BUFFER, delayed = 20)
                    } else {
                        TekiLog.w(TAG, "get output buffer failed on not start, ignore")
                    }
                }
                MSG_FLUSH -> {
                    lastEnqueueFullTime = null
                    try {
                        decoder?.flush()
                    } catch (ignoer: Exception) {
                        TekiLog.e(TAG, "flush", ignoer)
                    }
                    TekiLog.i(TAG, "flush on engine queue")
                    removeMessage(MSG_OUTPUT_BUFFER)
                    removeMessage(MSG_INPUT_BUFFER)
                }
                MSG_RESUME -> {
                    lastEnqueueFullTime = null
                    TekiLog.i(TAG, "resume on engine queue")
                    removeMessage(MSG_FLUSH)
                    sendQueueMessage(MSG_OUTPUT_BUFFER)
                }
                MSG_RELEASE -> {
                    TekiLog.i(TAG, "release on engine queue")
                    try {
                        decoder?.release()
                    } catch (ignoer: Exception) {
                        TekiLog.e(TAG, "release", ignoer)
                    }
                    removeMessage(MSG_OUTPUT_BUFFER)
                    removeMessage(MSG_INPUT_BUFFER)
                    stopEngineQueue()
                }
            }
        }
    }

    private var callback: Callback? = null
    private var callbackHandler: Handler? = null

    private val started = AtomicBoolean(false)
    private val release = AtomicBoolean(false)
    private var firstFrameRendered = false

    var configurated = false
        private set

    override fun isPlaying(): Boolean {
        return started.get()
    }

    override fun start(restart: Boolean) {
        TekiLog.i(TAG, "program: $threadName MediaCodecEngine start")
        try {
            format ?: throw EngineException(message = "未配置format")
            decoder?.name ?: throw UnSupportFormatException("不支持该格式")

            if (!configurated) {
                decoder?.start()
                // 防止重建audioSink时播放进度丢失
                val currentPositionUs = audioSink?.getCurrentPositionUs()
                seekPosition += (currentPositionUs ?: 0)
                //
                audioSink?.release()
                audioSink = DefaultAudioSink {zero->
                    // buffer 消耗完成的回调
                    // 当 buffer 消耗完成，且流处于结束状态，认为当前歌曲已播放完毕
                    if (endOfStream.get() && !started.get()) {
                        TekiLog.d(TAG, "bufferEndListener endOfStream")
                        callbackHandler?.post {
                            callback?.onPlaybackEnd(zero)
                        }
                    }
                }
                configurated = true
            }
            // 如果是codec重启，不需要更改当前的运行状态
            // 2023-03-16 去掉 restart 逻辑，由 decoder 内部处理
            if (!restart) {
                started.set(true)
                sendQueueMessage(MSG_INPUT_BUFFER)
                callbackHandler?.post {
                    callback?.onPlaybackPlayed()
                }
            }
        } catch (e: Exception) {
            callbackHandler?.post {
                callback?.onPlaybackException(e, false)
            }
        }
    }

    override fun resume() {
        if (!configurated) {
            start()
            return
        }

        if (!started.get()) {
//            endOfStream.set(false)
            started.set(true)
            try {
                audioSink?.play()
                sendQueueMessage(MSG_RESUME)
                callback?.onPlaybackResumed()
            } catch (e: IllegalStateException) {
                e.printStackTrace()
            }
        }
    }

    override fun flush() {
        started.set(false)
        endOfStream.set(false)
        try {
            if (!configurated) {
                return
            }
            audioSink?.flush()
            queueBufferHandler.removeMessages()
            sendQueueMessage(MSG_FLUSH, atFront = true)
            callback?.onPlaybackFlush()
        } catch (e: IllegalStateException) {
            e.printStackTrace()
        }
    }

    override fun stop() {
        started.set(false)
        release.set(true)

        try {
            TekiLog.i(TAG, "stop on MediaCodec Engine")
            // 要 AudioTrack 先释放，再释放 codec，否则会有底层崩溃
            audioSink?.release()
            sendQueueMessage(MSG_RELEASE)

            callback?.onPlaybackStopped()
            callback = null
        } catch (ignore: Exception) {
        }
    }

    override fun setVolume(volume: Float) {
        this.volume = volume
        audioSink?.setVolume(volume)
    }

    override fun pause() {
        if (started.get()) {
            started.set(false)
//            TekiLog.d(TAG, "pause")
            try {
                audioSink?.pause()
                callback?.onPlaybackPaused()
            } catch (ignore: IllegalStateException) {
            }
        }
    }

    override fun configFormat(format: MediaFormat) {
        TekiLog.i(TAG, "configFormat, format=$format")
        this.format = format
        decoder = DecoderFactoryImpl().createDecoder(format)
        if (decoder?.name.isNullOrEmpty()) {
            callbackHandler?.post {
                callback?.onPlaybackException(
                    UnSupportFormatException("Unsupported format for session=$threadName"),
                    false
                )
            }
            return
        }
        decoder?.decoderCallback = this
        TekiLog.i(TAG, "find decoder name=${decoder?.name}")
    }

    override fun getPositionUs(): Long {
        return seekPosition + (audioSink?.getCurrentPositionUs() ?: 0)
    }

    private fun stopEngineQueue() {
        queueBufferHandler.quitSafely()
    }

    private fun enqueueBufferIntoCodec(): Boolean {
        try {
            if (!started.get()) {
                return false
            }

            val inputBuffer = decoder?.dequeueInputBuffer() ?: return false
            // 读取数据到 inputBuffer
            val readSampleData = extractor.readSample(inputBuffer)
            val result: Boolean

            val flag = when (readSampleData) {
                -1 -> {
                    TekiLog.i(
                        TAG,
                        "onInputBufferAvailable, 文件读到结尾, currPosition=${getPositionUs()}, duration=${extractor.durationUs}"
                    )
                    result = false
                    MediaCodec.BUFFER_FLAG_END_OF_STREAM
                }
                0 -> {
                    if (endOfStream.get()) {
                        TekiLog.w(
                            TAG,
                            "no more buffer on last buffer already enqueue"
                        )
                        return false
                    }
                    callbackHandler?.post {
                        callback?.onNeedMoreData()
                    }
                    TekiLog.i(TAG, "read empty data, return and wait")
                    return false
                }
                else -> {
                    val bufferSize = extractor.getDataQueue()?.getBufferSize() ?: -1
                    if (bufferSize < bufferPolicy.shouldLoadingThresholds(TYPE_WIFI)) {
                        callbackHandler?.post {
                            callback?.onShouldContinueFeedData()
                        }
                    }
                    result = readSampleData > 0
                    0
                }
            }
            if (flag == MediaCodec.BUFFER_FLAG_END_OF_STREAM) {
                endOfStream.set(true)
//                codec?.queueInputBuffer(index, 0, 0, 0, MediaCodec.BUFFER_FLAG_END_OF_STREAM)
                inputBuffer.isEndOfStream = true
                decoder?.queueInputBuffer(inputBuffer)
            } else {
//                TekiLog.w(
//                    TAG,
//                    "enqueueBufferIntoCodec buffer index=$index position=${buffer.position} time=${buffer.timePositionUs}"
//                )
                inputBuffer.isEndOfStream = false
//                codec?.queueInputBuffer(index, 0, readSampleData, 0, flag)
                decoder?.queueInputBuffer(inputBuffer)
            }
            return result
        } catch (e: Exception) {
            // callback
            TekiLog.e(TAG, "", e)
            onError(decoder!!, e)
        }
        return false
    }

    private fun handleOutBufferFromCodec(): Boolean {
        try {
            if (!started.get()) {
                return false
            }
//            val index: Int = codec?.dequeueOutputBuffer(bufferInfo, 0) ?: -1
            val outputBuffer = decoder?.dequeueOutputBuffer() ?: return false
            if (!hasOutputFormat) {
                TekiLog.w(TAG, "has not output format from codec!!")
                return false
            }
            // 结束播放
            if (outputBuffer.isEndOfStream) {
                TekiLog.w(TAG, "handleOutBufferFromCodec 接受结束 flag，结束播放")
                started.set(false)
                audioSink?.playToEnd(outputBuffer.zeroDataFlag)
//                codec?.releaseOutputBuffer(index, false)
                decoder?.releaseOutputBuffer(outputBuffer)
                return false
            }
            // 无效数据
            val byteBuffer = outputBuffer.getDataByteBuffer()
            if (byteBuffer == null) {
                TekiLog.w(TAG, "handleOutBufferFromCodec receive invalid buffer, skip")
                return true
            }
            // 首帧
            if (!firstFrameRendered) {
                firstFrameRendered = true
                callbackHandler?.post {
                    callback?.onPlaybackFirstFrameRender()
                }
            }

            // 播放buffer
            audioSink?.handleBuffer(byteBuffer, 0)
            // 释放缓存
            try {
                decoder?.releaseOutputBuffer(outputBuffer)
            } catch (ignore: Exception) {
                TekiLog.w(TAG, ignore.message ?: "exception on releaseOutputBuffer")
            }
            return true
        } catch (e: Exception) {
            // callback
            TekiLog.e(TAG, "", e)
            onError(decoder!!, e)
        }
        return false
    }

    private fun processOutputFormat(outputFormat: MediaFormat) {
        hasOutputFormat = true
        val sampleRate = outputFormat.getInteger(MediaFormat.KEY_SAMPLE_RATE)
        val channels = outputFormat.getInteger(MediaFormat.KEY_CHANNEL_COUNT)
        audioSink?.configure(sampleRate, channels, speed, volume)
        audioSink?.play()
    }

    private fun removeMessage(what: Int) {
        queueBufferHandler.removeMessage(what)
    }

    private fun sendQueueMessage(what: Int, atFront: Boolean = false, delayed: Long = 0) {
        val message = Message.obtain()
        message.what = what
        if (atFront) {
            queueBufferHandler.sendMessageAtFront(message)
        } else {
            queueBufferHandler.sendMessageDelay(message, delayed)
        }
    }

    override fun resetPositionUs(positionUs: Long) {
        if (audioSink?.resetPositionUs(positionUs) != true) {
            this.seekPosition = positionUs
        } else {
            this.seekPosition = 0
        }
    }

    override fun setSpeed(speed: Float) {
        this.speed = speed
        audioSink?.setSpeed(speed)
    }

    override fun setCallback(
        callback: Callback,
        handler: Handler
    ) {
        this.callback = callback
        this.callbackHandler = handler
    }

    private fun onError(
        codec: Decoder<out InputBufferHolder, out OutputBufferHolder>,
        e: Exception,
        fatal: Boolean = true
    ) {
        TekiLog.w(TAG, "MediaCodecEngine onError $e")

        if (release.get()) {
            TekiLog.w(TAG, "onError on release, return")
            return
        }

        if (fatal) {
            onDecodeError(e)
        }
    }

    override fun onFormatOutput(format: MediaFormat) {
        processOutputFormat(format)
    }

    override fun onDecodeError(e: Exception) {
        callbackHandler?.post {
            callback?.onPlaybackException(EngineException(message = e.message, cause = e), e is RecoverableEngineException)
        }
    }

    fun setAesInfo(aesKey: String?, aesIV: String?, isRTPDecrypt:Boolean = false) {
        this.aesIV = aesIV
        this.aesKey = aesKey
        (decoder as? OpusDecoder)?.let {
            it.setCipherCallback(this@MediaAudioEngine)
            it.isRTPRealTimeDecrypt(isRTPDecrypt)
        }
    }

    override fun onDecrypt(cipherData: ByteArray): ByteBuffer? {
        if (null != aesKey && null != aesIV) {
            return AesComponent().apply {
                setCipher(aesKey!!, aesIV!!)
            }.run {
                decryptData(cipherData)?.let {
                    val directBuffer = ByteBuffer.allocateDirect(it.size)
                    directBuffer.put(it)
                    directBuffer.flip()
                    directBuffer
                }
            }
        }
        return null
    }
}