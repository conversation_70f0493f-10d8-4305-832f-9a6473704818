package com.lizhi.component.tekiplayer

import com.lizhi.component.tekiplayer.analyzer.BandwidthQuality
import com.lizhi.component.tekiplayer.controller.PlayerState.State

/**
 * 文件名：PlaybackStateListener
 * 作用：
 * 作者：luo<PERSON><PERSON><EMAIL>
 * 创建日期：2021/4/2
 */
interface PlayEventListener {

    /**
     * 播放状态回调
     *
     * @param status 播放状态定义，具体值可查看[State]
     */
    fun onPlaybackStateChange(@State status: Int)

    /**
     * 发生错误
     *
     * @param errCode 错误码
     */
    fun onError(errCode: Int, message: String)

    /**
     * 当前播放的节目被移除
     */
    fun onPlaybackRemoveOnList()

    /**
     * 播放列表变化
     */
    fun onPlayListUpdate()

    /**
     * 播放列表完成，只有顺序播放才会有此回调
     */
    fun onPlayListFinished()

    /**
     * 播放的歌曲变化
     *
     * @param index 第几首
     * @param item 当前歌曲
     * @param reason 上一首是否播放完成
     */
    fun onPlaybackChange(
        index: Int,
        item: MediaItem?,
        lastPosition: Long,
        reason: Int
    )

    /**
     * 播放进度变化
     *
     * @param index 第几首
     * @param item 当前歌曲
     * @param position 播放进度，单位：ms
     */
    fun onPlayedPositionUpdate(
        index: Int,
        item: MediaItem?,
        position: Long
    )
    /**
     * 监控播放的音频全是空数据
     *
     */
    fun onPlayZeroItem(
        item: MediaItem?
    )

    /**
     * 缓冲进度变化
     *
     * @param index 第几首
     * @param item 当前歌曲
     * @param bufferPosition 播放进度，单位：ms
     */
    fun onBufferedPositionUpdate(
        index: Int,
        item: MediaItem?,
        bufferPosition: Long
    )

    /**
     * 定时进度变化
     *
     * @param index 第几首
     * @param item 当前歌曲
     * @param remainingMs 剩余播放时间，单位：ms
     */
    fun onTimeRemainingUpdate(
        index: Int,
        item: MediaItem?,
        remainingMs: Long
    ) {
    }

    /**
     * 定时进度变化
     *
     * @param index 第几首
     * @param item 当前歌曲
     * @param remainingItem 剩余播放数量
     */
    fun onItemRemainingUpdate(
        index: Int,
        item: MediaItem?,
        remainingItem: Int
    ) {
    }

    /**
     * 音质切换成功
     * @param to 设置的音质
     */
    fun onAudioQualityChange(to: Player.Quality) {}
}

interface NetworkWatcher {

    fun onWatch(@BandwidthQuality.Level qualityLevel: Int, speedBps: Int)
}

