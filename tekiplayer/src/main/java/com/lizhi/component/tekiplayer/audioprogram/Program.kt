package com.lizhi.component.tekiplayer.audioprogram

import android.net.Uri
import android.os.Bundle

/**
 * 文件名：Program
 * 作用：
 * 作者：<EMAIL>
 * 创建日期：2021/4/1
 */

interface Program {

    val uuid: String

    var volume: Float

    val durationUs: Long

    val lastFatalError: Throwable?

    fun isReady(): Boolean

    fun isBuffering(): Boolean

    fun isPlaying(): Boolean

    fun isEnd(): Boolean

    fun isOnError(): Boolean

    /**
     * 预加载
     */
    fun preload(
        prepareCallback: (AudioProgram) -> Unit,
        error: (AudioProgram, Throwable?) -> Unit
    )

    /**
     * 播放
     */
    fun play()

    fun resume()

    fun seek(
        positionUs: Long
    )

    fun seek(
        percent: Float
    )

    fun continueLoading()

    /**
     * 停止播放
     */
    fun stop()

    fun stopLoading()

    /**
     * 暂停播放
     */
    fun pause()

    /**
     * 当前缓冲的位置
     * @return
     */
    fun getBufferedPositionUs(): Long

    /**
     * 设置播放速率
     */
    fun setPlayRate(rate: Float)

    fun getPositionUs(): Long

    fun reportBuffering()

    fun reportPlaying()

    fun reportFinish()

    fun reportForceStop()

    fun reportError(throwable: Throwable, recoverable: Boolean)

    fun reportTransactionEnd()

    interface Factory {

        fun createProgram(
            uri: Uri,
            seekTimeUs: Long,
            preBuffering: Boolean,
            fixedDuration: Long = -1,
            category: String? = null,
            extraData: Bundle? = null
        ): Program
    }
}