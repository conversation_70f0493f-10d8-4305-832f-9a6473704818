package com.lizhi.component.tekiplayer.datasource.exception

import com.lizhi.component.tekiplayer.ERR_DATASOURCE_FILE

/**
 * 文件名：FileDataSourceException
 * 作用：文件数据源抛出的异常信息
 * 作者：huangtianhao
 * 创建日期：2021/3/15
 */
class FileDataSourceException(
    override val cause: Throwable? = null,
    message: String? = null
) : DataSourceException(ERR_DATASOURCE_FILE, message ?: cause?.message ?: "FileDataSourceException", cause)