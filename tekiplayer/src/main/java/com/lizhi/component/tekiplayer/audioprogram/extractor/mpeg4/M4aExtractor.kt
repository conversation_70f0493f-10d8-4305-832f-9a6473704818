package com.lizhi.component.tekiplayer.audioprogram.extractor.mpeg4

import android.media.MediaFormat
import android.os.Build
import com.lizhi.component.tekiplayer.audioprogram.extractor.*
import com.lizhi.component.tekiplayer.audioprogram.extractor.Extractor.Companion.RESULT_CONTINUE
import com.lizhi.component.tekiplayer.audioprogram.extractor.Extractor.Companion.RESULT_END_OF_INPUT
import com.lizhi.component.tekiplayer.audioprogram.extractor.Extractor.Companion.RESULT_SEEK
import com.lizhi.component.tekiplayer.audioprogram.extractor.mpeg4.Sniffer.BRAND_HEIC
import com.lizhi.component.tekiplayer.audioprogram.extractor.mpeg4.Sniffer.BRAND_QUICKTIME
import com.lizhi.component.tekiplayer.engine.BufferHolder
import com.lizhi.component.tekiplayer.engine.DataQueue
import com.lizhi.component.tekiplayer.engine.exception.UnSupportFormatException
import com.lizhi.component.tekiplayer.util.Util
import com.lizhi.component.tekiplayer.util.audio.Ac4Util
import com.lizhi.component.tekiplayer.util.audio.MimeTypes
import java.io.IOException
import java.nio.ByteBuffer
import java.util.*
import kotlin.jvm.Throws
import kotlin.math.max
import kotlin.math.min

/**
 * 文件名：M4aExtractor
 * 作用：M4A音频解析封装
 * 作者：liaodongming
 * 创建日期：2021/2/20
 */
class M4aExtractor : Extractor, Seeker {

    companion object {
        const val TAG = "M4aExtractor"

        private const val STATE_READING_ATOM_HEADER = 0
        private const val STATE_READING_ATOM_PAYLOAD = 1
        private const val STATE_READING_SAMPLE = 2
        private const val STATE_READING_SEF = 3

        private const val FILE_TYPE_MP4 = 0
        private const val FILE_TYPE_QUICKTIME = 1
        private const val FILE_TYPE_HEIC = 2

        /**
         * When seeking within the source, if the offset is greater than or equal to this value (or the
         * offset is negative), the source will be reloaded.
         */
        private const val RELOAD_MINIMUM_SEEK_DISTANCE = 256 * 1024.toLong()

        /**
         * For poorly interleaved streams, the maximum byte difference one track is allowed to be read
         * ahead before the source will be reloaded at a new position to read another track.
         */
        private const val MAXIMUM_READ_AHEAD_BYTES_STREAM = 10 * 1024 * 1024.toLong()
    }

    // Temporary arrays.
    private val nalStartCode: ParsableByteArray = ParsableByteArray(Util.NAL_START_CODE)
    private val nalLength: ParsableByteArray = ParsableByteArray(4)
    private val scratch: ParsableByteArray = ParsableByteArray()

    private val atomHeader: ParsableByteArray = ParsableByteArray(Atom.LONG_HEADER_SIZE)
    private val containerAtoms: ArrayDeque<Atom.ContainerAtom> = ArrayDeque()

    private var parserState = STATE_READING_ATOM_HEADER
    private var atomType = 0
    private var atomSize: Long = 0
    private var atomHeaderBytesRead = 0
    private var atomData: ParsableByteArray? = null

    private var sampleTrackIndex = 0
    private var sampleBytesRead = 0
    private var sampleBytesWritten = 0
    private var sampleCurrentNalBytesRemaining = 0

    private var fileType = 0
    private var tracks: Array<Mp4Track>? = null
    private var accumulatedSampleSizes: Array<LongArray>? = null

    private var dataQueue: DataQueue? = null

    override var mediaFormat: MediaFormat? = null
        private set
    override var durationUs: Long = -1
        private set
    override var seeker: Seeker? = null
        private set
    override var dataEndPosition: Long = -1
        private set


    override fun init(dataQueue: DataQueue?) {
        this.dataQueue = dataQueue
    }

    override fun getDataQueue(): DataQueue? {
        return dataQueue
    }

    override fun sniff(extractorInput: ExtractorInput): Boolean {
        return Sniffer.sniffUnfragmented(extractorInput)
    }

    override fun sample(input: ExtractorInput, seekPosition: PositionHolder): Int {
        while (true) {
            when (parserState) {
                STATE_READING_ATOM_HEADER -> if (!readAtomHeader(input)) {
                    return -1
                }
                STATE_READING_ATOM_PAYLOAD -> if (readAtomPayload(input, seekPosition)) {
                    return 1
                }
                STATE_READING_SAMPLE -> return readSample(input, seekPosition)
                else -> throw IllegalStateException()
            }
        }
    }

    override fun seek(timeUs: Long, position: Long) {
        containerAtoms.clear()
        atomHeaderBytesRead = 0
        sampleTrackIndex = Util.INDEX_UNSET
        sampleBytesRead = 0
        sampleBytesWritten = 0
        sampleCurrentNalBytesRemaining = 0
        if (position == 0L) {
            enterReadingAtomHeaderState()
        } else if (tracks != null) {
            updateSampleIndices(timeUs)
        }
    }

    override fun readSample(buffer: BufferHolder): Int {
        return dataQueue?.blockDequeue(buffer) ?: -2
    }

    override fun reset() {

    }

    override fun timeToPosition(timeUs: Long): SeekPoint {
        return getPositionAtTimeUs(timeUs)
    }

    override fun getPositionAtTimeUs(timeUs: Long): SeekPoint {
        val tracks = tracks ?: return SeekPoint.START
        if (tracks.isEmpty()) {
            return SeekPoint.START
        }

        var firstOffset: Long = Long.MAX_VALUE
        val firstTimeUs: Long = timeUs

        // Take into account other tracks.
        for (i in tracks.indices) {
            val sampleTable = tracks[i].sampleTable
            firstOffset =
                maybeAdjustSeekOffset(
                    sampleTable,
                    firstTimeUs,
                    firstOffset
                )
        }

        return SeekPoint(firstTimeUs, firstOffset)
    }

    override fun release() {
    }

    @Throws(IOException::class)
    private fun readAtomHeader(input: ExtractorInput): Boolean {
        if (atomHeaderBytesRead == 0) {
            // Read the standard length atom header.
            if (!input.readFully(atomHeader.data, 0, Atom.HEADER_SIZE, true)) {
                return false
            }
            atomHeaderBytesRead = Atom.HEADER_SIZE
            atomHeader.position = 0
            atomSize = atomHeader.readUnsignedInt()
            atomType = atomHeader.readInt()
        }
        if (atomSize == Atom.DEFINES_LARGE_SIZE.toLong()) {
            // Read the large size.
            val headerBytesRemaining = Atom.LONG_HEADER_SIZE - Atom.HEADER_SIZE
            input.readFully(atomHeader.data, Atom.HEADER_SIZE, headerBytesRemaining)
            atomHeaderBytesRead += headerBytesRemaining
            atomSize = atomHeader.readUnsignedLongToLong()
        } else if (atomSize == Atom.EXTENDS_TO_END_SIZE.toLong()) {
            // The atom extends to the end of the file. Note that if the atom is within a container we can
            // work out its size even if the input length is unknown.
            var endPosition: Long = input.length
            if (endPosition == Util.LENGTH_UNSET) {
                val containerAtom: Atom.ContainerAtom? = containerAtoms.peek()
                if (containerAtom != null) {
                    endPosition = containerAtom.endPosition
                }
            }
            if (endPosition != Util.LENGTH_UNSET) {
                atomSize = endPosition - input.position + atomHeaderBytesRead
            }
        }
        if (atomSize < atomHeaderBytesRead) {
            throw UnSupportFormatException("Atom size less than header length (unsupported).")
        }
        if (shouldParseContainerAtom(atomType)) {
            val endPosition: Long = input.position + atomSize - atomHeaderBytesRead
            if (atomSize != atomHeaderBytesRead.toLong() && atomType == Atom.TYPE_meta) {
                maybeSkipRemainingMetaAtomHeaderBytes(input)
            }
            containerAtoms.push(Atom.ContainerAtom(atomType, endPosition))
            if (atomSize == atomHeaderBytesRead.toLong()) {
                processAtomEnded(endPosition)
            } else {
                // Start reading the first child atom.
                enterReadingAtomHeaderState()
            }
        } else if (shouldParseLeafAtom(atomType)) {
            // We don't support parsing of leaf atoms that define extended atom sizes, or that have
            // lengths greater than Integer.MAX_VALUE.
            Util.checkState(atomHeaderBytesRead == Atom.HEADER_SIZE)
            Util.checkState(atomSize <= Int.MAX_VALUE)
            val atomData = ParsableByteArray(atomSize.toInt())
            System.arraycopy(
                atomHeader.data,
                0,
                atomData.data,
                0,
                Atom.HEADER_SIZE
            )
            this.atomData = atomData
            parserState =
                STATE_READING_ATOM_PAYLOAD
        } else {
            processUnparsedAtom(input.position - atomHeaderBytesRead)
            atomData = null
            parserState = STATE_READING_ATOM_PAYLOAD
        }
        return true
    }


    /**
     * Processes the atom payload. If [.atomData] is null and the size is at or above the
     * threshold [.RELOAD_MINIMUM_SEEK_DISTANCE], `true` is returned and the caller should
     * restart loading at the position in `positionHolder`. Otherwise, the atom is read/skipped.
     */
    @Throws(IOException::class)
    private fun readAtomPayload(
        input: ExtractorInput,
        positionHolder: PositionHolder
    ): Boolean {
        val atomPayloadSize = atomSize - atomHeaderBytesRead
        val atomEndPosition: Long = input.position + atomPayloadSize
        var seekRequired = false
        val atomData = atomData
        if (atomData != null) {
            input.readFully(atomData.data, atomHeaderBytesRead, atomPayloadSize.toInt())
            if (atomType == Atom.TYPE_ftyp) {
                fileType = processFtypAtom(atomData)
            } else if (!containerAtoms.isEmpty()) {
                containerAtoms.peek()?.add(Atom.LeafAtom(atomType, atomData))
            }
        } else {
            // We don't need the data. Skip or seek, depending on how large the atom is.
            if (atomPayloadSize < RELOAD_MINIMUM_SEEK_DISTANCE) {
                input.skipFully(atomPayloadSize.toInt())
            } else {
                positionHolder.position = input.position + atomPayloadSize
                seekRequired = true
            }
        }
        processAtomEnded(atomEndPosition)
        return seekRequired && parserState != STATE_READING_SAMPLE
    }


    /**
     * Attempts to extract the next sample in the current mdat atom for the specified track.
     *
     *
     * Returns [.RESULT_SEEK] if the source should be reloaded from the position in `positionHolder`.
     *
     *
     * Returns [.RESULT_END_OF_INPUT] if no samples are left. Otherwise, returns [ ][.RESULT_CONTINUE].
     *
     * @param input The [ExtractorInput] from which to read data.
     * @param positionHolder If [.RESULT_SEEK] is returned, this holder is updated to hold the
     * position of the required data.
     * @return One of the `RESULT_*` flags in [Extractor].
     * @throws IOException If an error occurs reading from the input.
     */
    @Throws(IOException::class)
    private fun readSample(input: ExtractorInput, positionHolder: PositionHolder): Int {
        val inputPosition: Long = input.position
        if (sampleTrackIndex == Util.INDEX_UNSET) {
            sampleTrackIndex = getTrackIndexOfNextReadSample(inputPosition)
            if (sampleTrackIndex == Util.INDEX_UNSET) {
                dataQueue?.setEndFlag()
                return RESULT_END_OF_INPUT
            }
        }
        val track: Mp4Track = tracks?.get(sampleTrackIndex) ?: throw NullPointerException()
        val sampleIndex = track.sampleIndex
        val position = track.sampleTable.offsets[sampleIndex]
        var sampleSize = track.sampleTable.sizes[sampleIndex]
        var skipAmount: Long = position - inputPosition + sampleBytesRead
        if (skipAmount < 0 || skipAmount >= RELOAD_MINIMUM_SEEK_DISTANCE) {
            positionHolder.position = position
            return RESULT_SEEK
        }
        if (track.track.sampleTransformation == Track.TRANSFORMATION_CEA608_CDAT) {
            // The sample information is contained in a cdat atom. The header must be discarded for
            // committing.
            skipAmount += Atom.HEADER_SIZE
            sampleSize -= Atom.HEADER_SIZE
        }
        input.skipFully(skipAmount.toInt())
        if (MimeTypes.AUDIO_AC4 == track.track.format.sampleMimeType) {
            if (sampleBytesWritten == 0) {
                Ac4Util.getAc4SampleHeader(sampleSize, scratch)
//                dataQueue?.readToQueue(scratch, Ac4Util.SAMPLE_HEADER_SIZE)
                sampleBytesWritten += Ac4Util.SAMPLE_HEADER_SIZE
            }
            sampleSize += Ac4Util.SAMPLE_HEADER_SIZE
        }
        dataQueue?.sampleMetadata(track.sampleTable.timestampsUs[sampleIndex], sampleSize, input.position)
        while (sampleBytesWritten < sampleSize) {
            val writtenBytes: Int =
                dataQueue?.readToQueue(
                    input,
                    sampleSize - sampleBytesWritten,
                    track.sampleTable.timestampsUs[sampleIndex]
                ) ?: return -2
            sampleBytesRead += writtenBytes
            sampleBytesWritten += writtenBytes
            sampleCurrentNalBytesRemaining -= writtenBytes
        }
        track.sampleIndex++
        sampleTrackIndex = Util.INDEX_UNSET
        sampleBytesRead = 0
        sampleBytesWritten = 0
        sampleCurrentNalBytesRemaining = 0
        return RESULT_CONTINUE
    }


    /**
     * Returns the index of the track that contains the next sample to be read, or [ ][Util.INDEX_UNSET] if no samples remain.
     *
     *
     * The preferred choice is the sample with the smallest offset not requiring a source reload,
     * or if not available the sample with the smallest overall offset to avoid subsequent source
     * reloads.
     *
     *
     * To deal with poor sample interleaving, we also check whether the required memory to catch up
     * with the next logical sample (based on sample time) exceeds [ ][.MAXIMUM_READ_AHEAD_BYTES_STREAM]. If this is the case, we continue with this sample even
     * though it may require a source reload.
     */
    private fun getTrackIndexOfNextReadSample(inputPosition: Long): Int {
        var preferredSkipAmount = Long.MAX_VALUE
        var preferredRequiresReload = true
        var preferredTrackIndex: Int = Util.INDEX_UNSET
        var preferredAccumulatedBytes = Long.MAX_VALUE
        var minAccumulatedBytes = Long.MAX_VALUE
        var minAccumulatedBytesRequiresReload = true
        var minAccumulatedBytesTrackIndex: Int = Util.INDEX_UNSET
        for (trackIndex in tracks!!.indices) {
            val track = tracks!![trackIndex]
            val sampleIndex = track.sampleIndex
            if (sampleIndex == track.sampleTable.sampleCount) {
                continue
            }
            val sampleOffset = track.sampleTable.offsets[sampleIndex]
            val sampleAccumulatedBytes: Long = accumulatedSampleSizes!![trackIndex][sampleIndex]
            val skipAmount = sampleOffset - inputPosition
            val requiresReload =
                skipAmount < 0 || skipAmount >= RELOAD_MINIMUM_SEEK_DISTANCE
            if (!requiresReload && preferredRequiresReload
                || requiresReload == preferredRequiresReload && skipAmount < preferredSkipAmount
            ) {
                preferredRequiresReload = requiresReload
                preferredSkipAmount = skipAmount
                preferredTrackIndex = trackIndex
                preferredAccumulatedBytes = sampleAccumulatedBytes
            }
            if (sampleAccumulatedBytes < minAccumulatedBytes) {
                minAccumulatedBytes = sampleAccumulatedBytes
                minAccumulatedBytesRequiresReload = requiresReload
                minAccumulatedBytesTrackIndex = trackIndex
            }
        }
        return if (minAccumulatedBytes == Long.MAX_VALUE || !minAccumulatedBytesRequiresReload
            || preferredAccumulatedBytes < minAccumulatedBytes + MAXIMUM_READ_AHEAD_BYTES_STREAM
        ) preferredTrackIndex else minAccumulatedBytesTrackIndex
    }


    /** Returns whether the extractor should decode a container atom with type `atom`.  */
    private fun shouldParseContainerAtom(atom: Int): Boolean {
        return atom == Atom.TYPE_moov
                || atom == Atom.TYPE_trak
                || atom == Atom.TYPE_mdia
                || atom == Atom.TYPE_minf
                || atom == Atom.TYPE_stbl
                || atom == Atom.TYPE_edts
                || atom == Atom.TYPE_meta
    }


    /** Returns whether the extractor should decode a leaf atom with type `atom`.  */
    private fun shouldParseLeafAtom(atom: Int): Boolean {
        return atom == Atom.TYPE_mdhd
                || atom == Atom.TYPE_mvhd
                || atom == Atom.TYPE_hdlr
                || atom == Atom.TYPE_stsd
                || atom == Atom.TYPE_stts
                || atom == Atom.TYPE_stss
                || atom == Atom.TYPE_ctts
                || atom == Atom.TYPE_elst
                || atom == Atom.TYPE_stsc
                || atom == Atom.TYPE_stsz
                || atom == Atom.TYPE_stz2
                || atom == Atom.TYPE_stco
                || atom == Atom.TYPE_co64
                || atom == Atom.TYPE_tkhd
                || atom == Atom.TYPE_ftyp
                || atom == Atom.TYPE_udta
                || atom == Atom.TYPE_keys
                || atom == Atom.TYPE_ilst
    }

    /**
     * Updates every track's sample index to point its latest sync sample before/at `timeUs`.
     */
    private fun updateSampleIndices(timeUs: Long) {
        val arrayOfMp4Tracks = tracks ?: return
        for (track in arrayOfMp4Tracks) {
            val sampleTable = track.sampleTable
            var sampleIndex = sampleTable.getIndexOfEarlierOrEqualSynchronizationSample(timeUs)
            if (sampleIndex == Util.INDEX_UNSET) {
                // Handle the case where the requested time is before the first synchronization sample.
                sampleIndex = sampleTable.getIndexOfLaterOrEqualSynchronizationSample(timeUs)
            }
            track.sampleIndex = sampleIndex
        }
    }

    /**
     * Process an ftyp atom to determine the corresponding [FileType].
     *
     * @param atomData The ftyp atom data.
     * @return The [FileType].
     */
    private fun processFtypAtom(atomData: ParsableByteArray): Int {
        atomData.position = Atom.HEADER_SIZE
        val majorBrand = atomData.readInt()
        var fileType: Int =
            brandToFileType(majorBrand)
        if (fileType != FILE_TYPE_MP4) {
            return fileType
        }
        atomData.skipBytes(4) // minor_version
        while (atomData.bytesLeft() > 0) {
            fileType =
                brandToFileType(atomData.readInt())
            if (fileType != FILE_TYPE_MP4) {
                return fileType
            }
        }
        return FILE_TYPE_MP4
    }

    private fun brandToFileType(brand: Int): Int {
        return when (brand) {
            BRAND_QUICKTIME -> FILE_TYPE_QUICKTIME
            BRAND_HEIC -> FILE_TYPE_HEIC
            else -> FILE_TYPE_MP4
        }
    }


    /** Processes an atom whose payload does not need to be parsed.  */
    private fun processUnparsedAtom(atomStartPosition: Long) {
        if (atomType == Atom.TYPE_mpvd) {
        }
    }

    @Throws(IOException::class)
    private fun maybeSkipRemainingMetaAtomHeaderBytes(input: ExtractorInput) {
        scratch.reset(8)
        input.peekFully(scratch.data, 0, 8)
        AtomParsers.maybeSkipRemainingMetaAtomHeaderBytes(scratch)
        input.skipFully(scratch.position)
        input.resetPeekPosition()
    }

    @Throws(UnSupportFormatException::class)
    private fun processAtomEnded(atomEndPosition: Long) {
        while (!containerAtoms.isEmpty() && containerAtoms.peek()?.endPosition == atomEndPosition) {
            val containerAtom: Atom.ContainerAtom? = containerAtoms.pop()
            if (containerAtom?.type == Atom.TYPE_moov) {
                // We've reached the end of the moov atom. Process it and prepare to read samples.
                processMoovAtom(containerAtom)
                containerAtoms.clear()
                parserState = STATE_READING_SAMPLE
            } else if (!containerAtoms.isEmpty()) {
                containerAtoms.peek()?.add(containerAtom)
            }
        }
        if (parserState != STATE_READING_SAMPLE) {
            enterReadingAtomHeaderState()
        }
    }

    // Private methods.

    private fun enterReadingAtomHeaderState() {
        parserState = STATE_READING_ATOM_HEADER
        atomHeaderBytesRead = 0
    }

    /**
     * Updates the stored track metadata to reflect the contents of the specified moov atom.
     */
    @Throws(UnSupportFormatException::class)
    private fun processMoovAtom(moov: Atom.ContainerAtom) {
        var durationUs: Long = Util.TIME_UNSET
        val tracks: MutableList<Mp4Track> = ArrayList()

        // Process metadata.
        val isQuickTime = fileType == FILE_TYPE_QUICKTIME
        val ignoreEditLists = false
        val trackSampleTables: List<TrackSampleTable> = AtomParsers.parseTraks(
            moov,
            Util.TIME_UNSET,
            ignoreEditLists,
            isQuickTime  /* modifyTrackFunction= */
        )

        val trackCount = trackSampleTables.size
        for (i in 0 until trackCount) {
            val trackSampleTable: TrackSampleTable = trackSampleTables[i]
            if (trackSampleTable.sampleCount == 0) {
                continue
            }
            val track: Track = trackSampleTable.track
            // 只提取audio数据
            if (track.type != Util.TRACK_TYPE_AUDIO) {
                continue
            }
            val trackDurationUs: Long =
                if (track.durationUs != Util.TIME_UNSET) track.durationUs else trackSampleTable.durationUs
            durationUs = max(durationUs, trackDurationUs)
            val mp4Track = Mp4Track(track, trackSampleTable)

            // Each sample has up to three bytes of overhead for the start code that replaces its length.
            // Allow ten source samples per output sample, like the platform extractor.
            val maxInputSize: Int = trackSampleTable.maximumSize + 3 * 10
            val formatBuilder: Format.Builder = track.format.buildUpon()
            formatBuilder.setMaxInputSize(maxInputSize)
//            mp4Track.trackOutput.format(formatBuilder.build())
            val build = formatBuilder.build()
            val m4aFormat = MediaFormat.createAudioFormat(build.sampleMimeType!!, build.sampleRate, build.channelCount)
            if (maxInputSize > 0) {
                m4aFormat.setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, maxInputSize)
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                m4aFormat.setInteger(MediaFormat.KEY_PRIORITY,0 /* realtime priority */)
            }
            setCsdBuffers(m4aFormat, build.initializationData)
            mediaFormat = m4aFormat
            tracks.add(mp4Track)
        }
        this.durationUs = durationUs
        this.tracks = tracks.toTypedArray()
        accumulatedSampleSizes = calculateAccumulatedSampleSizes(this.tracks!!)

        seeker = this
    }

    /**
     * Sets a [MediaFormat]'s codec specific data buffers.
     *
     * @param format The [MediaFormat] being configured.
     * @param csdBuffers The csd buffers to set.
     */
    fun setCsdBuffers(
        format: MediaFormat,
        csdBuffers: List<ByteArray>
    ) {
        for (i in csdBuffers.indices) {
            format.setByteBuffer("csd-$i", ByteBuffer.wrap(csdBuffers[i]))
        }
    }


    /**
     * Adjusts a seek point offset to take into account the track with the given `sampleTable`,
     * for a given `seekTimeUs`.
     *
     * @param sampleTable The sample table to use.
     * @param seekTimeUs The seek time in microseconds.
     * @param offset The current offset.
     * @return The adjusted offset.
     */
    private fun maybeAdjustSeekOffset(
        sampleTable: TrackSampleTable, seekTimeUs: Long, offset: Long
    ): Long {
        val sampleIndex: Int =
            getSynchronizationSampleIndex(
                sampleTable,
                seekTimeUs
            )
        if (sampleIndex == Util.INDEX_UNSET) {
            return offset
        }
        val sampleOffset = sampleTable.offsets[sampleIndex]
        return min(sampleOffset, offset)
    }

    /**
     * Returns the index of the synchronization sample before or at `timeUs`, or the index of
     * the first synchronization sample if located after `timeUs`, or [Util.INDEX_UNSET] if
     * there are no synchronization samples in the table.
     *
     * @param sampleTable The sample table in which to locate a synchronization sample.
     * @param timeUs A time in microseconds.
     * @return The index of the synchronization sample before or at `timeUs`, or the index of
     * the first synchronization sample if located after `timeUs`, or [Util.INDEX_UNSET]
     * if there are no synchronization samples in the table.
     */
    private fun getSynchronizationSampleIndex(
        sampleTable: TrackSampleTable,
        timeUs: Long
    ): Int {
        var sampleIndex = sampleTable.getIndexOfEarlierOrEqualSynchronizationSample(timeUs)
        if (sampleIndex == Util.INDEX_UNSET) {
            // Handle the case where the requested time is before the first synchronization sample.
            sampleIndex = sampleTable.getIndexOfLaterOrEqualSynchronizationSample(timeUs)
        }
        return sampleIndex
    }

    /**
     * For each sample of each track, calculates accumulated size of all samples which need to be read
     * before this sample can be used.
     */
    private fun calculateAccumulatedSampleSizes(tracks: Array<Mp4Track>): Array<LongArray> {
        val accumulatedSampleSizes = Array(tracks.size) { LongArray(it) }
        val nextSampleIndex = IntArray(tracks.size)
        val nextSampleTimesUs = LongArray(tracks.size)
        val tracksFinished = BooleanArray(tracks.size)
        for (i in tracks.indices) {
            accumulatedSampleSizes[i] = LongArray(tracks[i].sampleTable.sampleCount)
            nextSampleTimesUs[i] = tracks[i].sampleTable.timestampsUs[0]
        }
        var accumulatedSampleSize: Long = 0
        var finishedTracks = 0
        while (finishedTracks < tracks.size) {
            var minTimeUs = Long.MAX_VALUE
            var minTimeTrackIndex = -1
            for (i in tracks.indices) {
                if (!tracksFinished[i] && nextSampleTimesUs[i] <= minTimeUs) {
                    minTimeTrackIndex = i
                    minTimeUs = nextSampleTimesUs[i]
                }
            }
            var trackSampleIndex = nextSampleIndex[minTimeTrackIndex]
            accumulatedSampleSizes[minTimeTrackIndex][trackSampleIndex] = accumulatedSampleSize
            accumulatedSampleSize += tracks[minTimeTrackIndex].sampleTable.sizes[trackSampleIndex]
            nextSampleIndex[minTimeTrackIndex] = ++trackSampleIndex
            if (trackSampleIndex < accumulatedSampleSizes[minTimeTrackIndex].size) {
                nextSampleTimesUs[minTimeTrackIndex] =
                    tracks[minTimeTrackIndex].sampleTable.timestampsUs[trackSampleIndex]
            } else {
                tracksFinished[minTimeTrackIndex] = true
                finishedTracks++
            }
        }
        return accumulatedSampleSizes
    }

    private class Mp4Track(
        val track: Track,
        val sampleTable: TrackSampleTable
    ) {
        var sampleIndex = 0
    }
}