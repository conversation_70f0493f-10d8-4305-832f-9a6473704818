package com.lizhi.component.tekiplayer.engine

import android.os.Handler
import android.os.HandlerThread
import android.os.SystemClock
import com.lizhi.component.tekiplayer.util.TekiLog

class DefaultTimeline(sampleRate: Int, channels: Int, private var speed: Float) : Timeline,
    Runnable {

    companion object {
        private const val TAG = "DefaultTimeline"
    }

    // 计算码率
    private var bitrate = sampleRate * channels * 2
    private var zeroDataFlag = false
    private var seekPositionUs: Long = 0
    private var currPositionMs: Long = 0
    private var remainBufferSize: Long = 0
    private var totalBufferSize: Long = 0
    private var lastBufferStartTime: Long = 0

    // 是否正在更新进度，暂停、buffer消耗完毕等场景都会停止更新进度
    private var counting = false
    // 当前 buffer 是否消耗完毕标记
    private var bufferEnd = false
    // 是否已经 buffer 写入结束，歌曲到末尾，等待播放完成
    private var waitToEnd = false

    private val handlerThread = HandlerThread("TekiPlayer:Engine:Timeline")
    private val handler: Handler
    private var bufferEndListener: ((zeroData:Boolean) -> Unit)? = null

    private val lock = Any()

    init {
        handlerThread.start()
        handler = Handler(handlerThread.looper)
    }

    override fun getPositionUs(): Long {
        return if (bufferEnd) {
            (totalBufferSize * 1.0f / bitrate * 1000 * 1000).toLong() + seekPositionUs
        } else {
            // 如果还在写入buffer，获取时需要更新一下进度
            if (counting) {
                val currTime = SystemClock.elapsedRealtime()
                val duration = currTime - lastBufferStartTime
                increasePosition(duration)
                consumeBuffer(duration, speed)
                lastBufferStartTime = currTime
            }
            currPositionMs * 1000 + seekPositionUs
        }
    }

    override fun addBuffer(size: Long, timeUs: Long, isPlaying: Boolean) {
        val currTime = SystemClock.elapsedRealtime()
        bufferEnd = false
        handler.removeCallbacks(this)
        totalBufferSize += size

        if (!counting && timeUs != -1L) {
            counting = true
        } else if (isPlaying) {
            val duration = currTime - lastBufferStartTime
            consumeBuffer(duration, speed)
            increasePosition(duration)
//            TekiLog.d(TAG, "addBuffer, timeUs=$timeUs, position=$currPositionMs，bitrate=$bitrate, totalSize=$totalBufferSize, remainBufferSize=$remainBufferSize")
            remainBufferSize += size
            handler.postDelayed(this, (remainBufferSize * 1000.0f / bitrate).toLong())
        } else {
            totalBufferSize += size
            remainBufferSize += size
        }

        lastBufferStartTime = currTime
    }
    override fun playToEnd(zeroData: Boolean) {
        waitToEnd = true
//        TekiLog.d(TAG, "playToEnd $remainBufferSize $counting $bufferEnd")
        if (remainBufferSize <= 0 && !counting) {
            bufferEndListener?.invoke(zeroData)
        } else {
            zeroDataFlag = zeroData
            addBuffer(0, -1, true)
        }
    }

    override fun run() {
        counting = false
        bufferEnd = true
        // 修正 position
        currPositionMs = (totalBufferSize * 1.0f / bitrate * 1000).toLong()
//        TekiLog.d(TAG, "buffer costed, position = $currPositionMs")
        remainBufferSize = 0
        if (waitToEnd) {
//            TekiLog.d(TAG, "waitToEnd, position = $currPositionMs")
            bufferEndListener?.invoke(zeroDataFlag)
        }
    }

    override fun pause() {
//        TekiLog.d(TAG, "pause $counting")
        if (!counting) {
            return
        }
        counting = false
        val currTime = SystemClock.elapsedRealtime()
        val duration = currTime - lastBufferStartTime
        consumeBuffer(duration, speed)
        increasePosition(duration)
        handler.removeCallbacks(this)
//        TekiLog.d(TAG, "pause $counting $remainBufferSize")
    }

    override fun seek(positionUs: Long): Boolean {
        counting = false
        bufferEnd = true
        seekPositionUs = positionUs
        currPositionMs = 0
        totalBufferSize = 0
        remainBufferSize = 0
        TekiLog.d(TAG, "seekPosition=$seekPositionUs")
        handler.removeCallbacks(this)
        return true
    }

    override fun setSpeed(speed: Float) {
        val oldSpeed = this.speed
        this.speed = speed
        // 更变速度的时候需要记录变更时间，后面的时间使用新的速度进行消耗
        if (counting) {
            val currTime = SystemClock.elapsedRealtime()
            val duration = currTime - lastBufferStartTime
            consumeBuffer(duration, oldSpeed)
            increasePosition(duration)
            lastBufferStartTime = currTime
        }
    }

    override fun setBufferEndFlowListener(listener: (zeroData:Boolean) -> Unit) {
        bufferEndListener = listener
    }

    override fun stop() {
        counting = false
        handlerThread.quitSafely()
    }

    private fun increasePosition(duration: Long) {
        currPositionMs += getSpeedTime(duration)
    }

    private fun getSpeedTime(duration: Long) = (duration * speed).toLong()

    private fun consumeBuffer(duration: Long, speed: Float) {
        if (remainBufferSize > 0) {
            var lossBuffer = (remainBufferSize - duration * speed / 1000.0f * bitrate).toLong()
            if (lossBuffer < 0) {
                lossBuffer = 0
            }
            remainBufferSize = lossBuffer
        } else {
            remainBufferSize = 0
        }
    }

}