/*
 * Copyright (C) 2018 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.lizhi.component.tekiplayer.audioprogram.extractor;

import com.lizhi.component.tekiplayer.util.TekiLog;
import java.io.EOFException;
import java.io.IOException;

/**
 * Peeks data from the beginning of an {@link ExtractorInput} to determine if there is any ID3 tag.
 */
public final class Id3Peeker {

    /** The first three bytes of a well formed ID3 tag header. */
    public static final int ID3_TAG = 0x00494433;
    /**
     * Length of an ID3 tag header.
     */
    public static final int ID3_HEADER_LENGTH = 10;

    private final ParsableByteArray scratch;

    public Id3Peeker() {
        scratch = new ParsableByteArray(ID3_HEADER_LENGTH);
    }

    /**
     * Peeks ID3 data from the input and parses the first ID3 tag.
     *
     * @param input The {@link ExtractorInput} from which data should be peeked.
     * @param id3FramePredicate Determines which ID3 frames are decoded. May be null to decode all
     * frames.
     * @return The first ID3 tag decoded into a {@link Metadata} object. May be null if ID3 tag is not
     * present in the input.
     * @throws IOException If an error occurred peeking from the input.
     */
    public void peekId3Data(ExtractorInput input) throws IOException {
        int peekedId3Bytes = 0;
        while (true) {
            try {
                input.peekFully(scratch.getData(), 0, ID3_HEADER_LENGTH);
            } catch (EOFException e) {
                // If input has less than ID3_HEADER_LENGTH, ignore the rest.
                break;
            }
            scratch.setPosition(0);
            if (scratch.readUnsignedInt24() != ID3_TAG) {
                // Not an ID3 tag.
                break;
            } else {
                // Skip major version, minor version and flags.
                scratch.skipBytes(3);
                int framesLength = scratch.readSynchSafeInt();
                int tagLength = ID3_HEADER_LENGTH + framesLength;
                if (tagLength >= 0) {
                    peekedId3Bytes += tagLength;
                } else {
                    TekiLog.w("ID3Peeker", "read negative id3 length=" + tagLength);
                }
            }
        }

        input.resetPeekPosition();
        input.advancePeekPosition(peekedId3Bytes);
    }
}
