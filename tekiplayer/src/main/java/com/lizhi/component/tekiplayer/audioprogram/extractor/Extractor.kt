package com.lizhi.component.tekiplayer.audioprogram.extractor

import android.media.MediaFormat
import com.lizhi.component.tekiplayer.engine.BufferHolder
import com.lizhi.component.tekiplayer.engine.DataQueue
import java.io.EOFException
import kotlin.jvm.Throws

/**
 * 文件名：Extractor
 * 作用：
 * 作者：liaodongming
 * 创建日期：2021/2/20
 */
interface Extractor {

    companion object {
        const val RESULT_CONTINUE = 0
        const val RESULT_SEEK = 1
        const val RESULT_END_OF_INPUT = -1
        const val RESULT_UNINITED = -2
    }

    val mediaFormat: MediaFormat?

    val durationUs: Long?

    val seeker: Seeker?

    fun init(dataQueue: DataQueue?)

    fun getDataQueue(): DataQueue?

    @Throws(EOFException::class)
    fun sniff(extractorInput: ExtractorInput): Boolean

    fun sample(input: ExtractorInput, seekPosition: PositionHolder): Int

    /**
     * 跳到目标位置
     *
     * @param timeUs
     * @return 0：成功，-1：没有足够的缓存进行快进
     */
    fun seek(timeUs: Long, position: Long)

    fun readSample(buffer: BufferHolder): Int

    fun reset()

    fun timeToPosition(timeUs: Long): SeekPoint

    fun release()
}