package com.lizhi.component.tekiplayer.controller

import android.content.Context
import com.lizhi.component.basetool.algorithm.Md5Util
import com.lizhi.component.tekiplayer.datasource.cache.CacheMmkvStorage
import com.lizhi.component.tekiplayer.datasource.cache.LruCacheEvictor
import com.lizhi.component.tekiplayer.datasource.entity.DeleteCacheResult
import com.lizhi.component.tekiplayer.datasource.entity.DeleteCacheType
import com.lizhi.component.tekiplayer.datasource.entity.MediaInfo
import com.lizhi.component.tekiplayer.datasource.entity.MediaInfoResult
import com.lizhi.component.tekiplayer.util.TekiLog
import java.io.File
import java.net.URL

/**
 * 缓存管理（删除/查询）
 */
object CacheController {
    private val TAG = "CacheController"
    private val lock = Any()

    @Volatile
    private var usingSets: HashSet<String> = HashSet()

    @Volatile
    var enableBuiltinPolicy = true

    @Volatile
    var isDeleting = false //外部删除策略的删除标记

    fun addUsing(urlWithoutHost: String) {
        usingSets.add(urlWithoutHost)
    }

    fun removeUsing(urlWithoutHost: String) {
        usingSets.remove(urlWithoutHost)
    }

    /**
     * 删除指定url的缓存
     */
    fun deleteMediaInfos(context: Context, infos: List<MediaInfo>): List<MediaInfoResult> {
        val list:MutableList<MediaInfoResult> = mutableListOf()
        synchronized(lock) {
            isDeleting = true
            for (info in infos) {
                try {
                    if (enableBuiltinPolicy) {
                        list.add(MediaInfoResult(info, DeleteCacheResult.RESULT_ERROR_ENABLE_BUILTIN_DELETE_POLICY))
                        continue
                    }
                    val mmkv = if (info.isHighPriority) {
                        CacheMmkvStorage.getInstance(CacheMmkvStorage.TEKI_PLAYER_HIGH_MMKV_ID)
                    } else {
                        CacheMmkvStorage.getInstance(CacheMmkvStorage.TEKI_PLAYER_MMKV_ID)
                    }
                    val downloadDirectory = if (info.isHighPriority) {
                        getHighPriorityCachePath(context)
                    } else {
                        getNormalCachePath(context)
                    }
                    val urlWithoutHost = getUrlWithoutHost(info.url)
                    val cacheInfo = mmkv.get(urlWithoutHost)
                    val fileName = cacheInfo?.fileName ?: getFileNameFromUrl(urlWithoutHost)
                    val file = File(downloadDirectory, fileName)
                    if (null == cacheInfo || !file.exists()) {
                        list.add(MediaInfoResult(info, DeleteCacheResult.RESULT_ERROR_URL_NOT_FOUND))
                        continue
                    }
                    if (usingSets.contains(urlWithoutHost)) {
                        list.add(MediaInfoResult(info, DeleteCacheResult.RESULT_ERROR_USING))
                        continue
                    }
                    val mmkvResult =  mmkv.remove(urlWithoutHost)
                    val fileResult = file.delete()
                    if (fileResult && mmkvResult) {
                        list.add(MediaInfoResult(info, DeleteCacheResult.RESULT_SUCCESS))
                    } else {
                        list.add(MediaInfoResult(info, DeleteCacheResult.RESULT_ERROR_INNER))
                    }
                } catch (e: Exception) {
                    TekiLog.e(TAG, "error on clearAllCaches()", e)
                    list.add(MediaInfoResult(info, DeleteCacheResult.RESULT_ERROR_INNER))
                }
            }
            isDeleting = false
        }
        return list
    }

    /**
     * 删除指定类型缓存
     */
    fun deleteCaches(context: Context, type: DeleteCacheType) {
//        if (enableBuiltinPolicy) {
//            return
//        }
        synchronized(lock) {
            isDeleting = true
            when(type) {
                DeleteCacheType.ALL -> {
                    clearCaches(getNormalCachePath(context), CacheMmkvStorage.TEKI_PLAYER_MMKV_ID)
                    clearCaches(getHighPriorityCachePath(context), CacheMmkvStorage.TEKI_PLAYER_HIGH_MMKV_ID)
                }
                DeleteCacheType.HIGH_PRIORITY -> {
                    clearCaches(getHighPriorityCachePath(context), CacheMmkvStorage.TEKI_PLAYER_HIGH_MMKV_ID)
                }
                DeleteCacheType.NORMAL -> {
                    clearCaches(getNormalCachePath(context), CacheMmkvStorage.TEKI_PLAYER_MMKV_ID)
                }
                else -> {}
            }
            isDeleting = false
        }
    }

    /**
     * 查询指定url的缓存大小
     */
    fun queryMediaInfosCacheSize(context: Context, infos: List<MediaInfo>) : Long {
        var size = 0L
        synchronized(lock) {
            for (info in infos) {
                try {
                    val mmkv = if (info.isHighPriority) {
                        CacheMmkvStorage.getInstance(CacheMmkvStorage.TEKI_PLAYER_HIGH_MMKV_ID)
                    } else {
                        CacheMmkvStorage.getInstance(CacheMmkvStorage.TEKI_PLAYER_MMKV_ID)
                    }
                    val downloadDirectory = if (info.isHighPriority) {
                        getHighPriorityCachePath(context)
                    } else {
                        getNormalCachePath(context)
                    }
                    val urlWithoutHost = getUrlWithoutHost(info.url)
                    val cacheInfo = mmkv.get(urlWithoutHost)
                    val fileName = cacheInfo?.fileName ?: getFileNameFromUrl(urlWithoutHost)
                    val file = File(downloadDirectory, fileName)
                    size += file.length()
                } catch (e: Exception) {
                    TekiLog.e(TAG, "error on clearAllCaches()", e)
                }
            }
        }
        return size
    }

    /**
     * 查询所有缓存大小
     */
    fun queryAllCacheSize(context: Context) : Long {
        var size = 0L
        synchronized(lock) {
            val normalPath = getNormalCachePath(context)
            val highPriorityPath = getHighPriorityCachePath(context)
            size += getDirectorySize(normalPath)
            size += getDirectorySize(highPriorityPath)
        }
        return size
    }

    /**
     * 开启内置缓存删除策略
     */
    fun enableBuiltinPolicy(isEnable: Boolean) {
        enableBuiltinPolicy = isEnable
    }

    private fun clearCaches(cachePath:File, mmkvId: String) {
        try {
            CacheMmkvStorage.getInstance(mmkvId).clearAll()
            cachePath.listFiles()?.forEach {
                it.deleteRecursively()
            }
        } catch (e: Exception) {
            TekiLog.e(TAG, "error on clearAllCaches()", e)
        }
    }

    private fun getNormalCachePath(context: Context): File {
        return context.externalCacheDir?.let { File(it, "tekiPlayer").apply { mkdirs() } }
                ?: context.getExternalFilesDir("tekiPlayer").apply { this?.mkdirs() }
                ?: context.cacheDir.let { File(it, "tekiPlayer").apply { mkdirs() } }
    }

    private fun getHighPriorityCachePath(context: Context): File {
        return context.externalCacheDir?.let { File(it, LruCacheEvictor.CACHE_PRIORITY_PATH).apply { mkdirs() } }
            ?: context.getExternalFilesDir(LruCacheEvictor.CACHE_PRIORITY_PATH).apply { this?.mkdirs() }
            ?: context.cacheDir.let { File(it, LruCacheEvictor.CACHE_PRIORITY_PATH).apply { mkdirs() } }
    }

    /**
     * 获取URL中的除域名部分
     * 若出现异常，返回原输入的数值
     */
    private fun getUrlWithoutHost(url: String): String {
        return try {
            val uUrl = URL(url)
            url.substringAfter(uUrl.host)
        } catch (e: Exception) {
            TekiLog.e(TAG, "getUrlWithoutHost $url", e)
            url
        }
    }

    /**
     * 将URL转换为文件名（一般是不包含host的url）
     */
    private fun getFileNameFromUrl(url: String): String {
        return Md5Util.getMD5String(url) + "." + getFileExtendFromUrl(url)
    }

    /**
     * 获取文件扩展名
     */
    private fun getFileExtendFromUrl(url: String): String {
        return url.substringAfterLast(".", "")
    }

    private fun getDirectorySize(directory: File): Long {
        var size: Long = 0
        if (directory.isDirectory) {
            val files = directory.listFiles()
            if (files != null) {
                for (file in files) {
                    size += if (file.isDirectory) {
                        getDirectorySize(file)
                    } else {
                        file.length()
                    }
                }
            }
        }
        return size
    }

}