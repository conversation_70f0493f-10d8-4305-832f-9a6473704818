package com.lizhi.component.tekiplayer.engine

import com.lizhi.component.tekiplayer.engine.exception.EngineException
import java.nio.ByteBuffer

/**
 * Desc:
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>@lizhi.fm on 2022/7/27.
 **/
abstract class BufferHolder {
    var timePositionUs: Long = 0
    var position = -1
    var size: Int = 0
    var isEndOfStream = false

    /**
     * 标记PCM数据全为空，仅在输出有意义
     */
    var zeroDataFlag = true
    abstract fun setData(data: ByteArray, offset: Int, size: Int)
    abstract fun setData(data: ByteBuffer)
    abstract fun getDataByteArray(): ByteArray?
    abstract fun getDataByteBuffer(): ByteBuffer?

    protected abstract fun clearData()

    fun clear() {
        clearData()
        timePositionUs = 0
        position = -1
        size = 0
    }
}

abstract class InputBufferHolder : BufferHolder() {

    override fun setData(data: ByteBuffer) {
        throw UnsupportedOperationException("InputBufferHolder setData ByteBuffer not support")
    }
}

abstract class OutputBufferHolder : BufferHolder() {

    override fun getDataByteArray(): ByteArray? {
        throw EngineException(1, "OutputBufferHolder getDataByteArray not support")
    }
}