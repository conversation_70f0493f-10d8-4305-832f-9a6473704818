package com.lizhi.component.tekiplayer.controller.dns

import android.os.Parcel
import android.os.Parcelable
import java.net.InetAddress

/**
 * 文件名：InetAddressWrapper
 * 作用：
 * 作者：huangtianhao
 * 创建日期：2021/5/10
 */
class InetAddressWrapper(val inetAddress: InetAddress) : Parcelable {
    constructor(parcel: Parcel) : this(parcel.readSerializable() as InetAddress)

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeSerializable(inetAddress)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<InetAddressWrapper> {
        override fun createFromParcel(parcel: Parcel): InetAddressWrapper {
            return InetAddressWrapper(parcel)
        }

        override fun newArray(size: Int): Array<InetAddressWrapper?> {
            return arrayOfNulls(size)
        }
    }
}