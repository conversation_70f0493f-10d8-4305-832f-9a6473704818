package com.lizhi.component.tekiplayer.controller.focus

/**
 * 文件名：AudioFocusManager
 * 作用：
 * 作者：huangtianhao
 * 创建日期：2021/4/6
 */
interface AudioFocusManager {

    var enabled: Boolean

    var audioFocusState: Int

    fun request(): Boolean

    fun abandon(): Boolean

    interface AudioFocusChangeListener {
        fun onAudioFocusGained()

        fun onAudioFocusLost(isTransient: Boolean, mayDuck: Boolean)
    }
}