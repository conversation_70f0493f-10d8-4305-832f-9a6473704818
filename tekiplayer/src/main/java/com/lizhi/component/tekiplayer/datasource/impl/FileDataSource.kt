package com.lizhi.component.tekiplayer.datasource.impl

import android.content.Context
import android.net.Uri
import android.os.Bundle
import com.lizhi.component.tekiplayer.datasource.BaseDataSource
import com.lizhi.component.tekiplayer.datasource.DataReader.Companion.READ_END_OF_INPUT
import com.lizhi.component.tekiplayer.datasource.DataReader.Companion.READ_ERROR
import com.lizhi.component.tekiplayer.datasource.DataSource
import com.lizhi.component.tekiplayer.datasource.DataSourceCallback
import com.lizhi.component.tekiplayer.datasource.DataSourceStrategy
import com.lizhi.component.tekiplayer.datasource.Range
import com.lizhi.component.tekiplayer.datasource.exception.FileDataSourceException
import com.lizhi.component.tekiplayer.util.TekiLog
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.io.InputStream
import java.io.RandomAccessFile
import java.net.URI
import javax.crypto.CipherInputStream

/**
 * 文件名：FileDataSource
 * 作用：本地文件数据源
 * 作者：huangtianhao
 * 创建日期：2021/2/22
 */
class FileDataSource(
    context: Context,
    url: String,
    dataSourceCallback: DataSourceCallback?,
    strategy: DataSourceStrategy
) : BaseDataSource(context, url, dataSourceCallback, strategy) {

    class FileDataSourceFactory : BaseDataSource.BaseFactory() {
        override fun create(uri: Uri, extraData: Bundle?): DataSource {
            return FileDataSource(
                checkNotNull(context),
                this.url ?: uri.toString(),
                this.dataSourceCallback,
                this.strategy
            )
        }
    }

    companion object {
        private const val TAG = "FileDataSource"
    }

    private var file: RandomAccessFile? = null
    private var inputStream: InputStream? = null
    private var bytesRemaining = 0L

    override var contentLength: Long? = null

    override val responseHeaders: Map<String, List<String>>?
        get() = null

    override fun open(range: Range): Boolean {
        // 参数校验
        if (!checkArgs(range)) {
            return false
        }

        // 读取文件
        val file = loadLocalFile().also {
            this.file = it
        } ?: return false

        // seek到指定位置，计算需要读的字节数
        try {
            contentLength = file.length()
            file.seek(range.start)
            bytesRemaining = if (range.end == null) {
                file.length() - range.start
            } else {
                range.end - range.start
            }
        } catch (e: IOException) {
            TekiLog.e(TAG, e.message ?: "", e)
            this.dataSourceCallback?.onErrorOccurred(FileDataSourceException(e, e.message))
        }

        if (bytesRemaining < 0) {
            return false
        }

        inputStream = if (hasAesInfo() && !isRealTimeDecrypt()) {
            CipherInputStream(FileInputStream(file.fd), aesComponent!!.decryptCipher)
        } else {
            FileInputStream(file.fd)
        }

        dataSourceCallback?.onReadyRead()

        return true
    }

    override fun read(
        buffer: ByteArray,
        offset: Int,
        readLength: Int
    ): Int {
        return when {
            readLength == 0 -> 0
            bytesRemaining == 0L -> {
                dataSourceCallback?.onEndEncountered()
                READ_END_OF_INPUT
            }
            else -> {
                var bytesRead: Int = READ_ERROR
                try {
                    bytesRead =
                        inputStream?.read(buffer, offset, minOf(bytesRemaining.toInt(), readLength)) ?: 0
                } catch (e: IOException) {
                    TekiLog.e(TAG, e.message ?: "", e)
                    this.dataSourceCallback?.onErrorOccurred(FileDataSourceException(e, e.message))
                }
                bytesRead
            }
        }
    }

    override fun updateDataSourceCallback(dataSourceCallback: DataSourceCallback) {
        this.dataSourceCallback = dataSourceCallback
    }

    private fun checkArgs(range: Range): Boolean {
        if (range.start < 0) {
            this.dataSourceCallback?.onErrorOccurred(
                FileDataSourceException(message = "illegal range ${range.start} < 0")
            )
            return false
        }
        if (originUrl.isBlank()) {
            this.dataSourceCallback?.onErrorOccurred(
                FileDataSourceException(message = "url is blank")
            )
            return false
        }
        return true
    }

    private fun loadLocalFile(): RandomAccessFile? {
        return try {
            RandomAccessFile(File(URI(originUrl)), "r")
        } catch (e: Exception) {
            this.dataSourceCallback?.onErrorOccurred(FileDataSourceException(e, e.message))
            null
        }
    }

    override fun close() {
        try {
            file?.close()
        } catch (ignore: Exception) {

        }
    }

    override fun getUrl(): String {
        return originUrl
    }

}