package com.lizhi.component.tekiplayer.datasource.exception

/**
 * 文件名：InvalidResponseCodeException
 * 作用：非正常响应码
 * 作者：huangtianhao
 * 创建日期：2021/3/17
 */
class InvalidResponseCodeException(
    val url: String,
    val responseCode: Int,
    responseMessage: String
) : HttpDataSourceException(
    code = responseCode,
    message = "Connecting $url response = $responseCode message = $responseMessage"
)