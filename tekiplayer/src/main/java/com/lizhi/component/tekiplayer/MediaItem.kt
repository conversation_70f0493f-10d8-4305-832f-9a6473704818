package com.lizhi.component.tekiplayer

import android.net.Uri
import android.os.Bundle
import android.os.Parcel
import android.os.Parcelable
import android.os.Parcelable.Creator
import com.lizhi.component.tekiplayer.util.ParcelableSparseUriArray
import java.lang.IllegalStateException

/**
 * 文件名：MediaItem
 * 作用：
 * 作者：luo<PERSON><PERSON><EMAIL>
 * 创建日期：2021/2/19
 */
class MediaItem private constructor(
    val tag: String?,
    val uri: Uri,
    startPosition: Long,
    val extraData: Bundle? = null,
    private val qualityUris: ParcelableSparseUriArray,
    internal val fixedDuration: Long,
    internal val category: String? = null
) : Parcelable {

    var pendingStartPositionMs: Long = startPosition
        internal set

    constructor(parcel: Parcel) : this(
        parcel.readString(),
        parcel.readParcelable(Uri::class.java.classLoader)!!,
        parcel.readLong(),
        parcel.readBundle(Bundle::class.java.classLoader),
        parcel.readParcelable<ParcelableSparseUriArray>(ParcelableSparseUriArray::class.java.classLoader)!!,
        parcel.readLong(),
        parcel.readString()
    )

    fun getQualitys(): List<Player.Quality> {
        return (0 until qualityUris.size())
            .map { qualityUris.keyAt(it) }
            .map {
                mapQuality(it)
            }
    }

    private fun mapQuality(it: Int): Player.Quality {
        return when (it) {
            Player.Quality.LOSSLESS.value -> Player.Quality.LOSSLESS
            Player.Quality.SUPER.value -> Player.Quality.SUPER
            Player.Quality.HIGH.value -> Player.Quality.HIGH
            Player.Quality.LOW.value -> Player.Quality.LOW
            else -> Player.Quality.HIGH
        }
    }

    fun getUriOnQuality(quality: Player.Quality): Pair<Player.Quality, Uri> {
        var value = quality.value
        // 先找指定的音质，没有则找比指定低级的音质
        while (value >= 0) {
            val uri = qualityUris[value]
            if (uri == null) {
                value--
            } else {
                return Pair(mapQuality(value), uri)
            }
        }
        // 没有的话再找更高的音质
        value = quality.value + 1
        while (value <= Player.Quality.LOSSLESS.value) {
            val uri = qualityUris[value]
            if (uri == null) {
                value++
            } else {
                return Pair(mapQuality(value), uri)
            }
        }
        throw IllegalStateException("没有找到合适的uri")
    }

    class Builder {
        private var tag: String? = null
        private var uri: Uri? = null
        private var pendingStartPositionMs: Long = 0
        private var extraData: Bundle? = null
        private var qualityUris = ParcelableSparseUriArray()
        private var fixedDuration: Long = -1
        private var category: String? = null

        /**
         * 直接设置的是[Player.Quality.HIGH]的uri
         */
        fun setUri(uri: Uri): Builder {
            this.uri = uri
            qualityUris.put(Player.Quality.HIGH.value, uri)
            return this
        }

        /**
         * 设置指定音质的uri
         */
        fun setUri(quality: Player.Quality, uri: Uri): Builder {
            qualityUris.put(quality.value, uri)
            return this
        }

        fun setSpecifiedDuration(durationMs: Long): Builder {
            fixedDuration = durationMs
            return this
        }

        fun setCategory(category: String): Builder {
            this.category = category
            return this
        }

        fun setTag(tag: String): Builder {
            this.tag = tag
            return this
        }

        fun setExtraData(data: Bundle): Builder {
            this.extraData = data
            return this
        }

        fun setPendingStartPosition(positionMs: Long): Builder {
            this.pendingStartPositionMs = positionMs
            return this
        }

        fun build(): MediaItem {
            if (qualityUris.size() == 0 && uri == null) {
                throw UnsupportedOperationException("uri 不能为空")
            }
            val uri = uri ?: throw UnsupportedOperationException("uri 不能为空")
            return MediaItem(tag, uri, pendingStartPositionMs, extraData, qualityUris, fixedDuration, category)
        }

    }

    override fun equals(other: Any?): Boolean {
        if (other is MediaItem) {
            return other.extraData == extraData && other.pendingStartPositionMs == pendingStartPositionMs && other.uri == uri && other.tag == tag
        }
        return super.equals(other)
    }

    override fun hashCode(): Int {
        return (extraData?.hashCode() ?: 0) + uri.hashCode() + (tag?.hashCode() ?: 0)
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(tag)
        parcel.writeParcelable(uri, flags)
        parcel.writeLong(pendingStartPositionMs)
        parcel.writeBundle(extraData)
        parcel.writeParcelable(qualityUris, flags)
        parcel.writeLong(fixedDuration)
        parcel.writeString(category)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Creator<MediaItem> {
        override fun createFromParcel(parcel: Parcel): MediaItem {
            return MediaItem(parcel)
        }

        override fun newArray(size: Int): Array<MediaItem?> {
            return arrayOfNulls(size)
        }
    }


    override fun toString(): String {
        return "MediaItem{$uri}"
    }
}