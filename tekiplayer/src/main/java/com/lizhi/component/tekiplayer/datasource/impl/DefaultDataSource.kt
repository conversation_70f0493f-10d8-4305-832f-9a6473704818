package com.lizhi.component.tekiplayer.datasource.impl

import android.content.Context
import android.net.Uri
import android.os.Bundle
import com.lizhi.component.tekiplayer.configuration.BufferSizePolicy
import com.lizhi.component.tekiplayer.controller.CacheController
import com.lizhi.component.tekiplayer.datasource.BaseDataSource
import com.lizhi.component.tekiplayer.datasource.CacheableDataSource
import com.lizhi.component.tekiplayer.datasource.DataReader.Companion.READ_ERROR
import com.lizhi.component.tekiplayer.datasource.DataSource
import com.lizhi.component.tekiplayer.datasource.DataSourceCallback
import com.lizhi.component.tekiplayer.datasource.DataSourceStrategy
import com.lizhi.component.tekiplayer.datasource.Range
import com.lizhi.component.tekiplayer.datasource.cache.Cache
import com.lizhi.component.tekiplayer.datasource.cache.CacheMmkvStorage
import com.lizhi.component.tekiplayer.datasource.cache.LruCacheEvictor
import com.lizhi.component.tekiplayer.datasource.cache.SimpleCache
import com.lizhi.component.tekiplayer.datasource.exception.DataSourceNotOpenException
import com.lizhi.component.tekiplayer.datasource.impl.FileDataSource.FileDataSourceFactory
import com.lizhi.component.tekiplayer.util.TekiLog
import java.io.File
import java.net.URI
import java.net.URISyntaxException

/**
 * 文件名：DefaultDataSource
 * 作用：总数据源，负责根据URL选择不同的数据源
 * 作者：huangtianhao
 * 创建日期：2021/3/24
 */
class DefaultDataSource(
    context: Context,
    url: String,
    override var dataSourceCallback: DataSourceCallback? = null,
    override val strategy: DataSourceStrategy,
    private val baseDataSource: DataSource
) : BaseDataSource(context, url, dataSourceCallback, strategy), CacheableDataSource {

    class DefaultDataSourceFactory(private val cachePath: File, private val bufferPolicy: BufferSizePolicy) : BaseDataSource.BaseFactory() {

        private var httpDataSource: DataSource.Factory =
            DefaultHttpDataSource.DefaultHttpDataSourceFactory()

        fun setHttpDataSourceFactory(dataSource: DataSource.Factory) = apply {
            this.httpDataSource = dataSource
        }


        override fun create(uri: Uri, extraData: Bundle?): DataSource {
            val isHighPriority = extraData?.getBoolean(LruCacheEvictor.CACHE_PRIORITY, false) ?: false
            val mmkvStorage = if (isHighPriority) CacheMmkvStorage.getInstance(CacheMmkvStorage.TEKI_PLAYER_HIGH_MMKV_ID) else CacheMmkvStorage.getInstance()
            var path = cachePath
            var maxSpace = bufferPolicy.limitMaxDiskSpaceUsage()
            if (isHighPriority) {
                cachePath.parent?.let { parent ->
                    path = File(parent, LruCacheEvictor.CACHE_PRIORITY_PATH)
                }
                maxSpace = bufferPolicy.limitMaxDiskHighPrioritySpaceUsage()
            }
            var cache: Cache ? = null
            if (!CacheController.isDeleting) { // 避免同时多个对象操作同一文件
                cache = SimpleCache(
                    path,
                    mmkvStorage,
                    LruCacheEvictor(mmkvStorage, path, maxSpace, bufferPolicy.limitFreeDiskSpaceUsage())
                )
            }
            val cacheDataSource = CacheDataSource(
                checkNotNull(context),
                this.url ?: uri.toString(),
                this.dataSourceCallback,
                this.strategy,
                (httpDataSource as BaseFactory).apply {
                    setContext(checkNotNull(context))
                    setUrl(<EMAIL> ?: uri.toString())
                    setDataSourceCallback(<EMAIL>)
                    setStrategy(<EMAIL>)
                }.create(uri),
                cache
            )

            return DefaultDataSource(
                checkNotNull(context),
                url ?: uri.toString(),
                dataSourceCallback,
                strategy,
                cacheDataSource
            )
        }

        fun create(buildBlock: DefaultDataSourceFactory.() -> Unit): DataSource {
            this.buildBlock()
            return this.create(Uri.parse(url))
        }
    }

    companion object {
        private const val TAG = "DefaultDataSource"
    }

    override fun updateDataSourceCallback(dataSourceCallback: DataSourceCallback) {
        this.dataSourceCallback = dataSourceCallback
        currentDataSource?.updateDataSourceCallback(dataSourceCallback)
    }


    private var currentDataSource: DataSource? = null

    override fun open(range: Range): Boolean {
        val currentDataSource = currentDataSource
        if (currentDataSource != null) {
            return currentDataSource.open(range)
        }

        val scheme = try {
            URI(originUrl).scheme
        } catch (e: URISyntaxException) {
            TekiLog.e(TAG, "Error in getting scheme of $originUrl", e)
            return false
        }

        val dataSource = if (isAssetsFile(originUrl)) {
            AssetsDataSource.AssetsDataSourceFactory().apply {
                setContext(context)
                setUrl(originUrl)
            }.create(Uri.parse(originUrl))
        } else if (isLocalFile(scheme)) {
            FileDataSourceFactory().setContext(context).setUrl(originUrl)
                .create(Uri.parse(originUrl))
        } else {
            baseDataSource
            if (baseDataSource is CacheDataSource) {
                baseDataSource.sampler = sampler
            }
            baseDataSource
        }
        dataSourceCallback?.let { dataSource.updateDataSourceCallback(it) }
        this.currentDataSource = dataSource
        this.currentDataSource?.setAesInfo(aesKey, aesIV, isRealTimeDecryption)
        return dataSource.open(range)
    }

    private fun isAssetsFile(originUrl: String): Boolean {
        return originUrl.contains("file:///android_asset")
    }

    private fun isLocalFile(scheme: String): Boolean {
        return scheme == "file"
    }

    override var contentLength: Long? = null
        get() = currentDataSource?.contentLength

    override var responseHeaders: Map<String, List<String>>? = null
        get() = currentDataSource?.responseHeaders
        private set

    override fun read(
        buffer: ByteArray,
        offset: Int,
        readLength: Int
    ): Int {
        checkCurrentDataSourceAndCallback()
        return currentDataSource?.read(
            buffer, offset, readLength
        ) ?: READ_ERROR
    }

    override fun getUrl(): String {
        // checkCurrentDataSourceAndCallback()
        return currentDataSource?.getUrl() ?: originUrl
    }

    override fun close() {
        // checkCurrentDataSourceAndCallback()
        currentDataSource?.close()
    }

    override fun deleteCache(): Boolean {
        val curDataSource = currentDataSource
        return if (curDataSource is CacheableDataSource) {
            curDataSource.deleteCache()
        } else {
            false
        }
    }

    override fun hasCacheOnPosition(position: Long): Boolean {
        val curDataSource = currentDataSource
        return if (curDataSource is CacheableDataSource) {
            curDataSource.hasCacheOnPosition(position)
        } else {
            false
        }
    }

    override fun releaseCache() {
        val curDataSource = currentDataSource
        if (curDataSource is CacheableDataSource) {
            curDataSource.releaseCache()
        }
    }

    private fun checkCurrentDataSourceAndCallback() {
        if (currentDataSource == null) {
            dataSourceCallback?.onErrorOccurred(
                DataSourceNotOpenException()
            )
        }
    }
}