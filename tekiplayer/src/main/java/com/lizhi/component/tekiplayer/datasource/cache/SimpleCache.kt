package com.lizhi.component.tekiplayer.datasource.cache

import com.lizhi.component.basetool.algorithm.Md5Util
import com.lizhi.component.tekiplayer.controller.CacheController
import com.lizhi.component.tekiplayer.datasource.Range
import com.lizhi.component.tekiplayer.datasource.exception.CacheFileNotInitException
import com.lizhi.component.tekiplayer.datasource.exception.IllegalRangeException
import com.lizhi.component.tekiplayer.datasource.getNotNullEndOrThrow
import com.lizhi.component.tekiplayer.util.TekiLog
import java.io.File
import java.io.IOException
import java.io.RandomAccessFile
import java.net.URL
import java.nio.ByteBuffer
import kotlin.math.abs

/**
 * 文件名：SimpleCache
 * 作用：缓存功能实现类
 * 作者：huangtianhao
 * 创建日期：2021/3/18
 */
class SimpleCache(
    private val downloadDirectory: File,
    private val cacheStorage: CacheStorage,
    private val evictor: CacheEvictor
) : Cache {

    companion object {
        private const val TAG = "SimpleCache"

        // 默认持久化写入间隔
        private const val DEFAULT_PERSIST_INTERVAL = 300L

        private const val DEFAULT_BUFFER_SIZE = 8 * 1024
    }

    private var rangeList: MutableList<Range> = ArrayList()

    var randomAccessFile: RandomAccessFile? = null
    var randomAccessFileEncrypt: RandomAccessFile? = null // 加密缓存文件
        private set

    var file: File? = null

    val exposedRangeList: List<Range>
        get() = rangeList

    var rethrowException: Boolean = false

    private var cacheInfo: CacheStorage.CacheInfo? = null

    private var urlWithoutHost: String? = null

    private var aesKey: String? = null
    private var aesIV: String? = null
    private var isRealTimeDecryption: Boolean = false

    override var isOpened: Boolean = false

    /**
     *  根据url获取对应的缓存信息，并且seek文件到指定的位置
     */
    override fun open(
        url: String,
        range: Range?
    ) {
        // 如果此时byteBuffer还有数据的话，强制写入文件或者清空
        if (byteBuffer.position() != 0) {
            // TekiLog.d(TAG, "reopen byteBuffer pos ${byteBuffer.position()} force write to file")
            if (randomAccessFile != null) {
                internalWriteToFile(byteBuffer, force = true)
            } else {
                byteBuffer.clear()
            }
        }

        TekiLog.i(TAG, "open in position ${range?.start}")
        val urlWithoutHost = getUrlWithoutHost(url).also {
            this.urlWithoutHost = it
        }
        CacheController.addUsing(urlWithoutHost)
        TekiLog.d("cache_manager","$urlWithoutHost isUsing true")
        val cacheInfo = this.cacheInfo ?: cacheStorage.get(urlWithoutHost)
        if (cacheInfo != null && this.cacheInfo == null) {
            // 获取到已知的缓存信息
            TekiLog.i(TAG, "open receive cacheInfo = $cacheInfo")
            this.cacheInfo = cacheInfo
            this.rangeList = cacheInfo.rangeList?.toMutableList() ?: ArrayList()
        }

        // 优先使用缓存信息中的文件名
        val fileName = cacheInfo?.fileName ?: getFileNameFromUrl(urlWithoutHost)
        TekiLog.d(TAG, "open ori $urlWithoutHost to $fileName in $downloadDirectory")
        if (!downloadDirectory.exists()) {
            if (!downloadDirectory.mkdirs()) {
                TekiLog.e(TAG, "create [parent path] ${downloadDirectory.path} failed!!")
            }
        }

        val file = File(downloadDirectory, fileName)

        // 有缓存信息，但文件实际不存在时，清除缓存信息
        if (cacheInfo != null && !file.exists()) {
            cacheStorage.remove(urlWithoutHost)
        }

        try {
            fileSeekToPosition(file, range)
        } catch (e: Exception) {
            TekiLog.e(TAG, "failed to seek file to pos $file")
        }

        if (this.cacheInfo == null) {
            // 没有现有的缓存信息，重新创建
            this.cacheInfo = CacheStorage.CacheInfo(fileName, rangeList)
        }

        this.cacheInfo?.let {
            // 通知缓存删除器更新访问时间+检测空间
            evictor.onTouchNewCache(urlWithoutHost, it)
            cacheStorage.save(urlWithoutHost, it)
        }
    }

    /**
     * 获取加密缓存
     */
    override fun obtainEncryptCache(): ByteArray? {
        var byteArray: ByteArray? = null
        createOrGetEncryptFile(false)?.let { newFile ->
            try{
                val length = newFile.length().toInt()
                TekiLog.w(TAG, "obtainEncryptCache length:$length")
                if (length > 0) {
                    byteArray = ByteArray(length)
                    newFile.readFully(byteArray)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } catch(e: Error) {
                e.printStackTrace()
            }
        }
        return byteArray
    }

    /**
     * 将文件seek到指定位置
     */
    private fun fileSeekToPosition(
        file: File,
        range: Range?
    ) {
        if (!file.exists()) {
            file.createNewFile()
        }
        RandomAccessFile(file, "rwd").apply {
            TekiLog.i(TAG, "seek to ${range?.start ?: 0}")
            seek(range?.start ?: 0)
        }.also {
            this.file = file
            this.randomAccessFile = it
            isOpened = true
        }
    }

    override fun close() {
        isOpened = false

        try {
            TekiLog.i(TAG, "close")
            tryToStorageLatestCacheInfo(force = true)
            internalWriteToFile(byteBuffer, force = true)
        } catch (e: IOException) {
            TekiLog.d(TAG, "close exception $e")
            if (rethrowException) {
                throw e
            }
        } finally {
            this.cacheInfo?.let {
                evictor.onLeaveCache(it)
            }
            try {
                randomAccessFile?.close()
                randomAccessFileEncrypt?.close()
            } catch (e: IOException) {
                TekiLog.e(TAG, "close ignored exception $e")
                if (rethrowException) {
                    throw e
                }
            }
        }
        randomAccessFileEncrypt = null
        randomAccessFile = null
    }

    /**
     * 获取缓存信息中的contentLength
     */
    override fun getContentLength(): Long? {
        return cacheInfo?.contentLength
    }

    /**
     * 将contentLength存入缓存信息
     */
    override fun setContentLength(contentLength: Long) {
        cacheInfo?.contentLength = contentLength
        TekiLog.i(TAG, "setContentLength $contentLength")
    }

    /**
     * 对写入的buffer做一层缓存，达到指定大小或者时机后才写入，以减少文件IO
     */
    private var byteBuffer: ByteBuffer = ByteBuffer.allocate(DEFAULT_BUFFER_SIZE)
    private var warningMessage: Int = 0

    override fun write(
        buffer: ByteArray,
        offset: Int,
        writeLength: Int
    ) {
        if (randomAccessFile == null) {
            // 文件创建失败，控制日志打印数
            if (warningMessage++ <= 15) {
                TekiLog.w(
                    TAG,
                    "file is null, maybe failed to createNewFile, skipping write to file.."
                )
            }
            return
        }
        var curOffset = offset
        var needToWriteLength = writeLength - (curOffset - offset)
        while (curOffset != offset + writeLength) {
            if (byteBuffer.hasRemaining()) {
                val readLength = minOf(byteBuffer.remaining(), needToWriteLength)
                byteBuffer.put(buffer, curOffset, readLength)
                curOffset += readLength
                needToWriteLength = writeLength - (curOffset - offset)
            } else {
                internalWriteToFile(byteBuffer)
            }
        }

        // 计算是否是最后一段数据，如果是的话，此时强制写入文件
        try {
//            TekiLog.i(TAG, "sumRangeLength:${sumRangeLength()} byteBuffer.position:${byteBuffer.position()}")
            if (sumRangeLength() + byteBuffer.position() == getContentLength()) {
                TekiLog.i(TAG, "Reached the content length, force write to file")
                internalWriteToFile(byteBuffer)
                deleteEncryptData()
            }
        } catch (e: IllegalRangeException) {
            TekiLog.e(TAG, e.message)
            deleteIllegalRange(e.range)
        }
    }

    private fun createOrGetEncryptFile(isNeedCreate: Boolean): RandomAccessFile? {
        if (null == randomAccessFileEncrypt) {
            generateOrGetEncryptFile(isNeedCreate)?.let {
                randomAccessFileEncrypt = RandomAccessFile(it,"rwd")
            }
        }
        return randomAccessFileEncrypt
    }

    private fun generateOrGetEncryptFile(isNeedCreate: Boolean): File? {
        var fileNew:File? = null
        if (null != this.file) {
            val fileName = this.file!!.name
            val dotIndex = fileName.lastIndexOf(".")
            val newFileName =
                fileName.substring(0, dotIndex) + "_encrypt" + fileName.substring(dotIndex)
            fileNew = File(this.file!!.parent, newFileName)
            if (!fileNew.exists()) {
                if (isNeedCreate) {
                    fileNew.createNewFile()
                } else {
                    return null
                }
            }
            TekiLog.i(TAG, "generateEncryptFile($isNeedCreate) $newFileName")
        }
        return fileNew
    }

    private fun internalWriteToFile(byteBuffer: ByteBuffer, force: Boolean = false) {
        if (byteBuffer.position() == 0) {
            return
        }
        val file = randomAccessFile ?: throw CacheFileNotInitException()
        val beforePos = file.filePointer
        val fileChannel = file.channel
        var afterPos = -1L

        byteBuffer.flip()
        try {
            fileChannel.write(byteBuffer)
            if (force) {
                fileChannel.force(true)
            }
            afterPos = file.filePointer

            // TekiLog.i(TAG, "beforePos = $beforePos afterPos = $afterPos")

            try {
                updateCachedRange(Range(beforePos, afterPos), force)
            } catch (e: IllegalRangeException) {
                TekiLog.e(TAG, e.message)
                deleteIllegalRange(e.range)
            }

        } catch (e: Exception) {
            TekiLog.e(TAG, "write into file error", e)
            throw e
        } finally {
            try {
                if (!Thread.interrupted() && afterPos != beforePos + byteBuffer.position() && byteBuffer.position() != 0) {
                    // 当前文件指针位置与预期不一致时，将文件指针seek到预期位置
                    TekiLog.e(TAG, "Inconsistent position happened")
                    TekiLog.i(
                        TAG,
                        "[afterPos] $afterPos [beforePos] ${beforePos + byteBuffer.position()}"
                    )
                    this.randomAccessFile?.seek(beforePos + byteBuffer.position())
                }
            } catch (e: Exception) {
                TekiLog.e(TAG, "seek inconsistent position error", e)
                TekiLog.i(
                    TAG,
                    "file = ${file.filePointer}, seekTo ${beforePos + byteBuffer.position()}"
                )
                throw e
            }

            byteBuffer.clear()
        }
    }

    override fun read(
        buffer: ByteArray,
        offset: Int,
        readLength: Int
    ): Int {
        if (byteBuffer.position() != 0) {
            TekiLog.d(TAG, "read byteBuffer pos ${byteBuffer.position()} force write to file")
            internalWriteToFile(byteBuffer, force = true)
        }
        val file = randomAccessFile ?: throw CacheFileNotInitException()
        val beforePos = file.filePointer
        return try {
            file.read(buffer, offset, readLength).also {
                // 正常不应该读到-1，如果出现-1，会再从上游读取，这时候要把指针位置重置回去，以便写入
                if (it == -1) {
                    getNextRangeOfPos(beforePos)?.let { range ->
                        deleteIllegalRange(range)
                    }

                    TekiLog.i(TAG, "-1 seek To beforePos $beforePos readLength $readLength")
                    file.seek(beforePos)
                }
            }
        } catch (e: Exception) {
            // 文件不存在，关闭缓存
            if (this.file?.exists() == false) {
                close()
            }
            throw e
        }
    }

    override fun getNextRangeOfPos(position: Long): Range? {
        if (rangeList.isEmpty()) {
            return null
        }
        rangeList.sortBy { it.start }
        return rangeList.find {
            val end = it.end
            end != null && end >= position
        }
    }

    /**
     * Range合并操作
     *
     * 目前对一些异常情况（如重叠Range）有冗余处理
     */
    private fun updateCachedRange(range: Range, force: Boolean) {
        val rangeList = rangeList
        val end = range.getNotNullEndOrThrow()
        rangeList.sortBy { it.start }
        val includeStartRange = rangeList.find {
            it.start <= range.start && (it.getNotNullEndOrThrow()) >= range.start
        }

        val includeEndRange = rangeList.find {
            (it.getNotNullEndOrThrow()) >= end &&
                    end >= it.start
        }

        when {
            includeStartRange != null && includeStartRange == includeEndRange -> {
                // 两段都已经包含了，不需要更新
            }
            includeStartRange == null && includeEndRange != null -> {
                // 前段不包含，更新startRange
                rangeList.remove(includeEndRange)
                addRangeAndRemoveInvalidValue(
                    includeEndRange.copy(
                        start = range.start
                    )
                )
            }
            includeEndRange == null && includeStartRange != null -> {
                // 后段不包含，更新endRange
                rangeList.remove(includeStartRange)
                addRangeAndRemoveInvalidValue(
                    includeStartRange.copy(
                        end = range.end
                    )
                )
            }
            includeEndRange != null && includeStartRange != null -> {
                // 两段都包含，且不相同，合并range
                rangeList.remove(includeStartRange)
                rangeList.remove(includeEndRange)
                addRangeAndRemoveInvalidValue(Range(includeStartRange.start, includeEndRange.end))
            }
            else -> {
                // 两段都为null，独立的范围，直接插入
                rangeList.add(range)
            }
        }

        this.cacheInfo?.rangeList = rangeList
        this.cacheInfo?.plainCacheLength = range.end
        tryToStorageLatestCacheInfo(force = isFullyCached() || force)
    }

    /**
     * 每次添加Range时，检查是否存在中间数据并且移除
     */
    private fun addRangeAndRemoveInvalidValue(range: Range) {
        rangeList.removeAll {
            val res =
                it.start >= range.start && (it.getNotNullEndOrThrow()) <= (range.getNotNullEndOrThrow())
            if (res) {
                TekiLog.e(TAG, "found invalid value $it")
            }
            res
        }
        rangeList.add(range)
    }

    /**
     * 持久化存储缓存分段信息
     */
    private var lastSaveTimestamp: Long? = null
    private fun tryToStorageLatestCacheInfo(force: Boolean) {
        lastSaveTimestamp?.let {
            if (!force && abs(System.currentTimeMillis() - it) < DEFAULT_PERSIST_INTERVAL) {
                return
            }
        }

        lastSaveTimestamp = System.currentTimeMillis()
        storageLatestCacheInfo()
    }

    private fun storageLatestCacheInfo() {
        val cacheInfo = this.cacheInfo ?: return
        val urlWithoutHost = this.urlWithoutHost ?: return
        cacheStorage.save(
            urlWithoutHost, cacheInfo
        )
    }


    /**
     * 移除异常数据
     */
    private fun deleteIllegalRange(range: Range) {
        TekiLog.e(TAG, "deleteIllegalRange $range")
        if (rangeList.contains(range)) {
            rangeList.remove(range)
            tryToStorageLatestCacheInfo(true)
        }
    }

    /**
     * 判断是否完全缓存
     */
    override fun isFullyCached(): Boolean {
        val firstItem = exposedRangeList.firstOrNull() ?: return false
        val contentLength = cacheInfo?.contentLength ?: return false
        return exposedRangeList.size == 1 && firstItem.start == 0L && firstItem.end == contentLength
    }

    override fun deleteCache(): Boolean {
        // 优先使用缓存信息中的文件名
        val fileName = cacheInfo?.fileName ?: return false

        val file =
            File(downloadDirectory, fileName)

        val dotIndex = fileName.lastIndexOf(".")
        val newFileName =
            fileName.substring(0, dotIndex) + "_encrypt" + fileName.substring(dotIndex)
        val encryptFile = File(downloadDirectory, newFileName)
        if (encryptFile.exists()) {
            encryptFile.delete()
            TekiLog.w(TAG, "deleteCache $newFileName")
        }

        if (file.exists()) {
            rangeList.clear()
            byteBuffer.clear()
            tryToStorageLatestCacheInfo(force = true)
            TekiLog.w(TAG, "deleteCache $fileName")
            return file.delete()
        }

        return false
    }

    /**
     * 获取URL中的除域名部分
     * 若出现异常，返回原输入的数值
     */
    private fun getUrlWithoutHost(url: String): String {
        return try {
            val uUrl = URL(url)
            url.substringAfter(uUrl.host)
        } catch (e: Exception) {
            TekiLog.e(TAG, "getUrlWithoutHost $url", e)
            url
        }
    }

    /**
     * 将URL转换为文件名（一般是不包含host的url）
     */
    fun getFileNameFromUrl(url: String): String {
        return Md5Util.getMD5String(url) + "." + getFileExtendFromUrl(url)
    }

    /**
     * 获取文件扩展名
     */
    private fun getFileExtendFromUrl(url: String): String {
        return url.substringAfterLast(".", "")
    }

    /**
     * 计算当前缓存的长度
     */
    private fun sumRangeLength(): Long {
        return rangeList.sumOf { maxOf(0, it.getNotNullEndOrThrow() - it.start) }
    }

    override fun setAesInfo(aesKey: String?, aesIV: String?, isRealTimeDecrypt: Boolean) {
        this.aesIV = aesIV
        this.aesKey = aesKey
        this.isRealTimeDecryption = isRealTimeDecrypt
    }

    override fun hasAesInfo(): Boolean {
        return null != aesIV && null != aesKey
    }

    override fun hasOfflineEncrypt(): Boolean {
        return hasAesInfo() && !isRealTimeDecryption
    }

    override fun saveEncryptData(ba: ByteArray) {
        // 保存加密文件
        if (hasOfflineEncrypt()) {
            createOrGetEncryptFile(true)?.let { encryptFile ->
                try {
                    encryptFile.write(ba)
//                    TekiLog.i(TAG, "write into encrypt file ${encryptFile?.filePointer}")
                } catch (e: Exception) {
                    e.printStackTrace()
                } catch(e: Error) {
                    e.printStackTrace()
                }
            }
        }
    }

    override fun deleteEncryptData() {
        if (hasOfflineEncrypt()) {
            generateOrGetEncryptFile(false)?.let {
                if (it.exists()) {
                    TekiLog.i(TAG, "deleteEncryptData ${it.name}")
                    val result = it.delete()
                    TekiLog.i(TAG, "deleteEncryptData result:$result")
                }
            }
        }
    }

    override fun getPlainCacheLength(): Long {
        return this.cacheInfo?.plainCacheLength ?: 0
    }

    override fun getPlainCacheTotalLength(): Long {
        this.cacheInfo?.plainCacheLength?.let {
            return it + byteBuffer.position()
        }
        return byteBuffer.position().toLong()
    }

    override fun releaseCache() {
        urlWithoutHost?.let {
            CacheController.removeUsing(it)
            TekiLog.d("cache_manager","$it. isUsing false")
        }
    }
}