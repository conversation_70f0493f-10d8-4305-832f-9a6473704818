package com.lizhi.component.tekiplayer.engine.decode.codec

import com.lizhi.component.tekiplayer.engine.InputBufferHolder
import com.lizhi.component.tekiplayer.engine.exception.UnSupportFormatException
import java.nio.ByteBuffer

class MediaCodecInputBufferHolder : InputBufferHolder() {
    var index: Int = -1

    private var buffer: ByteBuffer? = null

    override fun setData(data: ByteArray, offset: Int, size: Int) {
        this.buffer?.put(data, offset, size)
        buffer?.flip()
    }
    
    override fun setData(data: ByteBuffer) {
        this.buffer = data
    }

    override fun getDataByteArray(): ByteArray? {
        throw UnSupportFormatException("MediaCodecInputBufferHolder getDataByteArray not support")
    }

    override fun getDataByteBuffer(): ByteBuffer? {
        throw UnSupportFormatException("MediaCodecInputBufferHolder getDataByteBuffer not support")
    }

    override fun clearData() {
        buffer?.clear()
        buffer = null
    }
}