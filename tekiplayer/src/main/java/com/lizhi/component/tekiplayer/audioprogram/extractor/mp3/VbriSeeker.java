/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.lizhi.component.tekiplayer.audioprogram.extractor.mp3;

import android.util.Log;
import androidx.annotation.Nullable;
import com.lizhi.component.tekiplayer.audioprogram.extractor.ParsableByteArray;
import com.lizhi.component.tekiplayer.audioprogram.extractor.SeekPoint;
import com.lizhi.component.tekiplayer.audioprogram.extractor.Seeker;
import com.lizhi.component.tekiplayer.util.Util;
import com.lizhi.component.tekiplayer.util.audio.MpegAudioUtil;

import static java.lang.Math.max;

/** MP3 seeker that uses metadata from a VBRI header. */
/* package */ final class VbriSeeker implements Seeker {

    private static final String TAG = "VbriSeeker";

    /**
     * Returns a {@link VbriSeeker} for seeking in the stream, if required information is present.
     * Returns {@code null} if not. On returning, {@code frame}'s position is not specified so the
     * caller should reset it.
     *
     * @param inputLength The length of the stream in bytes, or -1 if unknown.
     * @param position The position of the start of this frame in the stream.
     * @param mpegAudioHeader The MPEG audio header associated with the frame.
     * @param frame The data in this audio frame, with its position set to immediately after the
     * 'VBRI' tag.
     * @return A {@link VbriSeeker} for seeking in the stream, or {@code null} if the required
     * information is not present.
     */
    @Nullable
    public static VbriSeeker create(
        long inputLength,
        long position,
        MpegAudioUtil.Header mpegAudioHeader,
        ParsableByteArray frame) {
        frame.skipBytes(10);
        int numFrames = frame.readInt();
        if (numFrames <= 0) {
            return null;
        }
        int sampleRate = mpegAudioHeader.sampleRate;
        long durationUs = Util.scaleLargeTimestamp(numFrames,
            Util.MICROS_PER_SECOND * (sampleRate >= 32000 ? 1152 : 576), sampleRate);
        int entryCount = frame.readUnsignedShort();
        int scale = frame.readUnsignedShort();
        int entrySize = frame.readUnsignedShort();
        frame.skipBytes(2);

        long minPosition = position + mpegAudioHeader.frameSize;
        // Read table of contents entries.
        long[] timesUs = new long[entryCount];
        long[] positions = new long[entryCount];
        for (int index = 0; index < entryCount; index++) {
            timesUs[index] = (index * durationUs) / entryCount;
            // Ensure positions do not fall within the frame containing the VBRI header. This constraint
            // will normally only apply to the first entry in the table.
            positions[index] = max(position, minPosition);
            int segmentSize;
            switch (entrySize) {
                case 1:
                    segmentSize = frame.readUnsignedByte();
                    break;
                case 2:
                    segmentSize = frame.readUnsignedShort();
                    break;
                case 3:
                    segmentSize = frame.readUnsignedInt24();
                    break;
                case 4:
                    segmentSize = frame.readUnsignedIntToInt();
                    break;
                default:
                    return null;
            }
            position += segmentSize * scale;
        }
        if (inputLength != -1 && inputLength != position) {
            Log.w(TAG, "VBRI data size mismatch: " + inputLength + ", " + position);
        }
        return new VbriSeeker(timesUs, positions, durationUs, /* dataEndPosition= */ position);
    }

    private final long[] timesUs;
    private final long[] positions;
    private final long durationUs;
    private final long dataEndPosition;

    private VbriSeeker(long[] timesUs, long[] positions, long durationUs, long dataEndPosition) {
        this.timesUs = timesUs;
        this.positions = positions;
        this.durationUs = durationUs;
        this.dataEndPosition = dataEndPosition;
    }

    @Override
    public SeekPoint getPositionAtTimeUs(long timeUs) {
        int tableIndex = Util.binarySearchFloor(timesUs, timeUs, true, true);
        long timesUs = this.timesUs[tableIndex];
        long position = positions[tableIndex];
        if (timesUs >= timeUs || tableIndex == this.timesUs.length - 1) {
            return new SeekPoint(timeUs, position);
        } else {
            return new SeekPoint(this.timesUs[tableIndex + 1], positions[tableIndex + 1]);
        }
    }

    @Override
    public long getTimeUs(long position) {
        return timesUs[Util.binarySearchFloor(positions, position, true, true)];
    }

    @Override
    public long getDurationUs() {
        return durationUs;
    }

    @Override
    public long getDataEndPosition() {
        return dataEndPosition;
    }
}
