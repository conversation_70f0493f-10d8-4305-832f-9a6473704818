package com.lizhi.component.tekiplayer.datasource

import java.io.IOException
import kotlin.Throws

/**
 * 文件名：DataReader
 * 作用：数据读取接口
 * 作者：huangtianhao
 * 创建日期：2021/3/16
 */
interface DataReader {

    companion object {
        // 读到末尾
        const val READ_END_OF_INPUT = -1

        // 读取发生错误
        const val READ_ERROR = -2
    }

    /**
     * 往buffer内写入读取到的数据
     *
     * 正常时返回读取的实际字节长度，
     * 异常时可能会返回[READ_END_OF_INPUT]、[READ_ERROR]
     */
    @Throws(IOException::class)
    fun read(
        buffer: ByteArray,
        offset: Int,
        readLength: Int
    ): Int

}