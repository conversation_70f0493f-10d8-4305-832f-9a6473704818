package com.lizhi.component.tekiplayer.datasource.exception

import com.lizhi.component.tekiplayer.ERR_DATASOURCE_HTTP_CONNECTION

/**
 * 文件名：FileDataSourceException
 * 作用：HTTP数据源抛出的异常信息
 * 作者：huangtianhao
 * 创建日期：2021/3/15
 */
open class HttpDataSourceException(
    code: Int = ERR_DATASOURCE_HTTP_CONNECTION, message: String? = null, cause: Throwable? = null
) : DataSourceException(code, message?: cause?.message ?: "HttpDataSourceException", cause)