package com.lizhi.component.tekiplayer.util

import com.google.gson.Gson
import com.google.gson.JsonSyntaxException

/**
 * 文件名：TekiGson
 * 作用：Json序列化工具类
 * 作者：huangtianhao
 * 创建日期：2021/3/22
 */
internal object TekiGson {
    val gson = Gson()
}


internal fun Any?.toJson(): String? {
    return if (this == null) {
        null
    } else {
        TekiGson.gson.toJson(this)
    }
}

internal inline fun <reified T> String?.fromJson(): T? {
    return if (this.isNullOrEmpty()) {
        null
    } else {
        try {
            TekiGson.gson.fromJson(this, T::class.java)
        } catch (e: JsonSyntaxException) {
            null
        }
    }
}