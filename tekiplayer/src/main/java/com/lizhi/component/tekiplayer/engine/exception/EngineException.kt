package com.lizhi.component.tekiplayer.engine.exception

import com.lizhi.component.tekiplayer.*
import java.io.IOException
import java.net.ConnectException
import java.net.SocketException
import java.net.SocketTimeoutException

/**
 * 文件名：EngineException
 * 作用：
 * 作者：l<PERSON><PERSON><PERSON><PERSON>@lizhi.fm
 * 创建日期：2021/3/31
 */
open class EngineException(
    code: Int = ERR_ENGINE,
    message: String? = null,
    cause: Throwable? = null
) : TekiException(code, message ?: "EngineException", cause)

open class RecoverableEngineException(
    code: Int = ERR_ENGINE,
    message: String? = null,
    cause: Throwable? = null
) : TekiException(code, message ?: "RecoverableEngineException", cause)

fun parseExceptionCodeAndMessage(error: Throwable): Pair<Int, String> {
    var code = -1
    val msg: String
    val cause = error.cause
    when {
        error is SocketTimeoutException -> {
            code = ERR_DATASOURCE_HTTP_TIMEOUT
            msg = "${error.message} bytesTransferred=${error.bytesTransferred}"
        }
        error is SocketException -> {
            code = ERR_DATASOURCE_HTTP_IO
            msg = error.message ?: "SocketException"
        }
        error is ConnectException -> {
            code = ERR_DATASOURCE_HTTP_CONNECTION
            msg = error.message ?: "ConnectException"
        }
        error is IOException && error.cause == null -> {
            code = 0
            msg = error.message ?: "IOException"
        }
        error is TekiException -> {
            code = error.code
            msg = getStackTrace(cause ?: error) ?: error.message
        }
        cause is TekiException -> {
            code = cause.code
            msg = getStackTrace(cause.cause ?: cause) ?: cause.message
        }
        else -> {
            msg = getStackTrace(cause ?: error) ?: "other exception"
        }
    }
    return Pair(code, msg)
}

// get first and second line of stack trace and message
//
fun getStackTrace(error: Throwable?): String? {
    if (error == null) {
        return null
    }
    val stackTrace = error.stackTrace
    val sb = StringBuilder()
    if (stackTrace.isNotEmpty()) {
        sb.append(stackTrace[0].toString())
        if (stackTrace.size > 1) {
            sb.append(" -> ")
            sb.append(stackTrace[1].toString())
        }
    }
    return "message: ${error.message}, stack: $sb"
}