package com.lizhi.component.tekiplayer.analyzer

import android.os.Bundle

/**
 * 文件名：Reporter
 * 作用：事件打点
 * 作者：huang<PERSON>hao
 * 创建日期：2021/2/20
 */
interface Reporter {

    companion object {
        const val REPORT_STATUS_INIT = 1
        const val REPORT_STATUS_SEEK = 2
        const val REPORT_STATUS_EMPTY = 3
        const val REPORT_STATUS_END = 4

        const val REPORT_KEY_TRACE_ID = "msgTraceId"
    }

    fun onBuffering()

    fun onPlaying(playbackDuration: Long)

    fun onPlayEnd(positionMs: Long, playbackDuration: Long)

    fun onWeakNetworkChecked(@BandwidthQuality.Level qualityLevel: Int, speed: Int, onError: Boolean?)

    fun onError(exception: Throwable, recoverable: Boolean, extraData: Bundle? = null)

    fun onForceStop()

    fun onTransactionEnd()
}