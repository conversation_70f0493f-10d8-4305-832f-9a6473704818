package com.lizhi.component.tekiplayer.controller.cdn

import com.lizhi.component.tekiplayer.util.IPUtils
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 文件名：CdnListHolder
 * 作用：存储CdnList单例
 * 作者：huangtianhao
 * 创建日期：2021/4/29
 */
object CdnListHolder {
    private var mutableCdnList: MutableList<String> = ArrayList()

    // 对外暴露不可变列表
    val cdnList: List<String>
        get() = mutableCdnList

    private var cdnListChangeListeners = CopyOnWriteArrayList<CdnListListener>()

    fun addListener(cdnListListener: CdnListListener) {
        this.cdnListChangeListeners.add(cdnListListener)
        cdnListListener.onCdnListChanged(cdnList)
    }

    fun removeListener(cdnListListener: CdnListListener) {
        this.cdnListChangeListeners.remove(cdnListListener)
    }

    fun updateCdnList(list: List<String>) {
        // 过滤IP地址（暂只判断ipv4），只保留域名
        mutableCdnList = ArrayList(list.filter { !IPUtils.isIpv4(it) })
        notifyChanged()
    }

    private fun notifyChanged() {
        this.cdnListChangeListeners.forEach {
            it.onCdnListChanged(cdnList)
        }
    }
}