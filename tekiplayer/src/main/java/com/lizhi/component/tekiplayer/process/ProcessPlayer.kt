package com.lizhi.component.tekiplayer.process

import android.app.Service
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import com.lizhi.component.tekiplayer.*
import com.lizhi.component.tekiplayer.TekiPlayer.Builder
import com.lizhi.component.tekiplayer.util.TekiLog
import java.util.*

/**
 * 文件名：ProcessPlayer
 * 作用：
 * 作者：luo<PERSON><EMAIL>
 * 创建日期：2021/4/6
 */
class ProcessPlayerBuilder(
    private val builder: Builder,
    private val clazz: Class<out PlayerService>
) {
    companion object {
        const val TAG = "ProcessPlayerBuilder"
    }

    private val context = builder.context

    fun get(): TekiPlayer {
        return ProcessPlayer.singlePlayerInstance?.also {
            TekiLog.w(TAG, "use exists player")
        } ?: ProcessPlayer(
            context, clazz, builder
        )
    }

}

private class ProcessPlayer internal constructor(
    context: Context,
    clazz: Class<out PlayerService>,
    private val builder: Builder
) : TekiPlayer(
    context,
    builder.cachePath,
    builder.bufferPolicy,
    builder.recordPosition,
    builder.extractorsFactory,
    builder.dnsResolver,
    builder.cdnUrlPattern,
    builder.cacheDuration
) {

    companion object {
        internal var singlePlayerInstance: ProcessPlayer? = null
    }

    private val mementoList = LinkedList<Pair<String, Any>>()
    private val playerDispatcher: PlayerEventDispatcher by lazy { PlayerEventDispatcher() }

    private var proxyPlayer: IPlayer? = null

    private val serviceConnection = object : ServiceConnection {
        override fun onServiceDisconnected(name: ComponentName?) {
            TekiLog.w(TAG, "process player disconnected~~")
            proxyPlayer = null
        }

        override fun onServiceConnected(
            name: ComponentName?,
            service: IBinder?
        ) {
            TekiLog.w(TAG, "process player connected")
            val proxyPlayer = IPlayer.Stub.asInterface(service)
            <EMAIL> = proxyPlayer
            // 传递配置，创建播放器
            proxyPlayer.updateConfiguration(
                builder.recordPosition,
                builder.preBufferSize,
                builder.autoPrepareMediaCount,
                builder.maxBufferSizeOnWifi,
                builder.maxBufferSizeOn5G,
                builder.maxBufferSizeOn4G,
                builder.maxBufferSizeOn3G,
                builder.maxBufferSizeOn2G,
                builder.shouldLoadingThresholds,
                builder.cdnUrlPattern,
                builder.cacheDuration
            )
            //
            proxyPlayer.addPlayListEventListener(object : IPlayEventListener.Stub() {
                override fun onPlaybackRemoveOnList() {
                    playerDispatcher.onPlaybackRemoveOnList()
                }

                override fun onPlaybackChange(
                    index: Int,
                    item: MediaItem?,
                    lastPosition: Long,
                    reason: Int
                ) {
                    playerDispatcher.onPlaybackChange(index, item, lastPosition, reason)
                }

                override fun onPlayListUpdate() {
                    playerDispatcher.onPlayListUpdate()
                }

                override fun onError(errCode: Int, message: String) {
                    playerDispatcher.onError(errCode, message)
                }

                override fun onPlaybackStateChange(status: Int) {
                    playerDispatcher.onPlaybackStateChange(status)
                }

                override fun onTimeRemainingUpdate(
                    index: Int,
                    item: MediaItem?,
                    remainingMs: Long
                ) {
                    playerDispatcher.onTimeRemainingUpdate(index, item, remainingMs)
                }

                override fun onItemRemainingUpdate(index: Int, item: MediaItem?, itemCount: Int) {
                    playerDispatcher.onItemRemainingUpdate(index, item, itemCount)
                }

                override fun onBufferedPositionUpdate(
                    index: Int,
                    item: MediaItem?,
                    bufferPosition: Long
                ) {
                    playerDispatcher.onBufferedPositionUpdate(index, item, bufferPosition)
                }

                override fun onPlayedPositionUpdate(index: Int, item: MediaItem?, position: Long) {
                    playerDispatcher.onPlayedPositionUpdate(index, item, position)
                }

                override fun onNetworkQualitySample(quality: Int, speedBps: Int) {
                    playerDispatcher.onWatch(quality, speedBps)
                }

                override fun onPlayListFinished() {
                    playerDispatcher.onPlayListFinished()
                }

                override fun onPlayZeroItem(item: MediaItem?) {
                    playerDispatcher.onPlayZeroItem(item)
                }
            })
            //
            mementoList.forEach {
                kotlin.runCatching {
                    resumeWhenPlayerCreated(it, proxyPlayer)
                }.onFailure { e ->
                    TekiLog.e(
                        "onServiceConnected",
                        "resumeWhenPlayerCreated fail when call ${it.first}",
                        e
                    )
                }
            }
            mementoList.clear()
        }
    }

    override var repeatMode: Int = REPEAT_MODE_OFF
        get() = proxyPlayer?.repeatMode ?: REPEAT_MODE_OFF
        set(value) {
            field = value
            proxyPlayer?.let {
                it.repeatMode = value
            } ?: mementoList.add(Pair("setRepeatMode", value))
        }

    override var speed: Float = 1.0F
        get() = proxyPlayer?.speed ?: 1.0F
        set(value) {
            field = value
            proxyPlayer?.speed = field
        }
    override var volume: Float = 1.0F
        get() = proxyPlayer?.volume ?: 1.0F
        set(value) {
            field = value
            proxyPlayer?.volume = field
        }

    init {
        context.bindService(Intent(context, clazz), serviceConnection, Service.BIND_AUTO_CREATE)
    }

    override fun addMediaItem(mediaItem: MediaItem) {
        proxyPlayer?.addMediaItem(mediaItem) ?: mementoList.add(Pair("addMediaItem", mediaItem))
    }

    override fun addMediaItem(
        index: Int,
        mediaItem: MediaItem
    ) {
        proxyPlayer?.addMediaItemAt(index, mediaItem) ?: mementoList.add(
            Pair(
                "addMediaItemAt",
                Pair(index, mediaItem)
            )
        )
    }

    override fun addMediaItem(list: List<MediaItem>) {
        proxyPlayer?.addMediaItemList(list) ?: mementoList.add(Pair("addMediaItemList", list))
    }

    override fun addMediaItem(
        index: Int,
        list: List<MediaItem>
    ) {
        proxyPlayer?.addMediaItemListAt(index, list)
            ?: mementoList.add(Pair("addMediaItemListAt", Pair(index, list)))
    }

    override fun seekTo(index: Int, percent: Float) {
        proxyPlayer?.seekToIndex(index, percent) ?: mementoList.add(Pair("seekToIndex", Pair(index, percent)))
    }

    override fun removeRange(
        startIndex: Int,
        length: Int
    ) {
        proxyPlayer?.removeRange(startIndex, length) ?: mementoList.add(
            Pair(
                "removeRange",
                Pair(startIndex, length)
            )
        )
    }

    override fun removeItem(item: MediaItem) {
        proxyPlayer?.removeItem(item) ?: mementoList.add(Pair("removeItem", item))
    }

    override fun removeItemAt(index: Int) {
        proxyPlayer?.removeItemAt(index) ?: mementoList.add(Pair("removeItemAt", index))
    }

    override fun getMediaItem(index: Int): MediaItem? {
        return proxyPlayer?.getMediaItem(index)
    }

    override fun getMediaItemList(): List<MediaItem> {
        return proxyPlayer?.mediaItemList ?: emptyList()
    }

    override fun clear() {
        proxyPlayer?.clear() ?: mementoList.add(Pair("clear", Any()))
    }

    override fun prepare() {
        proxyPlayer?.prepare() ?: mementoList.add(Pair("prepare", Any()))
    }

    override fun play() {
        proxyPlayer?.play() ?: mementoList.add(Pair("play", Any()))
    }

    override fun play(index: Int) {
        proxyPlayer?.playAt(index) ?: mementoList.add(Pair("playAt", index))
    }

    override fun playNext() {
        proxyPlayer?.playNext() ?: mementoList.add(Pair("playNext", Any()))
    }

    override fun playPrevious() {
        proxyPlayer?.playPrevious() ?: mementoList.add(Pair("playPrevious", Any()))
    }

    override fun pause() {
        proxyPlayer?.pause() ?: mementoList.add(Pair("pause", Any()))
    }

    override fun resume() {
        proxyPlayer?.resume() ?: mementoList.add(Pair("resume", Any()))
    }

    override fun stop() {
        proxyPlayer?.stop() ?: mementoList.add(Pair("stop", Any()))
    }

    override fun seekTo(positionMs: Long) {
        proxyPlayer?.seekTo(positionMs) ?: mementoList.add(Pair("seekTo", positionMs))
    }

    override fun seekBy(relativeMs: Long) {
        proxyPlayer?.seekBy(relativeMs) ?: mementoList.add(Pair("seekBy", relativeMs))
    }

    override fun getPosition(): Long {
        return proxyPlayer?.position ?: -1
    }

    override fun getCurrentIndexOnList(): Int {
        return proxyPlayer?.currentIndexOnList ?: -1
    }

    override fun getItemRemainingBeforeStop(): Int {
        return proxyPlayer?.itemRemainingBeforeStop ?: 0
    }

    override fun getTimeRemainingBeforeStop(): Long {
        return proxyPlayer?.timeRemainingBeforeStop ?: 0
    }

    override fun getAudioQuality(): Player.Quality {
        return when (proxyPlayer?.audioQuality ?: Player.Quality.HIGH.value) {
            Player.Quality.LOW.value -> Player.Quality.LOW
            Player.Quality.HIGH.value -> Player.Quality.HIGH
            Player.Quality.SUPER.value -> Player.Quality.SUPER
            Player.Quality.LOSSLESS.value -> Player.Quality.LOSSLESS
            else -> Player.Quality.HIGH
        }
    }

    override fun getCurrentMediaItem(): MediaItem? {
        return proxyPlayer?.currentMediaItem
    }

    override fun getBufferedPosition(): Long {
        return proxyPlayer?.bufferedPosition ?: -1
    }

    override fun getStatus(): Int {
        return proxyPlayer?.status ?: -1
    }

    override fun getDuration(): Long {
        return proxyPlayer?.duration ?: -1
    }

    override fun hasNext(): Boolean {
        return proxyPlayer?.hasNext() ?: false
    }

    override fun hasPrevious(): Boolean {
        return proxyPlayer?.hasPrevious() ?: false
    }

    override fun stopAtTime(absoluteTimestamp: Long) {
        proxyPlayer?.stopAtTime(absoluteTimestamp) ?: mementoList.add(
            Pair(
                "stopAtTime",
                absoluteTimestamp
            )
        )
    }

    override fun stopAfterTime(timeMs: Long) {
        proxyPlayer?.stopAfterTime(timeMs) ?: mementoList.add(Pair("stopAfterTime", timeMs))
    }

    override fun stopAfterMediaItemFinish(itemCount: Int) {
        proxyPlayer?.stopAfterMediaItemFinish(itemCount)
            ?: mementoList.add(Pair("stopAfterMediaItemFinish", itemCount))
    }

    override fun cancelStop() {
        proxyPlayer?.cancelStop() ?: mementoList.add(Pair("cancelStop", Any()))
    }

    override fun addPlayEventListener(listener: PlayEventListener) {
        playerDispatcher.addPlayListEventListener(listener)
    }

    override fun removePlayEventListener(listener: PlayEventListener) {
        playerDispatcher.removePlayListEventListener(listener)
    }

    override fun setCdnList(cdnList: List<String>) {
        proxyPlayer?.setCdnList(cdnList) ?: mementoList.add(Pair("setCdnList", cdnList))
    }

    override fun setAudioQuality(quality: Player.Quality) {
        super.setAudioQuality(quality)
        proxyPlayer?.setAudioQuality(quality.value) ?: mementoList.add(
            Pair(
                "setAudioQuality",
                quality.value
            )
        )
    }

    override fun getActualAudioQuality(): Player.Quality {
        return mapQuality(proxyPlayer?.actualQuality) ?: super.getActualAudioQuality()
    }

    override fun addNetworkQualityWatcher(watcher: (qualityLevel: Int, speedBps: Int) -> Unit) {
        playerDispatcher.addNetworkWatcher(watcher)
    }

    override fun removeNetworkQualityWatcher(watcher: (qualityLevel: Int, speedBps: Int) -> Unit) {
        playerDispatcher.removeNetworkWatcher(watcher)
    }

    private fun mapQuality(it: Int?): Player.Quality? {
        return when (it) {
            null -> null
            Player.Quality.LOSSLESS.value -> Player.Quality.LOSSLESS
            Player.Quality.SUPER.value -> Player.Quality.SUPER
            Player.Quality.HIGH.value -> Player.Quality.HIGH
            Player.Quality.LOW.value -> Player.Quality.LOW
            else -> Player.Quality.HIGH
        }
    }

    private fun resumeWhenPlayerCreated(
        it: Pair<String, Any>,
        proxyPlayer: IPlayer
    ) {
        when (it.first) {
            "addMediaItem" -> {
                proxyPlayer.addMediaItem(it.second as MediaItem?)
            }
            "addMediaItemAt" -> {
                val params = it.second as Pair<Int, MediaItem>
                proxyPlayer.addMediaItemAt(params.first, params.second)
            }
            "addMediaItemList" -> {
                proxyPlayer.addMediaItemList(it.second as List<MediaItem>)
            }
            "addMediaItemListAt" -> {
                val params = it.second as Pair<Int, List<MediaItem>>
                proxyPlayer.addMediaItemListAt(params.first, params.second)
            }
            "removeRange" -> {
                val params = it.second as Pair<Int, Int>
                proxyPlayer.removeRange(params.first, params.second)
            }
            "removeItem" -> {
                proxyPlayer.removeItem(it.second as MediaItem?)
            }
            "removeItemAt" -> {
                proxyPlayer.removeItemAt(it.second as Int)
            }
            "prepare" -> {
                proxyPlayer.prepare()
            }
            "play" -> {
                proxyPlayer.play()
            }
            "playAt" -> {
                proxyPlayer.playAt(it.second as Int)
            }
            "pause" -> {
                proxyPlayer.pause()
            }
            "stop" -> {
                proxyPlayer.stop()
            }
            "playNext" -> {
                proxyPlayer.playNext()
            }
            "playPrevious" -> {
                proxyPlayer.playPrevious()
            }
            "resume" -> {
                proxyPlayer.resume()
            }
            "seekTo" -> {
                proxyPlayer.seekTo(it.second as Long)
            }
            "seekBy" -> {
                proxyPlayer.seekBy(it.second as Long)
            }
            "stopAtTime" -> {
                proxyPlayer.stopAtTime(it.second as Long)
            }
            "stopAfterTime" -> {
                proxyPlayer.stopAfterTime(it.second as Long)
            }
            "stopAfterMediaItemFinish" -> {
                proxyPlayer.stopAfterMediaItemFinish(it.second as Int)
            }
            "cancelStop" -> {
                proxyPlayer.cancelStop()
            }
            "setCdnList" -> {
                proxyPlayer.setCdnList(it.second as MutableList<String>?)
            }
            "setAudioQuality" -> {
                proxyPlayer.audioQuality = it.second as Int
            }
            "seekToIndex" -> {
                val params = it.second as Pair<Int, Float>
                proxyPlayer.seekToIndex(params.first, params.second)
            }
        }
    }
}
