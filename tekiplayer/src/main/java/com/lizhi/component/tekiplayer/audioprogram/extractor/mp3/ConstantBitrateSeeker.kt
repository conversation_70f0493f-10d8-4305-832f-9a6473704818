/*
 * Copyright (C) 2018 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.lizhi.component.tekiplayer.audioprogram.extractor.mp3

import com.lizhi.component.tekiplayer.audioprogram.extractor.SeekPoint
import com.lizhi.component.tekiplayer.audioprogram.extractor.Seeker
import com.lizhi.component.tekiplayer.util.Util
import kotlin.math.max

/**
 * A [] implementation that assumes the stream has a constant bitrate and consists of
 * multiple independent frames of the same size. Seek points are calculated to be at frame
 * boundaries.
 */
/**
 * Constructs a new instance from a stream.
 *
 * @param inputLength The length of the stream in bytes, or -1 if unknown.
 * @param firstFrameBytePosition The byte-position of the first frame in the stream.
 * @param bitrate The bitrate (which is assumed to be constant in the stream).
 * @param frameSize The size of each frame in the stream in bytes. May be -1
 * if unknown.
 */
class ConstantBitrateSeeker(
    private val inputLength: Long,
    private val firstFrameBytePosition: Long,
    private val bitrate: Int,
    frameSize: Int
) : Seeker {

    private val frameSize: Int = if (frameSize == -1) 1 else frameSize
    private var dataSize: Long = 0
    override var durationUs: Long = 0

    init {
        if (inputLength == -1L) {
            dataSize = -1
            durationUs = -1
        } else {
            dataSize = inputLength - firstFrameBytePosition
            durationUs = getTimeUsAtPosition(inputLength, firstFrameBytePosition, bitrate)
        }
    }

    override fun getPositionAtTimeUs(timeUs: Long): SeekPoint {
        if (dataSize == -1L) {
            return SeekPoint(0L, firstFrameBytePosition)
        }
        val seekFramePosition = getFramePositionForTimeUs(timeUs)
        val seekTimeUs = getTimeUsAtPosition(seekFramePosition)
        return if (seekTimeUs >= timeUs || seekFramePosition + frameSize >= inputLength) {
            SeekPoint(seekTimeUs, seekFramePosition)
        } else {
            val secondSeekPosition = seekFramePosition + frameSize
            val secondSeekTimeUs = getTimeUsAtPosition(secondSeekPosition)
            SeekPoint(secondSeekTimeUs, secondSeekPosition)
        }
    }

    override fun getTimeUs(position: Long): Long {
        return getTimeUsAtPosition(position)
    }

    override val dataEndPosition: Long
        get() = -1

    /**
     * Returns the stream time in microseconds for a given position.
     *
     * @param position The stream byte-position.
     * @return The stream time in microseconds for the given position.
     */
    private fun getTimeUsAtPosition(position: Long): Long {
        return getTimeUsAtPosition(position, firstFrameBytePosition, bitrate)
    }

    private fun getFramePositionForTimeUs(timeUs: Long): Long {
        var positionOffset =
            timeUs * bitrate / (Util.MICROS_PER_SECOND * Util.BITS_PER_BYTE)
        // Constrain to nearest preceding frame offset.
        positionOffset = positionOffset / frameSize * frameSize
        positionOffset =
            Util.constrainValue(positionOffset, 0, dataSize - frameSize)
        return firstFrameBytePosition + positionOffset
    }

    companion object {
        // Internal methods
        /**
         * Returns the stream time in microseconds for a given stream position.
         *
         * @param position The stream byte-position.
         * @param firstFrameBytePosition The position of the first frame in the stream.
         * @param bitrate The bitrate (which is assumed to be constant in the stream).
         * @return The stream time in microseconds for the given stream position.
         */
        private fun getTimeUsAtPosition(
            position: Long,
            firstFrameBytePosition: Long,
            bitrate: Int
        ): Long {
            return ((max(0, position - firstFrameBytePosition)
                * Util.BITS_PER_BYTE
                * Util.MICROS_PER_SECOND)
                / bitrate)
        }
    }

}