package com.lizhi.component.tekiplayer.controller.list

import com.lizhi.component.tekiplayer.controller.AudioProgramHolder

interface OnPlayListUpdateListener {
    /**
     * 当前播放位置变动
     */
    fun onPlayPositionChanged(
        position: Int,
        lastPositionUs: Long,
        reason: Int
    )
    fun onPlayZero(
        position: Int,
    )

    /**
     * 播放列表变动
     * [reason] 变动原因
     */
    fun onPlayListChanged(reason: Int)

    /**
     * 当前正在播放的Item被移除
     */
    fun onPlayingItemRemoved(reason: Int)

    fun onItemReady(item: AudioProgramHolder)

    fun onPlayingItemPreloadFailed()
}