package com.lizhi.component.tekiplayer.controller.focus

import android.content.Context
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.os.Build
import androidx.annotation.RequiresApi
import com.lizhi.component.tekiplayer.util.TekiLog

/**
 * 文件名：PlayerAudioFocusManager
 * 作用：音频焦点封装实现
 * 作者：huangtianhao
 * 创建日期：2021/4/6
 */
class PlayerAudioFocusManager(
    context: Context,
    val listener: AudioFocusManager.AudioFocusChangeListener
) : AudioFocusManager, AudioManager.OnAudioFocusChangeListener {

    private var focusRequest: AudioFocusRequest? = null

    private val audioManager =
        context.getSystemService(Context.AUDIO_SERVICE) as AudioManager

    companion object {
        private const val TAG = "PlayerAudioFocusManager"
        const val UNSET = -1
    }

    override var enabled: Boolean = true

    override var audioFocusState: Int = UNSET

    override fun request(): Bo<PERSON>an {
        if (!enabled) {
            return false
        }

        if (audioFocusState == AudioManager.AUDIOFOCUS_GAIN) {
            return true
        }

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            internalRequestV26()
        } else {
            internalRequestDefault()
        }.also {
            if (it) {
                audioFocusState = AudioManager.AUDIOFOCUS_GAIN
            }
        }
    }

    private fun internalRequestDefault(): Boolean {
        val result = audioManager.requestAudioFocus(
            this,
            AudioManager.STREAM_MUSIC,
            AudioManager.AUDIOFOCUS_GAIN
        )

        return onRequestResult(result)
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun internalRequestV26(): Boolean {
        val playbackAttributes = AudioAttributes.Builder()
            .setUsage(AudioAttributes.USAGE_MEDIA)
            .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
            .build()

        val focusRequest = focusRequest ?: AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
            .setAudioAttributes(playbackAttributes)
            .setAcceptsDelayedFocusGain(false)
            .setOnAudioFocusChangeListener(this)
            .build().also { this.focusRequest = it }

        val result = audioManager.requestAudioFocus(focusRequest)
        return onRequestResult(result)
    }

    private fun onRequestResult(result: Int): Boolean {
        if (result != AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
            TekiLog.i(TAG, "AudioManager requestAudioFocus fail,result is $result")
        }

        return result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
    }

    override fun abandon(): Boolean {
        if (!enabled) {
            return false
        }
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            internalAbandonV26()
        } else {
            internalAbandon()
        }.also {
            audioFocusState = UNSET
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun internalAbandonV26(): Boolean {
        val focusRequest = focusRequest ?: return false
        return audioManager.abandonAudioFocusRequest(focusRequest) == AudioManager.AUDIOFOCUS_REQUEST_GRANTED.also {
            this.focusRequest = null
        }
    }

    private fun internalAbandon(): Boolean {
        return audioManager.abandonAudioFocus(this) == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
    }

    override fun onAudioFocusChange(focusChange: Int) {
        TekiLog.i(TAG, "onAudioFocusChange, audioFocus=$focusChange")
        audioFocusState = focusChange
        if (!enabled) {
            return
        }
        when (focusChange) {
            AudioManager.AUDIOFOCUS_GAIN -> listener.onAudioFocusGained()
            AudioManager.AUDIOFOCUS_LOSS -> listener.onAudioFocusLost(
                isTransient = false,
                mayDuck = false
            )
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> listener.onAudioFocusLost(
                isTransient = true,
                mayDuck = false
            )
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> listener.onAudioFocusLost(
                isTransient = true,
                mayDuck = true
            )
        }
    }

}