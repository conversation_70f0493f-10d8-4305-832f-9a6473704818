package com.lizhi.component.tekiplayer.engine.decode.opus

import com.lizhi.component.tekiplayer.engine.InputBufferHolder
import com.lizhi.component.tekiplayer.engine.exception.EngineException
import java.nio.ByteBuffer

class OpusInputBufferHolder : InputBufferHolder() {

    private var data: ByteArray = ByteArray(512)

    override fun setData(data: ByteArray, offset: Int, size: Int) {
        data.copyInto(this.data, 0, offset, offset + size)
    }

    override fun getDataByteArray(): ByteArray {
        return data
    }

    override fun getDataByteBuffer(): ByteBuffer? {
        throw EngineException(1, "OpusInputBufferHolder getDataByteBuffer not support")
    }

    override fun clearData() {

    }

}