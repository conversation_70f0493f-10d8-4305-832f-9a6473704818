package com.lizhi.component.tekiplayer.datasource.impl

import android.content.Context
import android.content.res.AssetManager
import android.net.Uri
import android.os.Bundle
import com.lizhi.component.tekiplayer.datasource.BaseDataSource
import com.lizhi.component.tekiplayer.datasource.DataReader.Companion.READ_END_OF_INPUT
import com.lizhi.component.tekiplayer.datasource.DataReader.Companion.READ_ERROR
import com.lizhi.component.tekiplayer.datasource.DataSource
import com.lizhi.component.tekiplayer.datasource.DataSourceCallback
import com.lizhi.component.tekiplayer.datasource.DataSourceStrategy
import com.lizhi.component.tekiplayer.datasource.Range
import com.lizhi.component.tekiplayer.datasource.exception.FileDataSourceException
import com.lizhi.component.tekiplayer.util.TekiLog
import java.io.IOException
import java.io.InputStream
import javax.crypto.CipherInputStream

/**
 * 文件名：AssetsDataSource
 * 作用：适配读取 file:///android_asset 中的文件路径
 * 作者：huangtianhao
 * 创建日期：2021/2/22
 */
class AssetsDataSource(
    context: Context,
    url: String,
    dataSourceCallback: DataSourceCallback?,
    strategy: DataSourceStrategy
) : BaseDataSource(context, url, dataSourceCallback, strategy) {

    class AssetsDataSourceFactory : BaseDataSource.BaseFactory() {
        override fun create(uri: Uri, extraData: Bundle?): DataSource {
            return AssetsDataSource(
                checkNotNull(this.context),
                this.url ?: uri.toString(),
                this.dataSourceCallback,
                this.strategy
            )
        }
    }

    companion object {
        private const val TAG = "FileDataSource"
    }

    private var inputStream: InputStream? = null
    private var bytesRemaining = 0L
    private val assetManager = context.assets

    override var contentLength: Long? = null

    override val responseHeaders: Map<String, List<String>>?
        get() = null

    override fun open(range: Range): Boolean {
        // 参数校验
        if (!checkArgs(range)) {
            return false
        }

        var path = originUrl
        if (path.startsWith("file://")) {
            path = path.substring(7)
        }
        if (path.startsWith("/android_asset/")) {
            path = path.substring(15);
        } else if (path.startsWith("/")) {
            path = path.substring(1);
        }

        // 读取文件
        val inputStream = try {
            assetManager.open(path, AssetManager.ACCESS_RANDOM)
        } catch (e: Exception) {
            this.dataSourceCallback?.onErrorOccurred(
                FileDataSourceException(e, e.message)
            )
            null
        } ?: return false
        if (hasAesInfo() && !isRealTimeDecrypt()) {
            this.inputStream = CipherInputStream(inputStream, aesComponent!!.decryptCipher)
        } else {
            this.inputStream = inputStream
        }

        // seek到指定位置，计算需要读的字节数
        try {
            val available = inputStream.available()
            if (available == Int.MAX_VALUE) {
                throw IOException("available is Int.MAX_VALUE")
            }
            contentLength = available.toLong()
            inputStream.skip(range.start)
            bytesRemaining = if (range.end == null) {
                available - range.start
            } else {
                range.end - range.start
            }
        } catch (e: IOException) {
            TekiLog.e(TAG, e.message ?: "", e)
            this.dataSourceCallback?.onErrorOccurred(FileDataSourceException(e, e.message))
        }


        if (bytesRemaining < 0) {
            return false
        }

        dataSourceCallback?.onReadyRead()

        return true
    }

    override fun read(
        buffer: ByteArray,
        offset: Int,
        readLength: Int
    ): Int {
        return when {
            readLength == 0 -> 0
            bytesRemaining == 0L -> {
                TekiLog.w(TAG, "read READ_END_OF_INPUT")
                dataSourceCallback?.onEndEncountered()
                READ_END_OF_INPUT
            }
            else -> {
                var bytesRead: Int = READ_ERROR
                try {
                    bytesRead =
                        inputStream?.read(buffer, offset, minOf(bytesRemaining.toInt(), readLength))
                            ?: 0
                    if (bytesRead < 0) {
                        TekiLog.w(TAG, "read bytesRead < 0  $offset  $readLength")
                    }
                } catch (e: IOException) {
                    TekiLog.e(TAG, e.message ?: "", e)
                    this.dataSourceCallback?.onErrorOccurred(FileDataSourceException(e, e.message))
                }
                bytesRead
            }
        }
    }

    override fun updateDataSourceCallback(dataSourceCallback: DataSourceCallback) {
        this.dataSourceCallback = dataSourceCallback
    }

    private fun checkArgs(range: Range): Boolean {
        if (range.start < 0) {
            this.dataSourceCallback?.onErrorOccurred(
                FileDataSourceException(message = "illegal range ${range.start} < 0")
            )
            return false
        }
        if (originUrl.isBlank()) {
            this.dataSourceCallback?.onErrorOccurred(
                FileDataSourceException(message = "url is blank")
            )
            return false
        }
        return true
    }

    override fun close() {
        try {
            inputStream?.close()
        } catch (ignore: Exception) {

        }
    }

    override fun getUrl(): String {
        return originUrl
    }

}