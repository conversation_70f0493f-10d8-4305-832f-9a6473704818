package com.lizhi.component.tekiplayer.audioprogram.extractor.mp3

import android.media.MediaFormat
import com.lizhi.component.tekiplayer.audioprogram.extractor.*
import com.lizhi.component.tekiplayer.audioprogram.extractor.Extractor.Companion.RESULT_CONTINUE
import com.lizhi.component.tekiplayer.datasource.exception.HttpDataSourceException
import com.lizhi.component.tekiplayer.engine.BufferHolder
import com.lizhi.component.tekiplayer.engine.DataQueue
import com.lizhi.component.tekiplayer.engine.exception.EngineException
import com.lizhi.component.tekiplayer.util.Util
import com.lizhi.component.tekiplayer.util.audio.MpegAudioUtil
import com.lizhi.component.tekiplayer.util.audio.MpegAudioUtil.Header
import java.io.EOFException
import java.io.IOException
import kotlin.jvm.Throws

/**
 * 文件名：Mp3Extractor
 * 作用：MP3音频解析封装
 * 作者：liaodongming
 * 创建日期：2021/2/20
 */
class Mp3Extractor : Extractor {

    companion object {
        const val TAG = "Mp3Extractor"

        /**
         * The maximum number of bytes to search when synchronizing, before giving up.
         */
        private const val MAX_SYNC_BYTES = 128 * 1024

        /**
         * The maximum number of bytes to peek when sniffing, excluding the ID3 header, before giving up.
         */
        private const val MAX_SNIFF_BYTES = 32 * 1024

        /**
         * Mask that includes the audio header values that must match between frames.
         */
        private const val MPEG_AUDIO_HEADER_MASK = -0x1f400

        private const val SEEK_HEADER_XING = 0x58696e67
        private const val SEEK_HEADER_INFO = 0x496e666f
        private const val SEEK_HEADER_VBRI = 0x56425249
        private const val SEEK_HEADER_UNSET = 0

        /**
         * Returns whether the headers match in those bits masked by [MPEG_AUDIO_HEADER_MASK].
         */
        private fun headersMatch(
            headerA: Int,
            headerB: Long
        ): Boolean {
            return (headerA and MPEG_AUDIO_HEADER_MASK).toLong() == headerB and MPEG_AUDIO_HEADER_MASK.toLong()
        }
    }

    override var mediaFormat: MediaFormat? = null
        private set
    override var durationUs: Long = -1L
        private set
        get() = seeker?.durationUs ?: -1
    override var seeker: Seeker? = null
        private set

    private val id3Peeker by lazy {
        Id3Peeker()
    }
    private val scratch: ParsableByteArray by lazy {
        ParsableByteArray(Id3Peeker.ID3_HEADER_LENGTH)
    }
    private val synchronizedHeader by lazy {
        Header()
    }

    private var synchronizedHeaderData = 0
    private var basisTimeUs: Long = -1
    private var samplesRead: Long = 0
    private var firstSamplePosition: Long = 0
    private var sampleBytesRemaining = 0
    private var dataQueue: DataQueue? = null

    override fun init(dataQueue: DataQueue?) {
        this.dataQueue = dataQueue
    }

    override fun sample(input: ExtractorInput, seekPosition: PositionHolder): Int {
        return readInternal(input)
    }

    override fun seek(timeUs: Long, position: Long) {
        synchronizedHeaderData = 0
        basisTimeUs = -1
        samplesRead = position
        sampleBytesRemaining = 0
    }

    /**
     * 探测输入流，看当前格式处理器是否支持此流
     * @param extractorInput 输入流
     * @return true 支持 else 不支持
     */
    override fun sniff(extractorInput: ExtractorInput): Boolean {
        return synchronize(extractorInput, true)
    }

    /**
     * 从采样数据种读取一帧至目标 buffer
     *
     * @param buffer
     * @return 0：没有读到数据 else 数据读取长度
     */
    override fun readSample(buffer: BufferHolder): Int {
        return dataQueue?.blockDequeue(buffer) ?: -2
    }

    override fun timeToPosition(timeUs: Long): SeekPoint {
        val positionAtTimeUs = seeker?.getPositionAtTimeUs(timeUs)
        return positionAtTimeUs ?: SeekPoint(-1, -1)
    }

    override fun release() {
        // Do nothing
    }

    /**
     * 尝试同步音频帧头，有效帧 >4 则说明同步成功
     *
     * @param input
     * @param sniffing
     * @return
     */
    @Throws(IOException::class)
    private fun synchronize(
        input: ExtractorInput,
        sniffing: Boolean
    ): Boolean {
        // 有效帧数量
        var validFrameCount = 0
        // 帧头描述数据缓存，
        var candidateSynchronizedHeaderData = 0
        // id3内容长度
        var peekedId3Bytes = 0
        // 探测长度
        var searchedBytes = 0
        val searchLimitBytes: Int = if (sniffing) MAX_SNIFF_BYTES else MAX_SYNC_BYTES
        input.resetPeekPosition()
        if (input.position == 0L) {
            // We need to parse enough ID3 metadata to retrieve any gapless/seeking playback information
            // even if ID3 metadata parsing is disabled.
            // 解析id3的数据长度，但不解析内容
            id3Peeker.peekId3Data(input)
            // 当前的数据流位置就是id3的长度
            peekedId3Bytes = input.peekPosition.toInt()
            if (!sniffing) {
                // 如果不是在最初的探测阶段，跳过id3数据
                input.skipFully(peekedId3Bytes)
            }
        }
        while (true) {
            // 在id3的位置后读4个字节到scratch，并判断是不是已经到达输入流结尾
            if (peekEndOfStreamOrHeader(input)) {
                if (validFrameCount > 0) {
                    // We reached the end of the stream but found at least one valid frame.
                    break
                }
                throw EOFException()
            }
            scratch.position = 0
            // 把读到的前4个字节按照MP3的格式要求转化成帧头
            val headerData: Int = scratch.readInt()
            var frameSize = 0
            // 如果帧头数据不正确，即当前读到的数据不是帧头
            if ((candidateSynchronizedHeaderData != 0
                        && !headersMatch(headerData, candidateSynchronizedHeaderData.toLong()))
                || MpegAudioUtil.getFrameSize(headerData).also { frameSize = it } == -1
            ) {
                // The header doesn't match the candidate header or is invalid. Try the next byte offset.
                if (searchedBytes++ == searchLimitBytes) {
                    if (!sniffing) {
                        throw EngineException(message = "Searched too many bytes.")
                    }
                    return false
                }
                // 重置可用帧数
                // 重置帧头数据
                validFrameCount = 0
                candidateSynchronizedHeaderData = 0
                // 如果是格式探测中，从头开始再读取进行测试
                // 否则往后移一位进行下一轮同步直到找到帧头或者探测的数据过多为止
                if (sniffing) {
                    input.resetPeekPosition()
                    input.advancePeekPosition(peekedId3Bytes + searchedBytes)
                } else {
                    input.skipFully(1)
                }
            } else {
                // The header matches the candidate header and/or is valid.
                // 如果帧头数据是正确的，则进行下一轮直到发现4个可用帧则为同步完成
                // 为什么是4个？改为3个是否可以提高首播速度？
                validFrameCount++
                if (validFrameCount == 1) {
                    synchronizedHeader.setForHeaderData(headerData)
                    candidateSynchronizedHeaderData = headerData
                } else if (validFrameCount == 4) {
                    break
                }
                // 前进一帧的位置，-4字节是为了同步帧头的32位数据
                input.advancePeekPosition(frameSize - 4)
            }
        }
        // Prepare to read the synchronized frame.
        if (sniffing) {
            input.skipFully(peekedId3Bytes + searchedBytes)
        } else {
            input.resetPeekPosition()
        }
        synchronizedHeaderData = candidateSynchronizedHeaderData
        return true
    }

    /**
     * Returns whether the extractor input is peeking the end of the stream. If `false`,
     * populates the scratch buffer with the next four bytes.
     */
    @Throws(IOException::class, HttpDataSourceException::class)
    private fun peekEndOfStreamOrHeader(extractorInput: ExtractorInput): Boolean {
        seeker?.let {
            val dataEndPosition: Long = it.dataEndPosition
            if (dataEndPosition != -1L && extractorInput.peekPosition > dataEndPosition - 4) {
                return true
            }
        }
        return try {
            !extractorInput.peekFully(
                scratch.data,  /* offset= */0,  /* length= */4,  /* allowEndOfInput= */true
            )
        } catch (e: EOFException) {
            true
        }
    }

    // Internal methods.
    @Throws(IOException::class)
    private fun readInternal(input: ExtractorInput): Int {
        if (synchronizedHeaderData == 0) {
            try {
                synchronize(input, false)
            } catch (e: EOFException) {
                dataQueue?.setEndFlag()
                return -1
            }
        }
        if (seeker == null) {
            seeker = computeSeeker(input)
            val mediaFormat = MediaFormat()
            mediaFormat.setString(MediaFormat.KEY_MIME, synchronizedHeader.mimeType)
            mediaFormat.setInteger(
                MediaFormat.KEY_MAX_INPUT_SIZE,
                MpegAudioUtil.MAX_FRAME_SIZE_BYTES
            )
            mediaFormat.setInteger(MediaFormat.KEY_CHANNEL_COUNT, synchronizedHeader.channels)
            mediaFormat.setInteger(MediaFormat.KEY_SAMPLE_RATE, synchronizedHeader.sampleRate)
            if (Util.SDK_INT >= 23) {
                mediaFormat.setInteger(MediaFormat.KEY_PRIORITY, 0 /* realtime priority */)
            }
            this.mediaFormat = mediaFormat
            firstSamplePosition = input.position
        } else if (firstSamplePosition != 0L) {
            val inputPosition: Long = input.position
            if (inputPosition < firstSamplePosition) {
                // Skip past the seek frame.
                input.skipFully((firstSamplePosition - inputPosition).toInt())
            }
        }
        return readSampleInternal(input)
    }

    override fun reset() {
        synchronizedHeaderData = 0
        basisTimeUs = -1
        samplesRead = 0
        sampleBytesRemaining = 0
    }

    @Throws(IOException::class)
    private fun readSampleInternal(extractorInput: ExtractorInput): Int {
        val sampleTimeUs = computeTimeUs(samplesRead)
        val newSample = sampleBytesRemaining == 0
        if (newSample) {
            extractorInput.resetPeekPosition()
            if (peekEndOfStreamOrHeader(extractorInput)) {
                dataQueue?.setEndFlag()
                return -1
            }
            scratch.position = 0
            val sampleHeaderData = scratch.readInt()
            if (!headersMatch(sampleHeaderData, synchronizedHeaderData.toLong())
                || MpegAudioUtil.getFrameSize(sampleHeaderData) == -1
            ) {
                // We have lost synchronization, so attempt to resynchronize starting at the next byte.
                extractorInput.skipFully(1)
                synchronizedHeaderData = 0
                return RESULT_CONTINUE
            }
            synchronizedHeader.setForHeaderData(sampleHeaderData)
            if (basisTimeUs == -1L) {
                basisTimeUs = seeker!!.getTimeUs(extractorInput.position)
            }
            sampleBytesRemaining = synchronizedHeader.frameSize

            dataQueue?.sampleMetadata(sampleTimeUs, synchronizedHeader.frameSize, extractorInput.position)
        }
        val bytesAppended: Int =
            dataQueue?.readToQueue(extractorInput, sampleBytesRemaining, sampleTimeUs)
                ?: throw UninitializedPropertyAccessException()
        if (bytesAppended == -1) {
            dataQueue?.setEndFlag()
            return -1
        }
        sampleBytesRemaining -= bytesAppended
        if (sampleBytesRemaining > 0) {
            return RESULT_CONTINUE
        }
        samplesRead += synchronizedHeader.samplesPerFrame
        sampleBytesRemaining = 0
        return RESULT_CONTINUE
    }

    override fun getDataQueue(): DataQueue? {
        return dataQueue
    }

    private fun computeTimeUs(samplesRead: Long): Long {
        return basisTimeUs + samplesRead * Util.MICROS_PER_SECOND / synchronizedHeader.sampleRate
    }

    @Throws(IOException::class)
    private fun computeSeeker(input: ExtractorInput): Seeker? {
        // Read past any seek frame and set the seeker based on metadata or a seek frame. Metadata
        // takes priority as it can provide greater precision.
        val seekFrameSeeker: Seeker? = maybeReadSeekFrame(input)
        var resultSeeker: Seeker? = null
        if (seekFrameSeeker != null) {
            resultSeeker = seekFrameSeeker
        }
        if (seekFrameSeeker == null) {
            resultSeeker = getConstantBitrateSeeker(input)
        }
        return resultSeeker
    }

    /**
     * Consumes the next frame from the `input` if it contains VBRI or Xing seeking metadata,
     * returning a [Seeker] if the metadata was present and valid, or `null` otherwise.
     * After this method returns, the input position is the start of the first frame of audio.
     *
     * @param input The [ExtractorInput] from which to read.
     * @return A [Seeker] if seeking metadata was present and valid, or `null` otherwise.
     * @throws IOException Thrown if there was an error reading from the stream. Not expected if the
     * next two frames were already peeked during synchronization.
     */
    @Throws(IOException::class)
    private fun maybeReadSeekFrame(input: ExtractorInput): Seeker? {
        val frame = ParsableByteArray(synchronizedHeader.frameSize)
        input.peekFully(frame.data, 0, synchronizedHeader.frameSize)
        val xingBase =
            if (synchronizedHeader.version and 1 != 0) if (synchronizedHeader.channels != 1) 36 else 21 // MPEG 1
            else if (synchronizedHeader.channels != 1) 21 else 13 // MPEG 2 or 2.5
        val seekHeader: Int = getSeekFrameHeader(frame, xingBase)
        val seeker: Seeker?
        if (seekHeader == SEEK_HEADER_XING || seekHeader == SEEK_HEADER_INFO) {
            seeker = XingSeeker.create(input.length, input.position, synchronizedHeader, frame)
            input.skipFully(synchronizedHeader.frameSize)
            if (seeker != null && seekHeader == SEEK_HEADER_INFO) {
                // Fall back to constant bitrate seeking for Info headers missing a table of contents.
                return getConstantBitrateSeeker(input)
            }
        } else if (seekHeader == SEEK_HEADER_VBRI) {
            seeker = VbriSeeker.create(input.length, input.position, synchronizedHeader, frame)
            input.skipFully(synchronizedHeader.frameSize)
        } else { // seekerHeader == SEEK_HEADER_UNSET
            // This frame doesn't contain seeking information, so reset the peek position.
            seeker = null
            input.resetPeekPosition()
        }
        return seeker
    }

    /**
     * Returns [.SEEK_HEADER_XING], [.SEEK_HEADER_INFO] or [.SEEK_HEADER_VBRI] if
     * the provided `frame` may have seeking metadata, or [.SEEK_HEADER_UNSET] otherwise.
     * If seeking metadata is present, `frame`'s position is advanced past the header.
     */
    private fun getSeekFrameHeader(
        frame: ParsableByteArray,
        xingBase: Int
    ): Int {
        if (frame.limit() >= xingBase + 4) {
            frame.position = xingBase
            val headerData = frame.readInt()
            if (headerData == SEEK_HEADER_XING || headerData == SEEK_HEADER_INFO) {
                return headerData
            }
        }
        if (frame.limit() >= 40) {
            frame.position = 36 // MPEG audio header (4 bytes) + 32 bytes.
            if (frame.readInt() == SEEK_HEADER_VBRI) {
                return SEEK_HEADER_VBRI
            }
        }
        return SEEK_HEADER_UNSET
    }

    /** Peeks the next frame and returns a [ConstantBitrateSeeker] based on its bitrate.  */
    @Throws(IOException::class)
    private fun getConstantBitrateSeeker(input: ExtractorInput): Seeker? {
        input.peekFully(scratch.data, 0, 4)
        scratch.position = 0
        synchronizedHeader.setForHeaderData(scratch.readInt())
        return ConstantBitrateSeeker(
            input.length,
            input.position,
            synchronizedHeader.bitrate,
            synchronizedHeader.frameSize
        )
    }
}