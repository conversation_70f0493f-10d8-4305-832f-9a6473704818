package com.lizhi.component.tekiplayer.analyzer

import androidx.annotation.IntDef
import kotlin.annotation.AnnotationTarget.*

/**
 * 文件名：BandwidthQualityLevel
 * 作用：网络质量级别常亮定义，
 * 作者：<PERSON><PERSON><PERSON><PERSON>@lizhi.fm
 * 创建日期：2021/2/20
 */
class BandwidthQuality {

    companion object {
        /**
         * 未知
         */
        const val LEVEL_UNKNOWN = 0

        /**
         * 无网（0kb/s)
         */
        const val LEVEL_NO_NETWORK = 1

        /**
         * 差（<128kbps)
         */
        const val LEVEL_BAD = 2

        /**
         * 中等(128-320kbps)
         */
        const val LEVEL_MEDIUM = 3

        /**
         * 好(320-1411kbps)
         */
        const val LEVEL_GOOD = 4

        /**
         * 极好(>1411kbps)
         */
        const val LEVEL_EXCELLENT = 5

        @Level
        fun getLevel(speed: Int): Int {
            return when {
                speed < 0 -> LEVEL_UNKNOWN
                speed == 0 -> LEVEL_NO_NETWORK
                speed < 128000 -> LEVEL_BAD
                speed <= 320000 -> LEVEL_MEDIUM
                speed <= 1411000 -> LEVEL_GOOD
                else -> LEVEL_EXCELLENT
            }
        }
    }

    /**
     * 网络质量状态
     */
    @MustBeDocumented
    @kotlin.annotation.Retention(AnnotationRetention.SOURCE)
    @Target(VALUE_PARAMETER, FIELD, PROPERTY, FUNCTION)
    @IntDef(value = [LEVEL_UNKNOWN, LEVEL_NO_NETWORK, LEVEL_BAD, LEVEL_MEDIUM, LEVEL_GOOD, LEVEL_EXCELLENT])
    annotation class Level

}