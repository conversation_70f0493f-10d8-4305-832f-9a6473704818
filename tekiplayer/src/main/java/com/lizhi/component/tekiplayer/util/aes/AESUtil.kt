package com.lizhi.component.tekiplayer.util.aes

import android.util.Base64
import android.util.Log
import com.lizhi.component.tekiplayer.util.TekiLog.e
import java.io.InputStream
import java.io.OutputStream
import java.security.InvalidAlgorithmParameterException
import java.security.InvalidKeyException
import java.security.NoSuchAlgorithmException
import java.security.SecureRandom
import javax.crypto.Cipher
import javax.crypto.CipherInputStream
import javax.crypto.KeyGenerator
import javax.crypto.NoSuchPaddingException
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

/**
 * @Auther: hzhenx
 * @Email: <EMAIL>
 * @datetime: 2023/7/25
 * @desc:
 */
object AESUtil {
    private const val TAG = "e2ee.AESUtils"
    const val AES_ALGORITHM = "AES"
    const val AESKEY_LENGTH = 32
    const val IV_LENGTH = 16
    const val AES_KEY = "aesKey"
    const val AES_IV = "aesIv"
    const val AES_IS_REAL_TIME_DECRYPT = "isRealTimeDecrypt"

    fun generateSecretKeySpec(): SecretKeySpec {
        return try {
            val keyGenerator = KeyGenerator.getInstance(AES_ALGORITHM)
            keyGenerator.init(AESKEY_LENGTH * 8, SecureRandom())
            val secretKey = keyGenerator.generateKey()
            val aesKey = secretKey.encoded
            SecretKeySpec(aesKey, AES_ALGORITHM)
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e(TAG, "generateSecretKeySpec exception:$e")
            throw Exception(e)
        }
    }

    fun generateSecretKeySpec(data: ByteArray?): SecretKeySpec {
        return try {
            SecretKeySpec(data, AES_ALGORITHM)
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e(TAG, "generateSecretKeySpec exception:$e")
            throw Exception(e)
        }
    }

    fun generateIvParameterSpec(): IvParameterSpec {
        return try {
            val secureRandom = SecureRandom()
            val iv = ByteArray(IV_LENGTH)
            secureRandom.nextBytes(iv)
            IvParameterSpec(iv)
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e(TAG, "generateIvParameterSpec exception:$e")
            throw Exception(e)
        }
    }

    fun generateIvParameterSpec(data: ByteArray?): IvParameterSpec {
        return try {
            IvParameterSpec(data)
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e(TAG, "generateIvParameterSpec exception:$e")
            throw Exception(e)
        }
    }

    fun encryptData(key: String, iv: String, data: ByteArray): ByteArray? {
        val secretKeySpec = generateSecretKeySpec(key.toByteArray())
        val ivSpec = generateIvParameterSpec(iv.toByteArray())
        return if (key == null || iv == null || data == null || data.isEmpty()) {
            null
        } else try {
            val cipher = getCipher(Cipher.ENCRYPT_MODE, secretKeySpec, ivSpec)
            cipher.doFinal(data)
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e(TAG, "encryptData exception:$e")
            throw Exception(e)
        }
    }

    fun decryptData(key: String, iv: String, data: ByteArray, isLast: Boolean = false): ByteArray? {
        val secretKeySpec = generateSecretKeySpec(key.toByteArray())
        val ivSpec = generateIvParameterSpec(iv.toByteArray())
        return try {
            val cipher = getCipher(Cipher.DECRYPT_MODE, secretKeySpec, ivSpec)
            if (isLast) {
                cipher.doFinal()
            } else {
                cipher.update(data)
//                cipher.doFinal()
            }
//            cipher.doFinal(data)
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e(TAG, "decryptData data exception:$e")
            throw Exception(e)
        }
    }

    fun encryptData(
        key: SecretKeySpec,
        iv: IvParameterSpec,
        inputStream: InputStream,
        outputStream: OutputStream
    ) {
        try {
            val cipher = getCipher(Cipher.ENCRYPT_MODE, key, iv)
            val cipherIn = CipherInputStream(inputStream, cipher)
            val inputBuffer = ByteArray(1024)
            var bytesRead: Int
            while (cipherIn.read(inputBuffer).also { bytesRead = it } != -1) {
                outputStream.write(inputBuffer, 0, bytesRead)
                outputStream.flush()
            }
            inputStream.close()
            outputStream.close()
            cipherIn.close()
        } catch (e: Exception) {
            e.printStackTrace()
            e(TAG, "encryptData stream", e)
            throw Exception(e)
        }
    }

    fun decryptData(
        key: SecretKeySpec,
        iv: IvParameterSpec,
        inputStream: InputStream,
        outputStream: OutputStream
    ) {
        try {
            val cipher = getCipher(Cipher.DECRYPT_MODE, key, iv)
            val cipherIn = CipherInputStream(inputStream, cipher)
            val inputBuffer = ByteArray(1024)
            var bytesRead: Int
            while (cipherIn.read(inputBuffer).also { bytesRead = it } != -1) {
                outputStream.write(inputBuffer, 0, bytesRead)
                outputStream.flush()
            }
            inputStream.close()
            outputStream.close()
            cipherIn.close()
        } catch (e: Exception) {
            e.printStackTrace()
            e(TAG, "decryptData stream", e)
            throw Exception(e)
        }
    }

    // public static void decryptData(SecretKeySpec key, IvParameterSpec iv, InputStream inputStream, AesUpdateCallBack callback) {
    //     try {
    //         Cipher cipher = getCipher(Cipher.DECRYPT_MODE, key, iv);
    //         CipherInputStream cipherIn = new CipherInputStream(inputStream, cipher);
    //         byte[] inputBuffer = new byte[1024];
    //         int bytesRead;
    //         while ((bytesRead = cipherIn.read(inputBuffer)) != -1) {
    //             if (callback != null) {
    //                 if (bytesRead == inputBuffer.length) {
    //                     callback.onUpdate(inputBuffer);
    //                 } else {
    //                     callback.onUpdate(ByteUtil.trim(inputBuffer, bytesRead));
    //                 }
    //             }
    //         }
    //         if (callback != null) {
    //             callback.onFinish();
    //         }
    //         inputStream.close();
    //         cipherIn.close();
    //     } catch (Exception e) {
    //         e.printStackTrace();
    //         Logs.e(TAG, "decryptData stream:" + e);
    //         throw new Exception(e);
    //     }
    // }
    fun decryptInputStreamToInputStream(
        key: SecretKeySpec,
        iv: IvParameterSpec,
        inputStream: InputStream?
    ): InputStream {
        return try {
            val cipher =
                getCipher(Cipher.DECRYPT_MODE, key, iv)
            CipherInputStream(inputStream, cipher)
        } catch (e: Exception) {
            e.printStackTrace()
            e(TAG, "decryptInputStreamToInputStream stream", e)
            throw Exception(e)
        }
    }

    fun getCipher(mode: Int, key: SecretKeySpec, iv: IvParameterSpec): Cipher {
        return try {
            val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
            cipher.init(mode, key, iv)
            cipher
        } catch (var5: NoSuchPaddingException) {
            throw Exception(var5)
        } catch (var5: InvalidKeyException) {
            throw Exception(var5)
        } catch (var5: InvalidAlgorithmParameterException) {
            throw Exception(var5)
        } catch (var5: NoSuchAlgorithmException) {
            throw Exception(var5)
        }
    }

    fun encodeToBase64(value: ByteArray?): String {
        return Base64.encodeToString(value, Base64.NO_WRAP)
    }

    fun decodeToByteArray(baseData: String?): ByteArray {
        return Base64.decode(baseData, Base64.NO_WRAP)
    }
}