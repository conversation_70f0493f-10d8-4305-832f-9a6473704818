package com.lizhi.component.tekiplayer.controller.lock

import android.content.Context
import android.net.wifi.WifiManager
import android.net.wifi.WifiManager.WifiLock
import com.lizhi.component.tekiplayer.util.TekiLog
import com.lizhi.component.tekiplayer.util.TekiLog.w

/**
 * 负责处理 [WifiLock]，默认关闭了引用计数，因此可多次调用acquire方法
 *
 *
 * The handling of wifi locks requires the [android.Manifest.permission.WAKE_LOCK]
 * permission.
 */
internal class WifiLockManager(context: Context) {
    private val wifiManager: WifiManager? =
        context.applicationContext.getSystemService(Context.WIFI_SERVICE) as? WifiManager
    private var wifiLock: WifiLock? = null
    private var enabled = false
    private var stayAwake = false

    /**
     * Sets whether to enable the usage of a [WifiLock].
     *
     *
     * By default, wifi lock handling is not enabled. Enabling will acquire the wifi lock if
     * necessary. Disabling will release the wifi lock if held.
     *
     *
     * Enabling [WifiLock] requires the [android.Manifest.permission.WAKE_LOCK].
     *
     * @param enabled True if the player should handle a [WifiLock].
     */
    fun setEnabled(enabled: Boolean) {
        if (enabled && wifiLock == null) {
            if (wifiManager == null) {
                w(TAG, "WifiManager is null, therefore not creating the WifiLock.")
                return
            }
            wifiManager.createWifiLock(WifiManager.WIFI_MODE_FULL_HIGH_PERF, WIFI_LOCK_TAG).also {
                wifiLock = it
                it.setReferenceCounted(false)
            }
        }
        this.enabled = enabled
        updateWifiLock()
    }

    /**
     * Sets whether to acquire or release the [WifiLock].
     *
     *
     * The wifi lock will not be acquired unless handling has been enabled through [ ][.setEnabled].
     *
     * @param stayAwake True if the player should acquire the [WifiLock]. False if it should
     * release.
     */
    fun setStayAwake(stayAwake: Boolean) {
        this.stayAwake = stayAwake
        updateWifiLock()
    }

    private fun updateWifiLock() {
        val wifiLock = wifiLock ?: return
        if (enabled && stayAwake) {
            wifiLock.acquire()
        } else {
            wifiLock.release()
        }
        TekiLog.i(TAG, "wifiLock [isHeld] ${wifiLock.isHeld}")
    }

    companion object {
        private const val TAG = "WifiLockManager"
        private const val WIFI_LOCK_TAG = "TekiPlayer:WifiLockManager"
    }


}