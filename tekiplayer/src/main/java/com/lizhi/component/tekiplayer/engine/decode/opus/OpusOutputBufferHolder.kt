package com.lizhi.component.tekiplayer.engine.decode.opus

import com.lizhi.component.tekiplayer.engine.OutputBufferHolder
import com.lizhi.component.tekiplayer.engine.exception.EngineException
import java.nio.ByteBuffer

/**
 * Desc: OpusOutputBufferHolder
 */
class OpusOutputBufferHolder : OutputBufferHolder() {

    private var data: ByteBuffer? = null

    override fun setData(data: ByteArray, offset: Int, size: Int) {
        throw EngineException(1, "OpusOutputBufferHolder setData ByteArray not support")
    }

    override fun setData(data: ByteBuffer) {
        this.data = data
    }

    override fun getDataByteBuffer(): ByteBuffer? {
        return data
    }

    override fun clearData() {
        data?.clear()
        data = null
    }

}