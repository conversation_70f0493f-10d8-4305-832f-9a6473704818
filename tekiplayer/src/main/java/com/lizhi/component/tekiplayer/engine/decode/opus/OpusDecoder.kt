package com.lizhi.component.tekiplayer.engine.decode.opus

import android.media.MediaFormat
import com.lizhi.audiocore.AudioBufferProcess
import com.lizhi.audiocore.CipherCallback
import com.lizhi.audiocore.FillByteBufferCallback
import com.lizhi.component.tekiplayer.engine.decode.Decoder
import com.lizhi.component.tekiplayer.engine.decode.DecoderCallback
import com.lizhi.component.tekiplayer.engine.exception.EngineException
import com.lizhi.component.tekiplayer.util.TekiLog
import java.nio.ByteBuffer


class OpusDecoder(private val format: MediaFormat) : Decoder<OpusInputBufferHolder, OpusOutputBufferHolder>,
    FillByteBufferCallback {

    companion object {
        const val TAG = "OpusDecoder"
    }

    override val name: String = "OpusDecoder"
    override var decoderCallback: DecoderCallback? = null

    private var started = false
    private var released = false

    private var codec: AudioBufferProcess = AudioBufferProcess()
    private var outputBuffer = OpusOutputBufferHolder()
    private lateinit var directBuffer: ByteBuffer
    // cache output pcm data
    private var outPcmDataCaches = ArrayDeque<ByteBuffer>(4)
    // empty buffer cache
    private var availableOutDatas = Array<ByteBuffer?>(4) { null }
    private var availableOutDataIndex = availableOutDatas.size
    private val inputBufferHolder = OpusInputBufferHolder()
    private val lock = Object()
    private var firstFrame = true
    private var lastFrameFinished = false
    private var isRTPRealTimeDecrypt = false

    override fun start() {
        val channel = format.getInteger(MediaFormat.KEY_CHANNEL_COUNT)
        val sampleRate = format.getInteger(MediaFormat.KEY_SAMPLE_RATE)
        val duration = format.getInteger(MediaFormat.KEY_DURATION)
        codec.init(channel, sampleRate, duration, isRTPRealTimeDecrypt)
        // allocate duration ms buffer to receive output data
        ByteBuffer.allocateDirect(2 * sampleRate * channel / 1000 * duration).let {
            codec.cacheDirectBufferAddress(it)
            directBuffer = it
        }
        (availableOutDatas.indices).forEach {
            availableOutDatas[it] = ByteBuffer.allocateDirect(2 * sampleRate * channel / 1000 * duration)
        }

        codec.setFullByteBufferCallBack(this)
        started = true
    }

    override fun dequeueInputBuffer(): OpusInputBufferHolder? {
        // if the buffer is not consumed, return null
        if (codec.getUnWriteSize() < 512) {
            return null
        }
        // if the last frame is finished, return null
        if (inputBufferHolder.isEndOfStream) {
            return null
        }
        return inputBufferHolder.apply {
            clear()
        }
    }


    override fun queueInputBuffer(buffer: OpusInputBufferHolder) {
        if (buffer.isEndOfStream) {
            TekiLog.i(TAG, "queueInputBuffer end of stream")
            codec.sendDecodeBuffer(ByteArray(0), 0, true)
            return
        }
        val data = buffer.getDataByteArray()
        val receiveSize = codec.sendDecodeBuffer(data, buffer.size, false)
//        TekiLog.d(TAG, "queueInputBuffer size: ${buffer.size} receiveSize: $receiveSize position: ${buffer.position}")
        buffer.clear()
    }

    override fun dequeueOutputBuffer(): OpusOutputBufferHolder? = synchronized(lock) {
//        TekiLog.d(TAG, "dequeueOutputBuffer availableOutDataIndex: $availableOutDataIndex outPcmDataCaches size: ${outPcmDataCaches.size}")
        if (lastFrameFinished) {
            TekiLog.i(TAG, "dequeueOutputBuffer lastFrameFinished")
            outputBuffer.isEndOfStream = true
            return outputBuffer
        }

        val byteBuffer = outPcmDataCaches.removeFirstOrNull()
        return byteBuffer?.let {
            outputBuffer.setData(it)
            outputBuffer
        }
    }

    override fun releaseOutputBuffer(outputBuffer: OpusOutputBufferHolder) = synchronized(lock) {
        if (outputBuffer.getDataByteBuffer() != null) {
            availableOutDatas[availableOutDataIndex++ % availableOutDatas.size] = outputBuffer.getDataByteBuffer() ?: return
        }
//        TekiLog.d(TAG, "releaseOutputBuffer availableOutDataIndex: $availableOutDataIndex")
        outputBuffer.clear()
        try {
            if (availableOutDataIndex > 0) {
                lock.notify()
            }
        } catch (_: Exception) {
        }
    }

    override fun flush() = synchronized(lock) {
        // not support seek now, so do nothing
        TekiLog.i(TAG, "flush")
        availableOutDataIndex = availableOutDatas.size
        outPcmDataCaches.clear()
        try {
            if (availableOutDataIndex > 0) {
                lock.notify()
            }
        } catch (_: Exception) {
        }
        inputBufferHolder.isEndOfStream = false
    }

    override fun release() = synchronized(lock) {
        TekiLog.i(TAG, "flush")
        released = true
        availableOutDataIndex = availableOutDatas.size
        try {
            lock.notify()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        codec.stopDecoderThread()
    }

    override fun fullPcmDataSuccess() {

//        TekiLog.d(TAG, "fullPcmDataSuccess availableOutDataIndex: $availableOutDataIndex ")

        // the first frame is the format
        if (firstFrame) {
            firstFrame = false
            decoderCallback?.onFormatOutput(format)
        }
        // should block and wait for the buffer to be consumed
        synchronized(lock) {
            while (availableOutDataIndex <= 0 && !released) {
                lock.wait()
            }
            if (released) {
                return
            }
            val buffer = availableOutDatas[--availableOutDataIndex] ?: return
            buffer.put(directBuffer)
            buffer.flip()
//            TekiLog.d(TAG, "fullPcmDataSuccess buffer size: ${buffer.remaining()}")
            outPcmDataCaches.addLast(buffer)
            directBuffer.clear()
        }
    }

    override fun quicDecodeThreadSuccess() {
        codec.unit()
    }

    override fun lastFrameFinished() {
        TekiLog.i(TAG, "lastFrameFinished from native")
        lastFrameFinished = true
        outputBuffer.isEndOfStream = true
    }

    override fun inPutErrorData() {
//        TekiLog.d(TAG, "inPutErrorData")
        try {
            lock.notify()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        decoderCallback?.onDecodeError(EngineException(message = "opus decode error", cause = null))
    }

    fun setCipherCallback(callback: CipherCallback) {
        codec.setCipherCallback(callback)
    }

    fun isRTPRealTimeDecrypt(isRealTimeDecrypt: Boolean) {
        isRTPRealTimeDecrypt = isRealTimeDecrypt
    }

}