package com.lizhi.component.tekiplayer.controller.clock

/**
 * 文件名：Clock
 * 作用：
 * 作者：l<PERSON><PERSON><PERSON><PERSON>@lizhi.fm
 * 创建日期：2021/4/3
 */
interface Clock {

    var stoped : Boolean

    var onItemRemainingUpdate: (count: Int) -> Unit

    var onTimeRemainingUpdate: (duration: Long) -> Unit

    fun stopAtTime(time: Long)

    fun stopAfterTime(time: Long)

    fun stopAfterItem(count: Int)

    fun addFinishedItem()

    fun cancelStop()

    fun getRemainingTimeBeforeStop(): Long

    fun getRemainingItemBeforeStop(): Int
}