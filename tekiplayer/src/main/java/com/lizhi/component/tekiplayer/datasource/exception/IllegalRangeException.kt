package com.lizhi.component.tekiplayer.datasource.exception

import com.lizhi.component.tekiplayer.ERR_DATASOURCE_ILLEGAL_RANGE
import com.lizhi.component.tekiplayer.datasource.Range

/**
 * 文件名：IllegalRangeException
 * 作用：非法Range异常
 * 作者：huangtianhao
 * 创建日期：2021/3/19
 */
class IllegalRangeException(val range: Range) : DataSourceException(ERR_DATASOURCE_ILLEGAL_RANGE, "Illegal Range [${range.start}-${range.end}") {
}