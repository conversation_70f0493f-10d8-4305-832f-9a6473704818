package com.lizhi.component.tekiplayer.datasource.cache

import com.lizhi.component.tekiplayer.util.TekiLog
import com.lizhi.component.tekiplayer.util.fromJson
import com.lizhi.component.tekiplayer.util.toJson
import com.tencent.mmkv.MMKV


/**
 * 文件名：CacheMmkvProvider
 * 作用：使用MMKV实现的持久化层
 * 作者：huangtianhao
 * 创建日期：2021/3/18
 */
class CacheMmkvStorage private constructor(mmkvId: String = TEKI_PLAYER_MMKV_ID) : CacheStorage {

    companion object {
        fun getInstance(mmkvId: String = TEKI_PLAYER_MMKV_ID): CacheMmkvStorage {
            return if (mmkvId == TEKI_PLAYER_HIGH_MMKV_ID) {
                InstanceHolder.cacheHighMmkvProvider
            } else {
                InstanceHolder.cacheMmkvProvider
            }
        }

        const val TEKI_PLAYER_MMKV_ID = "TekiPlayer"
        const val TEKI_PLAYER_HIGH_MMKV_ID = "TekiPlayerHighPriority"

        private const val TAG = "CacheMmkvStorage"
    }

    private object InstanceHolder {
        val cacheMmkvProvider = CacheMmkvStorage(TEKI_PLAYER_MMKV_ID)
        val cacheHighMmkvProvider = CacheMmkvStorage(TEKI_PLAYER_HIGH_MMKV_ID)
    }

    private val mmkv: MMKV = MMKV.mmkvWithID(mmkvId, MMKV.MULTI_PROCESS_MODE)

    override fun get(urlWithoutHost: String): CacheStorage.CacheInfo? {
        return mmkv.getString(urlWithoutHost, "").fromJson<CacheStorage.CacheInfo>().also {
            TekiLog.d(TAG, "Getting key $urlWithoutHost value = $it")
        }
    }

    override fun remove(urlWithoutHost: String): Boolean {
        mmkv.edit().run {
            this.remove(urlWithoutHost)
            this.apply()
        }
        return true
    }

    override fun save(urlWithoutHost: String, cacheInfo: CacheStorage.CacheInfo) {
        TekiLog.d(TAG, "Saving key $urlWithoutHost cacheInfo $cacheInfo")
        mmkv.edit()
            .run {
                putString(urlWithoutHost, cacheInfo.toJson())
                apply()
            }
    }

    override fun getAll(): List<CacheStorage.CacheInfo> {
        return mmkv.allKeys()
            .mapNotNull { mmkv.getString(it, "").fromJson<CacheStorage.CacheInfo>() }
    }

    override fun clearAll() {
        mmkv.clearAll()
    }
}