package com.lizhi.component.tekiplayer.datasource.cache

import androidx.annotation.Keep
import com.lizhi.component.tekiplayer.datasource.Range

/**
 * 文件名：CacheStorage
 * 作用：缓存持久化接口定义
 * 作者：huangtianhao
 * 创建日期：2021/3/22
 */
interface CacheStorage {

    @Keep
    data class CacheInfo(
        val fileName: String,
        var rangeList: List<Range>?,
        var contentLength: Long? = null,
        var plainCacheLength: Long?= null, // 缓存明文长度
        var lastTouchTimeStamp: Long = System.currentTimeMillis()
    )

    fun save(urlWithoutHost: String, cacheInfo: CacheInfo)

    fun get(urlWithoutHost: String): CacheInfo?

    fun remove(urlWithoutHost: String): Boolean

    fun getAll(): List<CacheInfo>

    fun clearAll()
}