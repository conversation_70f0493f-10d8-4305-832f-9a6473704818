package com.lizhi.component.tekiplayer.analyzer.impl

import android.os.Bundle
import android.os.SystemClock
import com.lizhi.component.basetool.common.Statistic
import com.lizhi.component.tekiplayer.BuildConfig
import com.lizhi.component.tekiplayer.analyzer.BandwidthQuality
import com.lizhi.component.tekiplayer.analyzer.Reporter
import com.lizhi.component.tekiplayer.analyzer.Reporter.Companion.REPORT_STATUS_END
import com.lizhi.component.tekiplayer.analyzer.Reporter.Companion.REPORT_STATUS_INIT
import com.lizhi.component.tekiplayer.audioprogram.AudioProgram
import com.lizhi.component.tekiplayer.engine.exception.EngineException
import com.lizhi.component.tekiplayer.engine.exception.parseExceptionCodeAndMessage

/**
 * 文件名：EventReporter
 * 作用：事件打点
 * 作者：huangtianhao
 * 创建日期：2021/2/20
 */
class EventReporter(private val programId: String, private val programUrl: String, private val category: String?, private val extraData: Bundle? = null) : Reporter {

    companion object {
        const val ENTER_WEAK_NETWORK = 1
        const val EXIT_WEAK_NETWORK = 0

        const val CACHE_NONE = 0
        const val CACHE_LOCAL = 1
        const val CACHE_PREBUFFERING = 2
        const val CACHE_PREBUFFERED = 3

        const val CIPHER_KEY = "cipherKey"
        const val CIPHER_IV = "cipherIv"
        const val CIPHER_IS_REAL_TIME_DECRYPT = "isRealTimeDecrypt"
    }

    private var weakNetworkStartTime = -1L
    private var lastWeakNetworkType = EXIT_WEAK_NETWORK
    private var bufferStartTime = 0L
    private var playSuccess = false
    private var playError= false

    private var suffix = programUrl.substringAfterLast(".", "unknown") // 后缀

    private val playerVersion = BuildConfig.VERSION_NAME
    var cdnStatus: String? = null
    var prebufferStatus: Int? = null
    var cacheStatus = false
    var bufferingStatus = REPORT_STATUS_INIT


    override fun onBuffering() {
        bufferStartTime = SystemClock.elapsedRealtime()

        val map = hashMapOf<String, Any?>()
        map["currentState"] = bufferingStatus
        extraData?.let { bundle->
            bundle.get(Reporter.REPORT_KEY_TRACE_ID)?.let { traceId->
                map[Reporter.REPORT_KEY_TRACE_ID] = traceId
                postEvent("EVENT_SUPPORT_TEKIPLAYER_BUFFERING", map)
            }
        }
    }

    override fun onForceStop() {
        if (bufferingStatus != REPORT_STATUS_INIT || playSuccess) {
            return
        }
        val map = hashMapOf<String, Any?>()
        map["bufferDuration"] = SystemClock.elapsedRealtime() - bufferStartTime
        extraData?.let { bundle->
            bundle.get(Reporter.REPORT_KEY_TRACE_ID)?.let { traceId->
                map[Reporter.REPORT_KEY_TRACE_ID] = traceId
                postEvent("EVENT_SUPPORT_TEKIPLAYER_FORCE_STOP", map)
            }
        }
    }

    override fun onPlaying(playbackDuration: Long) {
        playSuccess = true
        val bufferDuration = SystemClock.elapsedRealtime() - bufferStartTime

        val map = hashMapOf<String, Any?>()
        map["currentState"] = bufferingStatus
        extraData?.let { bundle->
            bundle.get(Reporter.REPORT_KEY_TRACE_ID)?.let { traceId->
                map[Reporter.REPORT_KEY_TRACE_ID] = traceId
                postEvent("EVENT_SUPPORT_TEKIPLAYER_PLAYING", map)
            }
        }

        val map2 = hashMapOf<String, Any?>()
        map2["currentState"] = bufferingStatus
        map2["duration"] = bufferDuration
        map2["playbackDuration"] = playbackDuration
        postEvent("EVENT_SUPPORT_TEKIPLAYER_BUFFER_DURATION", map2)
    }

    override fun onPlayEnd(positionMs: Long, playbackDuration: Long) {
        val map = hashMapOf<String, Any?>()
        map["currentState"] = REPORT_STATUS_END
        map["playbackDuration"] = playbackDuration
        map["playedDuration"] = positionMs
        extraData?.let { bundle->
            bundle.get(Reporter.REPORT_KEY_TRACE_ID)?.let { traceId->
                map[Reporter.REPORT_KEY_TRACE_ID] = traceId
                postEvent("EVENT_SUPPORT_TEKIPLAYER_PLAYING", map)
            }
        }
    }

    override fun onTransactionEnd() {
        val map = hashMapOf<String, Any?>()
        map["playsuccess"] = if (playSuccess) 1 else 0
        map["playerror"] = if (playError) 1 else 0
        map["currentState"] = bufferingStatus
        postEvent("EVENT_SUPPORT_TEKIPLAYER_PLAY_RESULT", map)
    }

    override fun onWeakNetworkChecked(qualityLevel: Int, speed: Int, onError: Boolean?) {

        var type = -1
        var duration: Long = SystemClock.elapsedRealtime() - weakNetworkStartTime

        // 如果网速过低或者请求失败，进入弱网，可以重复进入，即当前是弱网状态，也会重新进入弱网并且刷新进入时间
        if (qualityLevel < BandwidthQuality.LEVEL_MEDIUM && onError == null || onError == true) {
            type = ENTER_WEAK_NETWORK
            // 如果上次并非弱网状态，则说明当前弱网状态是新的，重置duration
            if (lastWeakNetworkType == EXIT_WEAK_NETWORK) {
                duration = 0
            }
            weakNetworkStartTime = SystemClock.elapsedRealtime()
        }
        // 如果上次检测是弱网，目前网速良好，或者上次是错误状态，目前可以接受数据，则退出弱网
        if (qualityLevel >= BandwidthQuality.LEVEL_MEDIUM && lastWeakNetworkType == ENTER_WEAK_NETWORK && onError == null || onError == false) {
            type = EXIT_WEAK_NETWORK
        }
        // 不是进入弱网和退出弱网
        if (type == -1) {
            return
        }

        val map = hashMapOf<String, Any?>()
        map["type"] = type
        map["duration"] = duration
        map["bandwidth"] = speed
        postEvent("EVENT_SUPPORT_TEKIPLAYER_WEAK_NETWORK", map)

        lastWeakNetworkType = type
    }

    override fun onError(exception: Throwable, recoverable: Boolean, extraData: Bundle?) {
        if (!playError) {
            playError = !recoverable
        }

        val map = hashMapOf<String, Any?>()
        val errType = if (exception is EngineException || exception.cause is EngineException) {
            2
        } else {
            1
        }
        val (code, desc) = parseExceptionCodeAndMessage(exception)
        map["errType"] = errType
        map["errCode"] = code
        map["errDesc"] = desc
        map["playsuccess"] = if (playSuccess) 1 else 0
        if (null != extraData) {
            val aesKey = extraData.getString(CIPHER_KEY, null)
            val aesIv = extraData.getString(CIPHER_IV, null)
            if (null != aesKey && null != aesIv) {
                map["isAesStream"] = true
            }
            extraData.getBoolean(CIPHER_IS_REAL_TIME_DECRYPT, false)?.let {
                map[CIPHER_IS_REAL_TIME_DECRYPT] = it
            }
        }
        postEvent("EVENT_SUPPORT_TEKIPLAYER_PLAY_ERROR", map)
    }

    private fun postEvent(eventId: String, map: MutableMap<String, Any?>) {
        map["playerVersion"] = playerVersion
        map["category"] = category
        map["format"] = suffix

        cdnStatus?.let {
            map["cdnStatus"] = it
        }
        map["cacheStatus"] = if (!cacheStatus) {
            when (prebufferStatus) {
                AudioProgram.PREBUFFER_STARTED -> {
                    2
                }
                AudioProgram.PREBUFFER_FINISH -> {
                    3
                }
                else -> {
                    0
                }
            }
        } else {
            1
        }

        map["programId"] = programId
        map["programUrl"] = programUrl

        Statistic.out.stat(eventId, map)
    }

}