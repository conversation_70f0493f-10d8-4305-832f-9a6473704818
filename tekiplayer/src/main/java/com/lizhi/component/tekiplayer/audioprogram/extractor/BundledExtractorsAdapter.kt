/*
 * Copyright (C) 2020 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.lizhi.component.tekiplayer.audioprogram.extractor

import android.media.MediaFormat
import android.net.Uri
import com.lizhi.component.tekiplayer.datasource.DataReader
import com.lizhi.component.tekiplayer.datasource.DataSource
import com.lizhi.component.tekiplayer.engine.BufferHolder
import com.lizhi.component.tekiplayer.engine.DataQueue
import com.lizhi.component.tekiplayer.engine.exception.UnSupportFormatException
import com.lizhi.component.tekiplayer.util.Util
import com.lizhi.component.tekiplayer.util.aes.CipherException
import java.io.IOException

/**
 * [ProgressiveMediaExtractor] built on top of [Extractor] instances, whose
 * implementation classes are bundled in the app.
 */
/**
 * Creates a holder that will select an extractor and initialize it using the specified output.
 *
 * @param extractorsFactory The [ExtractorsFactory] providing the extractors to choose from.
 */
internal class BundledExtractorsAdapter(private val extractorsFactory: ExtractorsFactory) :
    Extractor {

    private var extractorInput: ExtractorInput? = null
    var extractor: Extractor? = null
        private set

    val currentInputPosition: Long
        get() = extractorInput?.position ?: -1
    override val mediaFormat: MediaFormat?
        get() = extractor?.mediaFormat
    override val durationUs: Long?
        get() = extractor?.durationUs
    override val seeker: Seeker?
        get() = extractor?.seeker

    @Throws(IOException::class)
    fun init(
        dataReader: DataReader?,
        uri: Uri,
        responseHeaders: Map<String, List<String?>?>?,
        position: Long,
        length: Long,
        output: DataQueue?
    ) {
        val extractorInput: ExtractorInput = DefaultExtractorInput(dataReader, position, length)
        extractorInput.isRealTimeDecrypt = (dataReader as DataSource).isRealTimeDecrypt()
        val (key,iv) = (dataReader as DataSource).getAesInfo()
        extractorInput.aesIv = iv
        extractorInput.aesKey = key
        this.extractorInput = extractorInput
        if (extractor != null) {
            return
        }
        val extractors = extractorsFactory.createExtractors(uri, responseHeaders)
        if (extractors.size == 1) {
            extractor = extractors[0]
        } else {
            for (extractor in extractors) {
                try {
                    extractor ?: continue
                    if (extractor.sniff(extractorInput)) {
                        this.extractor = extractor
                        break
                    }
                } catch (ignore: Exception) {
//                    ignore.printStackTrace()
                    if (ignore is CipherException) {
                        throw ignore
                        break
                    }
                    // Do nothing.
                } finally {
                    Util.checkState(
                        this.extractor != null || extractorInput.position == position
                    )
                    extractorInput.resetPeekPosition()
                }
            }
            if (extractor == null) {
                throw UnSupportFormatException(
                    "None of the available extractors (${extractors.joinToString { extractor -> extractor?.javaClass?.simpleName ?: "" }} could read the stream. uri=$uri"
                )
            }
        }
        extractor?.init(output)
    }

    override fun reset() {
        extractor?.reset()
    }

    override fun release() {
        extractor?.release()
        extractor = null
        extractorInput = null
    }

    override fun init(dataQueue: DataQueue?) {
    }

    override fun getDataQueue(): DataQueue? {
        return extractor?.getDataQueue()
    }

    override fun sniff(extractorInput: ExtractorInput): Boolean {
        return false
    }

    override fun sample(input: ExtractorInput, seekPosition: PositionHolder): Int {
        // don't call this method
        throw UnsupportedOperationException()
    }

    override fun seek(timeUs: Long, position: Long) {
        extractor?.seek(timeUs, position)
    }

    override fun readSample(buffer: BufferHolder): Int {
        return extractor?.readSample(buffer) ?: -2
    }

    override fun timeToPosition(timeUs: Long): SeekPoint {
        val extractor = extractor ?: return SeekPoint.START
        return extractor.timeToPosition(timeUs)
    }

    @Throws(IOException::class)
    fun read(seekPosition: PositionHolder): Int {
        val extractorInput = extractorInput ?: return -2
        return extractor?.sample(extractorInput, seekPosition) ?: -2
    }

}