package com.lizhi.component.tekiplayer.engine

/**
 * 大的音频数据缓存块，大小最大为65536b。对应的音频包元数据由几个数组描述。
 */
class FrameDataNode private constructor() {

    companion object {
        const val CACHE_SIZE = 65536

        private var sPool: FrameDataNode? = null
        private var sPoolSize = 0
        private var sPoolSync = Any()

        fun obtain(position: Long): FrameDataNode {
            synchronized(sPoolSync) {
                val pool = sPool
                return if (pool == null) {
                    FrameDataNode().apply { startPosition = position }
                } else {
                    sPool = pool.next
                    sPoolSize--
                    pool.apply {
                        startPosition = position
                    }
                }
            }
        }
    }

    private var eofIndex: Int? = null

    var data: ByteArray = ByteArray(CACHE_SIZE)
    var startPosition = 0L
    var writtenBytes = 0
    val endPosition: Long
        get() = startPosition + writtenBytes
    val readEndOfStream: Boolean
        get() = readSampleIndex == eofIndex
    val currWriteOffset: Int
        get() = offsets[writeSampleIndex]
    val currPresentationTimeUs: Long
        get() = presentationTimeUss[writeSampleIndex]

    var presentationTimeUss = LongArray(160) { -1L }
    var sizes = IntArray(160)
    var offsets = IntArray(160)

    var readSampleIndex = 0
    var writeSampleIndex = 0
    var metadataSampleIndex = 0
    var maxSampleIndex = Int.MAX_VALUE
    var next: FrameDataNode? = null


    /**
     * 记录采样元数据
     * @return 返回剩余空间是否能满足当前帧
     */
    fun sampleMetadata(size: Int, presentationTimeUs: Long): Boolean {
        val writePosition: Int = if (metadataSampleIndex > 0) {
            offsets[metadataSampleIndex - 1] + sizes[metadataSampleIndex - 1]
        } else {
            0
        }
        // 最后的空间不满足一帧
        if (writePosition + size > CACHE_SIZE) {
            maxSampleIndex = metadataSampleIndex
//            TekiLog.w("frameNode", "maxSampleIndex=$maxSampleIndex")
            return false
        }
        // 赋值，当前的offset是上一个采样的offset+size
        offsets[metadataSampleIndex] = writePosition
        sizes[metadataSampleIndex] = size
        presentationTimeUss[metadataSampleIndex] = presentationTimeUs
        // 前进
        metadataSampleIndex += 1
        // 数据长度不足，扩容
        if (metadataSampleIndex == sizes.size) {
            double()
        }
//        TekiLog.w("frameNode", "sampleMetadata writeSampleIndex=$writeSampleIndex maxSampleIndex=$maxSampleIndex")
        return true
    }

    fun isFull(): Boolean {
//        TekiLog.w("frameNode", "writeSampleIndex=$writeSampleIndex maxSampleIndex=$maxSampleIndex")

        if (writeSampleIndex == maxSampleIndex) {
//            TekiLog.w("frameNode", "isFull writeSampleIndex=$writeSampleIndex maxSampleIndex=$maxSampleIndex")
            return true
        }
        return false
    }

    fun endSample(length: Int) {
        writtenBytes += length
        val writePosition = offsets[writeSampleIndex] + sizes[writeSampleIndex]
        if (writtenBytes == writePosition) {
            writeSampleIndex++
        }
    }

    fun endOfStream(next: Boolean = false) {
        eofIndex = writeSampleIndex
    }

    fun endRead(): Boolean {
        readSampleIndex += 1
        return readSampleIndex == maxSampleIndex
    }

    fun clear(): FrameDataNode? {
        val temp = next
        recycle()
        return temp
    }

    fun findSeekIndex(position: Long) {
        val offset = (position - startPosition).toInt()
        val containIndex = binarySearch0(offsets, 0, writeSampleIndex, offset)
        if (containIndex >= 0) {
            readSampleIndex = containIndex
        }
    }

    fun recycle() {
        writtenBytes = 0
        readSampleIndex = 0
        writeSampleIndex = 0
        startPosition = 0
        eofIndex = null
        next = null
        maxSampleIndex = Int.MAX_VALUE
        metadataSampleIndex = 0

        synchronized(sPoolSync) {
            if (sPoolSize >= 128) {
                return@synchronized
            }
            next = sPool
            sPoolSize++
            sPool = this
        }
    }

    private fun binarySearch0(
        a: IntArray, fromIndex: Int, toIndex: Int,
        key: Int
    ): Int {

        var low = fromIndex
        var high = toIndex - 1
        while (low <= high) {
            val mid = low + high ushr 1
            val midVal = a[mid]
            val midEndVal = a[mid + 1]
            when {
                key in midVal..midEndVal -> return mid
                midVal < key -> low = mid + 1
                midVal > key -> high = mid - 1
                else -> return -1
            } // key found
        }
        return -(low + 1) // key not found.
    }

    private fun double() {
        val newPresentations = LongArray(presentationTimeUss.size * 2)
        System.arraycopy(presentationTimeUss, 0, newPresentations, 0, presentationTimeUss.size)
        presentationTimeUss = newPresentations

        val newSizes = IntArray(sizes.size * 2)
        System.arraycopy(sizes, 0, newSizes, 0, sizes.size)
        sizes = newSizes

        val newOffsets = IntArray(offsets.size * 2)
        System.arraycopy(offsets, 0, newOffsets, 0, offsets.size)
        offsets = newOffsets
    }
}