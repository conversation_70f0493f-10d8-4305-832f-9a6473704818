package com.lizhi.component.tekiplayer.audioprogram.extractor.adts

import android.media.MediaFormat
import com.lizhi.component.tekiplayer.audioprogram.extractor.*
import com.lizhi.component.tekiplayer.audioprogram.extractor.Extractor.Companion.RESULT_CONTINUE
import com.lizhi.component.tekiplayer.audioprogram.extractor.Extractor.Companion.RESULT_END_OF_INPUT
import com.lizhi.component.tekiplayer.audioprogram.extractor.Id3Peeker.ID3_HEADER_LENGTH
import com.lizhi.component.tekiplayer.audioprogram.extractor.Id3Peeker.ID3_TAG
import com.lizhi.component.tekiplayer.audioprogram.extractor.mp3.ConstantBitrateSeeker
import com.lizhi.component.tekiplayer.engine.BufferHolder
import com.lizhi.component.tekiplayer.engine.DataQueue
import com.lizhi.component.tekiplayer.engine.exception.UnSupportFormatException
import com.lizhi.component.tekiplayer.util.Util
import java.io.EOFException
import java.io.IOException

/**
 * 文件名：AdtsExtractor
 * 作用：AAC音频解析封装
 * 作者：liaodongming
 * 创建日期：2021/2/20
 */
class AdtsExtractor : Extractor {

    companion object {
        private const val MAX_PACKET_SIZE = 2 * 1024

        private const val TS_PACKET_SIZE = 188

        /**
         * The maximum number of bytes to search when sniffing, excluding the header, before giving up.
         * Frame sizes are represented by 13-bit fields, so expect a valid frame in the first 8192 bytes.
         */
        private val MAX_SNIFF_BYTES = 8 * 1024

        /**
         * The maximum number of frames to use when calculating the average frame size for constant
         * bitrate seeking.
         */
        private val NUM_FRAMES_FOR_AVERAGE_FRAME_SIZE = 1000
    }

    private val reader: AdtsReader = AdtsReader()
    private val packetBuffer: ParsableByteArray
    private val scratch: ParsableByteArray
    private val scratchBits: ParsableBitArray

    private var firstSampleTimestampUs: Long = 0
    private var firstFramePosition: Long = 0
    private var averageFrameSize = 0
    private var hasCalculatedAverageFrameSize = false
    private var startedPacket = false
    private var hasOutputSeekMap = false
    private var dataQueue: DataQueue? = null

    override val mediaFormat: MediaFormat?
        get() = reader.format
    override val durationUs: Long?
        get() = seeker?.durationUs
    override var seeker: Seeker? = null
        private set

    init {
        packetBuffer = ParsableByteArray(MAX_PACKET_SIZE)
        averageFrameSize = Util.LENGTH_UNSET.toInt()
        firstFramePosition = Util.POSITION_UNSET
        // Allocate scratch space for an ID3 header. The same buffer is also used to read 4 byte values.
        scratch = ParsableByteArray(ID3_HEADER_LENGTH)
        scratchBits = ParsableBitArray(scratch.data)

    }

    override fun init(dataQueue: DataQueue?) {
        this.dataQueue = dataQueue
        reader.createTracks(dataQueue)
    }

    override fun getDataQueue(): DataQueue? {
        return dataQueue
    }

    override fun sniff(extractorInput: ExtractorInput): Boolean {
        // Skip any ID3 headers.
        val startPosition: Int = peekId3Header(extractorInput)

        // Try to find four or more consecutive AAC audio frames, exceeding the MPEG TS packet size.
        var headerPosition = startPosition
        var totalValidFramesSize = 0
        var validFramesCount = 0
        while (true) {
            extractorInput.peekFully(scratch.data, 0, 2)
            scratch.position = 0
            val syncBytes = scratch.readUnsignedShort()
            if (!AdtsReader.isAdtsSyncWord(syncBytes)) {
                validFramesCount = 0
                totalValidFramesSize = 0
                extractorInput.resetPeekPosition()
                if (++headerPosition - startPosition >= MAX_SNIFF_BYTES) {
                    return false
                }
                extractorInput.advancePeekPosition(headerPosition)
            } else {
                if (++validFramesCount >= 4) {
                    return true
                }

                // Skip the frame.
                extractorInput.peekFully(scratch.data, 0, 4)
                scratchBits.position = 14
                val frameSize = scratchBits.readBits(13)
                // Either the stream is malformed OR we're not parsing an ADTS stream.
                if (frameSize <= 6) {
                    return false
                }
                extractorInput.advancePeekPosition(frameSize - 6)
                totalValidFramesSize += frameSize
            }
        }
    }

    override fun sample(input: ExtractorInput, seekPosition: PositionHolder): Int {
        val inputLength: Long = input.length
        calculateAverageFrameSize(input)

        val bytesRead = input.read(packetBuffer.data, 0, MAX_PACKET_SIZE)
        val readEndOfStream = bytesRead == RESULT_END_OF_INPUT
        maybeOutputSeekMap(inputLength, readEndOfStream)
        if (readEndOfStream) {
            dataQueue?.setEndFlag()
            return RESULT_END_OF_INPUT
        }

        // Feed whatever data we have to the reader, regardless of whether the read finished or not.
        packetBuffer.position = 0
        packetBuffer.setLimit(bytesRead)

        if (!startedPacket) {
            // Pass data to the reader as though it's contained within a single infinitely long packet.
            reader.packetStarted(firstSampleTimestampUs, 0)
            startedPacket = true
        }
        // TODO: Make it possible for reader to consume the dataSource directly, so that it becomes
        // unnecessary to copy the data through packetBuffer.
        reader.consume(packetBuffer)
        return RESULT_CONTINUE
    }

    override fun seek(timeUs: Long, position: Long) {
        startedPacket = false
        reader.seek()
        firstSampleTimestampUs = timeUs
    }

    override fun readSample(buffer: BufferHolder): Int {
        return dataQueue?.blockDequeue(buffer) ?: -2
    }

    override fun reset() {
    }

    override fun timeToPosition(timeUs: Long): SeekPoint {
        return seeker?.getPositionAtTimeUs(timeUs) ?: SeekPoint(-1, -1)
    }

    override fun release() {
    }

    @Throws(IOException::class)
    private fun peekId3Header(input: ExtractorInput): Int {
        var firstFramePosition = 0
        while (true) {
            input.peekFully(scratch.data, 0, ID3_HEADER_LENGTH)
            scratch.position = 0
            if (scratch.readUnsignedInt24() != ID3_TAG) {
                break
            }
            scratch.skipBytes(3)
            val length = scratch.readSynchSafeInt()
            firstFramePosition += ID3_HEADER_LENGTH + length
            input.advancePeekPosition(length)
        }
        input.resetPeekPosition()
        input.advancePeekPosition(firstFramePosition)
        if (this.firstFramePosition == Util.POSITION_UNSET) {
            this.firstFramePosition = firstFramePosition.toLong()
        }
        return firstFramePosition
    }

    private fun maybeOutputSeekMap(
        inputLength: Long,
        readEndOfStream: Boolean
    ) {
        if (hasOutputSeekMap) {
            return
        }
        val useConstantBitrateSeeking = averageFrameSize > 0
        if (useConstantBitrateSeeking && reader.sampleDurationUs == Util.TIME_UNSET && !readEndOfStream) {
            // Wait for the sampleDurationUs to be available, or for the end of the stream to be reached,
            // before creating seek map.
            return
        }
        if (reader.sampleDurationUs != Util.TIME_UNSET) {
            seeker = getConstantBitrateSeekMap(inputLength)
        }
        hasOutputSeekMap = true
    }


    @Throws(IOException::class)
    private fun calculateAverageFrameSize(input: ExtractorInput) {
        if (hasCalculatedAverageFrameSize) {
            return
        }
        averageFrameSize = Util.LENGTH_UNSET.toInt()
        input.resetPeekPosition()
        if (input.position == 0L) {
            // Skip any ID3 headers.
            peekId3Header(input)
        }
        var numValidFrames = 0
        var totalValidFramesSize: Long = 0
        try {
            while (input.peekFully(scratch.data, 0, 2, true)) {
                scratch.position = 0
                val syncBytes = scratch.readUnsignedShort()
                if (!AdtsReader.isAdtsSyncWord(syncBytes)) {
                    // Invalid sync byte pattern.
                    // Constant bit-rate seeking will probably fail for this stream.
                    numValidFrames = 0
                    break
                } else {
                    // Read the frame size.
                    if (!input.peekFully(scratch.data, 0, 4, true)) {
                        break
                    }
                    scratchBits.position = 14
                    val currentFrameSize = scratchBits.readBits(13)
                    // Either the stream is malformed OR we're not parsing an ADTS stream.
                    if (currentFrameSize <= 6) {
                        hasCalculatedAverageFrameSize = true
                        throw UnSupportFormatException("Malformed ADTS stream")
                    }
                    totalValidFramesSize += currentFrameSize.toLong()
                    if (++numValidFrames == NUM_FRAMES_FOR_AVERAGE_FRAME_SIZE) {
                        break
                    }
                    if (!input.advancePeekPosition(currentFrameSize - 6, true)) {
                        break
                    }
                }
            }
        } catch (e: EOFException) {
            // We reached the end of the input during a peekFully() or advancePeekPosition() operation.
            // This is OK, it just means the input has an incomplete ADTS frame at the end. Ideally
            // ExtractorInput would allow these operations to encounter end-of-input without throwing an
            // exception [internal: b/145586657].
        }
        input.resetPeekPosition()
        averageFrameSize = if (numValidFrames > 0) {
            (totalValidFramesSize / numValidFrames).toInt()
        } else {
            Util.LENGTH_UNSET.toInt()
        }
        hasCalculatedAverageFrameSize = true
    }

    private fun getConstantBitrateSeekMap(inputLength: Long): Seeker? {
        val bitrate = getBitrateFromFrameSize(averageFrameSize, reader.sampleDurationUs)
        return ConstantBitrateSeeker(inputLength, firstFramePosition, bitrate, averageFrameSize)
    }

    /**
     * Returns the stream bitrate, given a frame size and the duration of that frame in microseconds.
     *
     * @param frameSize The size of each frame in the stream.
     * @param durationUsPerFrame The duration of the given frame in microseconds.
     * @return The stream bitrate.
     */
    private fun getBitrateFromFrameSize(frameSize: Int, durationUsPerFrame: Long): Int {
        return (frameSize * Util.BITS_PER_BYTE * Util.MICROS_PER_SECOND / durationUsPerFrame).toInt()
    }
}