/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.lizhi.component.tekiplayer.audioprogram.extractor.mpeg4;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.Nullable;

import com.lizhi.component.tekiplayer.util.audio.MimeTypes;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Represents a media format.
 *
 * <p>When building formats, populate all fields whose values are known and relevant to the type of
 * format being constructed. For information about different types of format, see ExoPlayer's <a
 * href="https://exoplayer.dev/supported-formats.html">Supported formats page</a>.
 *
 * <h3>Fields commonly relevant to all formats</h3>
 *
 * <ul>
 *   <li>{@link #id}
 *   <li>{@link #label}
 *   <li>{@link #selectionFlags}
 *   <li>{@link #roleFlags}
 *   <li>{@link #averageBitrate}
 *   <li>{@link #peakBitrate}
 *   <li>{@link #codecs}
 *   <li>{@link #metadata}
 * </ul>
 *
 * <h3 id="container-formats">Fields relevant to container formats</h3>
 *
 * <ul>
 *   <li>{@link #containerMimeType}
 *   <li>If the container only contains a single media track, <a href="#sample-formats">fields
 *       relevant to sample formats</a> can are also be relevant and can be set to describe the
 *       sample format of that track.
 *   <li>If the container only contains one track of a given type (possibly alongside tracks of
 *       other types), then fields relevant to that track type can be set to describe the properties
 *       of the track. See the sections below for <a href="#video-formats">video</a>, <a
 *       href="#audio-formats">audio</a> and <a href="#text-formats">text</a> formats.
 * </ul>
 *
 * <h3 id="sample-formats">Fields relevant to sample formats</h3>
 *
 * <ul>
 *   <li>{@link #sampleMimeType}
 *   <li>{@link #maxInputSize}
 *   <li>{@link #initializationData}
 *   <li>{@link #subsampleOffsetUs}
 *   <li>Fields relevant to the sample format's track type are also relevant. See the sections below
 *       for <a href="#video-formats">video</a>, <a href="#audio-formats">audio</a> and <a
 *       href="#text-formats">text</a> formats.
 * </ul>
 *
 * <h3 id="video-formats">Fields relevant to video formats</h3>
 *
 *
 * <h3 id="audio-formats">Fields relevant to audio formats</h3>
 *
 * <ul>
 *   <li>{@link #channelCount}
 *   <li>{@link #sampleRate}
 *   <li>{@link #pcmEncoding}
 *   <li>{@link #encoderDelay}
 *   <li>{@link #encoderPadding}
 * </ul>
 *
 */
public final class Format implements Parcelable {

    /**
     * Builds {@link Format} instances.
     *
     * <p>Use Format#buildUpon() to obtain a builder representing an existing {@link Format}.
     *
     * <p>When building formats, populate all fields whose values are known and relevant to the type
     * of format being constructed. See the {@link Format} Javadoc for information about which fields
     * should be set for different types of format.
     */
    public static final class Builder {

        @Nullable
        private String id;
        @Nullable
        private String label;
        private int selectionFlags;
        private int roleFlags;
        private int averageBitrate;
        private int peakBitrate;
        @Nullable
        private String codecs;
        @Nullable
        private Metadata metadata;

        // Container specific.

        @Nullable
        private String containerMimeType;

        // Sample specific.

        @Nullable
        private String sampleMimeType;
        private int maxInputSize;
        @Nullable
        private List<byte[]> initializationData;
        private long subsampleOffsetUs;

        // Audio specific.

        private int channelCount;
        private int sampleRate;
        private int pcmEncoding;
        private int encoderDelay;
        private int encoderPadding;

        // Provided by the source.

        /**
         * Creates a new instance with default values.
         */
        public Builder() {
            averageBitrate = NO_VALUE;
            peakBitrate = NO_VALUE;
            // Sample specific.
            maxInputSize = NO_VALUE;
            subsampleOffsetUs = OFFSET_SAMPLE_RELATIVE;
            // Audio specific.
            channelCount = NO_VALUE;
            sampleRate = NO_VALUE;
            pcmEncoding = NO_VALUE;
        }

        /**
         * Creates a new instance to build upon the provided {@link Format}.
         *
         * @param format The {@link Format} to build upon.
         */
        private Builder(Format format) {
            this.id = format.id;
            this.label = format.label;
            this.selectionFlags = format.selectionFlags;
            this.roleFlags = format.roleFlags;
            this.averageBitrate = format.averageBitrate;
            this.peakBitrate = format.peakBitrate;
            this.codecs = format.codecs;
            this.metadata = format.metadata;
            // Container specific.
            this.containerMimeType = format.containerMimeType;
            // Sample specific.
            this.sampleMimeType = format.sampleMimeType;
            this.maxInputSize = format.maxInputSize;
            this.initializationData = format.initializationData;
            this.subsampleOffsetUs = format.subsampleOffsetUs;
            // Audio specific.
            this.channelCount = format.channelCount;
            this.sampleRate = format.sampleRate;
            this.pcmEncoding = format.pcmEncoding;
            this.encoderDelay = format.encoderDelay;
            this.encoderPadding = format.encoderPadding;
        }

        /**
         * Sets {@link Format#id}. The default value is {@code null}.
         *
         * @param id The {@link Format#id}.
         * @return The builder.
         */
        public Builder setId(@Nullable String id) {
            this.id = id;
            return this;
        }

        /**
         * Sets {@link Format#id} to {@link Integer#toString() Integer.toString(id)}. The default value
         * is {@code null}.
         *
         * @param id The {@link Format#id}.
         * @return The builder.
         */
        public Builder setId(int id) {
            this.id = Integer.toString(id);
            return this;
        }

        /**
         * Sets {@link Format#label}. The default value is {@code null}.
         *
         * @param label The {@link Format#label}.
         * @return The builder.
         */
        public Builder setLabel(@Nullable String label) {
            this.label = label;
            return this;
        }

        /**
         * Sets {@link Format#selectionFlags}. The default value is 0.
         *
         * @param selectionFlags The {@link Format#selectionFlags}.
         * @return The builder.
         */
        public Builder setSelectionFlags(int selectionFlags) {
            this.selectionFlags = selectionFlags;
            return this;
        }

        /**
         * Sets {@link Format#roleFlags}. The default value is 0.
         *
         * @param roleFlags The {@link Format#roleFlags}.
         * @return The builder.
         */
        public Builder setRoleFlags(int roleFlags) {
            this.roleFlags = roleFlags;
            return this;
        }

        /**
         * Sets {@link Format#averageBitrate}. The default value is {@link #NO_VALUE}.
         *
         * @param averageBitrate The {@link Format#averageBitrate}.
         * @return The builder.
         */
        public Builder setAverageBitrate(int averageBitrate) {
            this.averageBitrate = averageBitrate;
            return this;
        }

        /**
         * Sets {@link Format#peakBitrate}. The default value is {@link #NO_VALUE}.
         *
         * @param peakBitrate The {@link Format#peakBitrate}.
         * @return The builder.
         */
        public Builder setPeakBitrate(int peakBitrate) {
            this.peakBitrate = peakBitrate;
            return this;
        }

        /**
         * Sets {@link Format#codecs}. The default value is {@code null}.
         *
         * @param codecs The {@link Format#codecs}.
         * @return The builder.
         */
        public Builder setCodecs(@Nullable String codecs) {
            this.codecs = codecs;
            return this;
        }

        /**
         * Sets {@link Format#metadata}. The default value is {@code null}.
         *
         * @param metadata The {@link Format#metadata}.
         * @return The builder.
         */
        public Builder setMetadata(@Nullable Metadata metadata) {
            this.metadata = metadata;
            return this;
        }

        // Container specific.

        /**
         * Sets {@link Format#containerMimeType}. The default value is {@code null}.
         *
         * @param containerMimeType The {@link Format#containerMimeType}.
         * @return The builder.
         */
        public Builder setContainerMimeType(@Nullable String containerMimeType) {
            this.containerMimeType = containerMimeType;
            return this;
        }

        // Sample specific.

        /**
         * Sets {@link Format#sampleMimeType}. The default value is {@code null}.
         *
         * @param sampleMimeType {@link Format#sampleMimeType}.
         * @return The builder.
         */
        public Builder setSampleMimeType(@Nullable String sampleMimeType) {
            this.sampleMimeType = sampleMimeType;
            return this;
        }

        /**
         * Sets {@link Format#maxInputSize}. The default value is {@link #NO_VALUE}.
         *
         * @param maxInputSize The {@link Format#maxInputSize}.
         * @return The builder.
         */
        public Builder setMaxInputSize(int maxInputSize) {
            this.maxInputSize = maxInputSize;
            return this;
        }

        /**
         * Sets {@link Format#initializationData}. The default value is {@code null}.
         *
         * @param initializationData The {@link Format#initializationData}.
         * @return The builder.
         */
        public Builder setInitializationData(@Nullable List<byte[]> initializationData) {
            this.initializationData = initializationData;
            return this;
        }


        /**
         * Sets {@link Format#subsampleOffsetUs}. The default value is {@link #OFFSET_SAMPLE_RELATIVE}.
         *
         * @param subsampleOffsetUs The {@link Format#subsampleOffsetUs}.
         * @return The builder.
         */
        public Builder setSubsampleOffsetUs(long subsampleOffsetUs) {
            this.subsampleOffsetUs = subsampleOffsetUs;
            return this;
        }

        // Audio specific.

        /**
         * Sets {@link Format#channelCount}. The default value is {@link #NO_VALUE}.
         *
         * @param channelCount The {@link Format#channelCount}.
         * @return The builder.
         */
        public Builder setChannelCount(int channelCount) {
            this.channelCount = channelCount;
            return this;
        }

        /**
         * Sets {@link Format#sampleRate}. The default value is {@link #NO_VALUE}.
         *
         * @param sampleRate The {@link Format#sampleRate}.
         * @return The builder.
         */
        public Builder setSampleRate(int sampleRate) {
            this.sampleRate = sampleRate;
            return this;
        }

        /**
         * Sets {@link Format#pcmEncoding}. The default value is {@link #NO_VALUE}.
         *
         * @param pcmEncoding The {@link Format#pcmEncoding}.
         * @return The builder.
         */
        public Builder setPcmEncoding(int pcmEncoding) {
            this.pcmEncoding = pcmEncoding;
            return this;
        }

        /**
         * Sets {@link Format#encoderDelay}. The default value is 0.
         *
         * @param encoderDelay The {@link Format#encoderDelay}.
         * @return The builder.
         */
        public Builder setEncoderDelay(int encoderDelay) {
            this.encoderDelay = encoderDelay;
            return this;
        }

        /**
         * Sets {@link Format#encoderPadding}. The default value is 0.
         *
         * @param encoderPadding The {@link Format#encoderPadding}.
         * @return The builder.
         */
        public Builder setEncoderPadding(int encoderPadding) {
            this.encoderPadding = encoderPadding;
            return this;
        }

        // Build.

        public Format build() {
            return new Format(/* builder= */ this);
        }
    }

    /**
     * A value for various fields to indicate that the field's value is unknown or not applicable.
     */
    public static final int NO_VALUE = -1;

    /**
     * A value for {@link #subsampleOffsetUs} to indicate that subsample timestamps are relative to
     * the timestamps of their parent samples.
     */
    public static final long OFFSET_SAMPLE_RELATIVE = Long.MAX_VALUE;

    /**
     * An identifier for the format, or null if unknown or not applicable.
     */
    @Nullable
    public final String id;
    /**
     * The human readable label, or null if unknown or not applicable.
     */
    @Nullable
    public final String label;
    /**
     * Track selection flags.
     */
    public final int selectionFlags;
    /**
     * Track role flags.
     */
    public final int roleFlags;
    /**
     * The average bitrate in bits per second, or {@link #NO_VALUE} if unknown or not applicable. The
     * way in which this field is populated depends on the type of media to which the format
     * corresponds:
     *
     * <ul>
     *   <li>DASH representations: Always {@link Format#NO_VALUE}.
     *   <li>HLS variants: The {@code AVERAGE-BANDWIDTH} attribute defined on the corresponding {@code
     *       EXT-X-STREAM-INF} tag in the master playlist, or {@link Format#NO_VALUE} if not present.
     *   <li>SmoothStreaming track elements: The {@code Bitrate} attribute defined on the
     *       corresponding {@code TrackElement} in the manifest, or {@link Format#NO_VALUE} if not
     *       present.
     *   <li>Progressive container formats: Often {@link Format#NO_VALUE}, but may be populated with
     *       the average bitrate of the container if known.
     *   <li>Sample formats: Often {@link Format#NO_VALUE}, but may be populated with the average
     *       bitrate of the stream of samples with type {@link #sampleMimeType} if known. Note that if
     *       {@link #sampleMimeType} is a compressed format (e.g., {@link MimeTypes#AUDIO_AAC}), then
     *       this bitrate is for the stream of still compressed samples.
     * </ul>
     */
    public final int averageBitrate;
    /**
     * The peak bitrate in bits per second, or {@link #NO_VALUE} if unknown or not applicable. The way
     * in which this field is populated depends on the type of media to which the format corresponds:
     *
     * <ul>
     *   <li>DASH representations: The {@code @bandwidth} attribute of the corresponding {@code
     *       Representation} element in the manifest.
     *   <li>HLS variants: The {@code BANDWIDTH} attribute defined on the corresponding {@code
     *       EXT-X-STREAM-INF} tag.
     *   <li>SmoothStreaming track elements: Always {@link Format#NO_VALUE}.
     *   <li>Progressive container formats: Often {@link Format#NO_VALUE}, but may be populated with
     *       the peak bitrate of the container if known.
     *   <li>Sample formats: Often {@link Format#NO_VALUE}, but may be populated with the peak bitrate
     *       of the stream of samples with type {@link #sampleMimeType} if known. Note that if {@link
     *       #sampleMimeType} is a compressed format (e.g., {@link MimeTypes#AUDIO_AAC}), then this
     *       bitrate is for the stream of still compressed samples.
     * </ul>
     */
    public final int peakBitrate;
    /**
     * The bitrate in bits per second. This is the peak bitrate if known, or else the average bitrate
     * if known, or else {@link Format#NO_VALUE}. Equivalent to: {@code peakBitrate != NO_VALUE ?
     * peakBitrate : averageBitrate}.
     */
    public final int bitrate;
    /**
     * Codecs of the format as described in RFC 6381, or null if unknown or not applicable.
     */
    @Nullable
    public final String codecs;
    /**
     * Metadata, or null if unknown or not applicable.
     */
    @Nullable
    public final Metadata metadata;

    // Container specific.

    /**
     * The mime type of the container, or null if unknown or not applicable.
     */
    @Nullable
    public final String containerMimeType;

    // Sample specific.

    /**
     * The sample mime type, or null if unknown or not applicable.
     */
    @Nullable
    public final String sampleMimeType;
    /**
     * The maximum size of a buffer of data (typically one sample), or {@link #NO_VALUE} if unknown or
     * not applicable.
     */
    public final int maxInputSize;
    /**
     * Initialization data that must be provided to the decoder. Will not be null, but may be empty
     * if initialization data is not required.
     */
    public final List<byte[]> initializationData;

    /**
     * For samples that contain subsamples, this is an offset that should be added to subsample
     * timestamps. A value of {@link #OFFSET_SAMPLE_RELATIVE} indicates that subsample timestamps are
     * relative to the timestamps of their parent samples.
     */
    public final long subsampleOffsetUs;

    // Audio specific.

    /**
     * The number of audio channels, or {@link #NO_VALUE} if unknown or not applicable.
     */
    public final int channelCount;
    /**
     * The audio sampling rate in Hz, or {@link #NO_VALUE} if unknown or not applicable.
     */
    public final int sampleRate;
    /**
     * The {@link } for PCM audio. Set to {@link #NO_VALUE} for other media types.
     */
    public final int pcmEncoding;
    /**
     * The number of frames to trim from the start of the decoded audio stream, or 0 if not
     * applicable.
     */
    public final int encoderDelay;
    /**
     * The number of frames to trim from the end of the decoded audio stream, or 0 if not applicable.
     */
    public final int encoderPadding;

    // Lazily initialized hashcode.
    private int hashCode;

    // Audio.

    /**
     * @deprecated Use {@link Format.Builder}.
     */
    @Deprecated
    public static Format createAudioContainerFormat(
            @Nullable String id,
            @Nullable String label,
            @Nullable String containerMimeType,
            @Nullable String sampleMimeType,
            @Nullable String codecs,
            @Nullable Metadata metadata,
            int bitrate,
            int channelCount,
            int sampleRate,
            @Nullable List<byte[]> initializationData,
            int selectionFlags,
            int roleFlags) {
        return new Builder()
                .setId(id)
                .setLabel(label)
                .setSelectionFlags(selectionFlags)
                .setRoleFlags(roleFlags)
                .setAverageBitrate(bitrate)
                .setPeakBitrate(bitrate)
                .setCodecs(codecs)
                .setMetadata(metadata)
                .setContainerMimeType(containerMimeType)
                .setSampleMimeType(sampleMimeType)
                .setInitializationData(initializationData)
                .setChannelCount(channelCount)
                .setSampleRate(sampleRate)
                .build();
    }

    // Generic.

    /**
     * @deprecated Use {@link Format.Builder}.
     */
    @Deprecated
    public static Format createContainerFormat(
            @Nullable String id,
            @Nullable String label,
            @Nullable String containerMimeType,
            @Nullable String sampleMimeType,
            @Nullable String codecs,
            int bitrate,
            int selectionFlags,
            int roleFlags) {
        return new Builder()
                .setId(id)
                .setLabel(label)
                .setSelectionFlags(selectionFlags)
                .setRoleFlags(roleFlags)
                .setAverageBitrate(bitrate)
                .setPeakBitrate(bitrate)
                .setCodecs(codecs)
                .setContainerMimeType(containerMimeType)
                .setSampleMimeType(sampleMimeType)
                .build();
    }

    /**
     * @deprecated Use {@link Format.Builder}.
     */
    @Deprecated
    public static Format createSampleFormat(@Nullable String id, @Nullable String sampleMimeType) {
        return new Builder().setId(id).setSampleMimeType(sampleMimeType).build();
    }

    private Format(Builder builder) {
        id = builder.id;
        label = builder.label;
        selectionFlags = builder.selectionFlags;
        roleFlags = builder.roleFlags;
        averageBitrate = builder.averageBitrate;
        peakBitrate = builder.peakBitrate;
        bitrate = peakBitrate != NO_VALUE ? peakBitrate : averageBitrate;
        codecs = builder.codecs;
        metadata = builder.metadata;
        // Container specific.
        containerMimeType = builder.containerMimeType;
        // Sample specific.
        sampleMimeType = builder.sampleMimeType;
        maxInputSize = builder.maxInputSize;
        initializationData =
                builder.initializationData == null ? Collections.emptyList() : builder.initializationData;
        subsampleOffsetUs = builder.subsampleOffsetUs;
        // Audio specific.
        channelCount = builder.channelCount;
        sampleRate = builder.sampleRate;
        pcmEncoding = builder.pcmEncoding;
        encoderDelay = builder.encoderDelay == NO_VALUE ? 0 : builder.encoderDelay;
        encoderPadding = builder.encoderPadding == NO_VALUE ? 0 : builder.encoderPadding;
    }

    // Some fields are deprecated but they're still assigned below.
    @SuppressWarnings({"ResourceType"})
    /* package */ Format(Parcel in) {
        id = in.readString();
        label = in.readString();
        selectionFlags = in.readInt();
        roleFlags = in.readInt();
        averageBitrate = in.readInt();
        peakBitrate = in.readInt();
        bitrate = peakBitrate != NO_VALUE ? peakBitrate : averageBitrate;
        codecs = in.readString();
        metadata = in.readParcelable(Metadata.class.getClassLoader());
        // Container specific.
        containerMimeType = in.readString();
        // Sample specific.
        sampleMimeType = in.readString();
        maxInputSize = in.readInt();
        int initializationDataSize = in.readInt();
        initializationData = new ArrayList<>(initializationDataSize);
        for (int i = 0; i < initializationDataSize; i++) {
            initializationData.add(in.createByteArray());
        }
        subsampleOffsetUs = in.readLong();
        // Audio specific.
        channelCount = in.readInt();
        sampleRate = in.readInt();
        pcmEncoding = in.readInt();
        encoderDelay = in.readInt();
        encoderPadding = in.readInt();
    }

    /**
     * Returns a {@link Format.Builder} initialized with the values of this instance.
     */
    public Builder buildUpon() {
        return new Builder(this);
    }

    /**
     * @deprecated Use {@link #buildUpon()} and {@link Builder#setMaxInputSize(int)}.
     */
    @Deprecated
    public Format copyWithMaxInputSize(int maxInputSize) {
        return buildUpon().setMaxInputSize(maxInputSize).build();
    }

    /**
     * @deprecated Use {@link #buildUpon()} and {@link Builder#setSubsampleOffsetUs(long)}.
     */
    @Deprecated
    public Format copyWithSubsampleOffsetUs(long subsampleOffsetUs) {
        return buildUpon().setSubsampleOffsetUs(subsampleOffsetUs).build();
    }

    /**
     * @deprecated Use {@link #buildUpon()} and {@link Builder#setLabel(String)} .
     */
    @Deprecated
    public Format copyWithLabel(@Nullable String label) {
        return buildUpon().setLabel(label).build();
    }


    /**
     * @deprecated Use {@link #buildUpon()}, {@link Builder#setEncoderDelay(int)} and {@link
     * Builder#setEncoderPadding(int)}.
     */
    @Deprecated
    public Format copyWithGaplessInfo(int encoderDelay, int encoderPadding) {
        return buildUpon().setEncoderDelay(encoderDelay).setEncoderPadding(encoderPadding).build();
    }

    /**
     * @deprecated Use {@link #buildUpon()} and {@link Builder#setMetadata(Metadata)}.
     */
    @Deprecated
    public Format copyWithMetadata(@Nullable Metadata metadata) {
        return buildUpon().setMetadata(metadata).build();
    }

    /**
     * @deprecated Use {@link #buildUpon()} and {@link Builder#setAverageBitrate(int)} and {@link
     * Builder#setPeakBitrate(int)}.
     */
    @Deprecated
    public Format copyWithBitrate(int bitrate) {
        return buildUpon().setAverageBitrate(bitrate).setPeakBitrate(bitrate).build();
    }

    @Override
    public String toString() {
        return "Format("
                + id
                + ", "
                + label
                + ", "
                + containerMimeType
                + ", "
                + sampleMimeType
                + ", "
                + codecs
                + ", "
                + bitrate
                + "]"
                + ", ["
                + channelCount
                + ", "
                + sampleRate
                + "])";
    }

    @Override
    public int hashCode() {
        if (hashCode == 0) {
            // Some fields for which hashing is expensive are deliberately omitted.
            int result = 17;
            result = 31 * result + (id == null ? 0 : id.hashCode());
            result = 31 * result + (label != null ? label.hashCode() : 0);
            result = 31 * result + selectionFlags;
            result = 31 * result + roleFlags;
            result = 31 * result + averageBitrate;
            result = 31 * result + peakBitrate;
            result = 31 * result + (codecs == null ? 0 : codecs.hashCode());
            result = 31 * result + (metadata == null ? 0 : metadata.hashCode());
            // Container specific.
            result = 31 * result + (containerMimeType == null ? 0 : containerMimeType.hashCode());
            // Sample specific.
            result = 31 * result + (sampleMimeType == null ? 0 : sampleMimeType.hashCode());
            result = 31 * result + maxInputSize;
            // [Omitted] initializationData.
            // [Omitted] drmInitData.
            result = 31 * result + (int) subsampleOffsetUs;
            // Audio specific.
            result = 31 * result + channelCount;
            result = 31 * result + sampleRate;
            result = 31 * result + pcmEncoding;
            result = 31 * result + encoderDelay;
            result = 31 * result + encoderPadding;
            // Text specific.
            hashCode = result;
        }
        return hashCode;
    }

    @Override
    public boolean equals(@Nullable Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        Format other = (Format) obj;
        if (hashCode != 0 && other.hashCode != 0 && hashCode != other.hashCode) {
            return false;
        }
        // Field equality checks ordered by type, with the cheapest checks first.
        return selectionFlags == other.selectionFlags
                && roleFlags == other.roleFlags
                && averageBitrate == other.averageBitrate
                && peakBitrate == other.peakBitrate
                && maxInputSize == other.maxInputSize
                && subsampleOffsetUs == other.subsampleOffsetUs
                && channelCount == other.channelCount
                && sampleRate == other.sampleRate
                && pcmEncoding == other.pcmEncoding
                && encoderDelay == other.encoderDelay
                && encoderPadding == other.encoderPadding
                && initializationDataEquals(other);
    }

    /**
     * Returns whether the {@link #initializationData}s belonging to this format and {@code other} are
     * equal.
     *
     * @param other The other format whose {@link #initializationData} is being compared.
     * @return Whether the {@link #initializationData}s belonging to this format and {@code other} are
     * equal.
     */
    public boolean initializationDataEquals(Format other) {
        if (initializationData.size() != other.initializationData.size()) {
            return false;
        }
        for (int i = 0; i < initializationData.size(); i++) {
            if (!Arrays.equals(initializationData.get(i), other.initializationData.get(i))) {
                return false;
            }
        }
        return true;
    }

    // Utility methods

    /**
     * Returns a prettier {@link String} than {@link #toString()}, intended for logging.
     */
    public static String toLogString(@Nullable Format format) {
        if (format == null) {
            return "null";
        }
        StringBuilder builder = new StringBuilder();
        builder.append("id=").append(format.id).append(", mimeType=").append(format.sampleMimeType);
        if (format.bitrate != NO_VALUE) {
            builder.append(", bitrate=").append(format.bitrate);
        }
        if (format.codecs != null) {
            builder.append(", codecs=").append(format.codecs);
        }
        if (format.channelCount != NO_VALUE) {
            builder.append(", channels=").append(format.channelCount);
        }
        if (format.sampleRate != NO_VALUE) {
            builder.append(", sample_rate=").append(format.sampleRate);
        }
        if (format.label != null) {
            builder.append(", label=").append(format.label);
        }
        return builder.toString();
    }

    // Parcelable implementation.

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(id);
        dest.writeString(label);
        dest.writeInt(selectionFlags);
        dest.writeInt(roleFlags);
        dest.writeInt(averageBitrate);
        dest.writeInt(peakBitrate);
        dest.writeString(codecs);
        dest.writeParcelable(metadata, 0);
        // Container specific.
        dest.writeString(containerMimeType);
        // Sample specific.
        dest.writeString(sampleMimeType);
        dest.writeInt(maxInputSize);
        int initializationDataSize = initializationData.size();
        dest.writeInt(initializationDataSize);
        for (int i = 0; i < initializationDataSize; i++) {
            dest.writeByteArray(initializationData.get(i));
        }
        dest.writeLong(subsampleOffsetUs);
        // Audio specific.
        dest.writeInt(channelCount);
        dest.writeInt(sampleRate);
        dest.writeInt(pcmEncoding);
        dest.writeInt(encoderDelay);
        dest.writeInt(encoderPadding);
    }

    public static final Creator<Format> CREATOR = new Creator<Format>() {

        @Override
        public Format createFromParcel(Parcel in) {
            return new Format(in);
        }

        @Override
        public Format[] newArray(int size) {
            return new Format[size];
        }

    };
}
