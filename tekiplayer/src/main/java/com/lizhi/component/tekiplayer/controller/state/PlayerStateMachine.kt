package com.lizhi.component.tekiplayer.controller.state

import com.lizhi.component.tekiplayer.controller.PlayerState
import com.lizhi.component.tekiplayer.util.TekiLog

/**
 * 文件名：PlayerStateManager
 * 作用：播放器状态机
 * 作者：huang<PERSON><PERSON>
 * 创建日期：2021/4/6
 */
class PlayerStateMachine(
    private val listener: StateChangeListener? = null
) : StateMachine {

    companion object {
        private const val TAG = "SimplePlayerStateManager"
    }

    private val stateMachine = SimpleStateMachine.create<Int, Int, Int> {
        // 初始状态
        setInitState(PlayerState.STATE_IDLE)

        // 定义各个状态和状态转换
        state(PlayerState.STATE_IDLE) {
            on(PlayerEvent.EVENT_PREPARE, PlayerEvent.EVENT_PLAY) {
                transitionTo(PlayerState.STATE_BUFFERING)
            }
        }

        state(PlayerState.STATE_BUFFERING) {
            on(PlayerEvent.EVENT_BUFFERED_ENOUGH) {
                transitionTo(PlayerState.STATE_READY)
            }
            on(PlayerEvent.EVENT_STOP, PlayerEvent.EVENT_AUDIO_ERROR) {
                transitionTo(PlayerState.STATE_ENDED)
            }
        }

        state(PlayerState.STATE_READY) {
            on(PlayerEvent.EVENT_NEED_MORE_DATA) {
                transitionTo(PlayerState.STATE_BUFFERING)
            }
            on(PlayerEvent.EVENT_PLAY, PlayerEvent.EVENT_RESUME) {
                transitionTo(PlayerState.STATE_PLAYING)
            }
            on(PlayerEvent.EVENT_STOP, PlayerEvent.EVENT_ENDED) {
                transitionTo(PlayerState.STATE_ENDED)
            }
        }

        state(PlayerState.STATE_PLAYING) {
            on(
                PlayerEvent.EVENT_PAUSE,
                PlayerEvent.EVENT_FOCUS_LOST_PAUSED
            ) {
                transitionTo(PlayerState.STATE_PAUSED)
            }
            on(
                PlayerEvent.EVENT_STOP,
                PlayerEvent.EVENT_AUDIO_ERROR,
                PlayerEvent.EVENT_ENDED
            ) {
                transitionTo(PlayerState.STATE_ENDED)
            }
            on(PlayerEvent.EVENT_NEED_MORE_DATA) {
                transitionTo(PlayerState.STATE_BUFFERING)
            }
        }

        state(PlayerState.STATE_PAUSED) {
            on(PlayerEvent.EVENT_RESUME) {
                transitionTo(PlayerState.STATE_PLAYING)
            }
            on(PlayerEvent.EVENT_STOP, PlayerEvent.EVENT_ENDED) {
                transitionTo(PlayerState.STATE_ENDED)
            }
            on(PlayerEvent.EVENT_NEED_MORE_DATA) {
                transitionTo(PlayerState.STATE_BUFFERING)
            }
        }

        state(PlayerState.STATE_ENDED) {
            on(PlayerEvent.EVENT_PREPARE, PlayerEvent.EVENT_PLAY) {
                transitionTo(PlayerState.STATE_BUFFERING)
            }
        }

        onStateExit { state, causeEvent ->
            TekiLog.i(
                TAG,
                "from ${PlayerState.getStateName(state)} to ${PlayerState.getStateName(<EMAIL>)} because $causeEvent"
            )
            listener?.onStateExit(state, causeEvent)
        }

        onStateEnter { state, causeEvent ->
            listener?.onStateEnter(state, causeEvent)
        }

        onSideEffectOccurBlock {
            // 目前没有定义任何副作用
        }
    }

    @PlayerState.State
    override val state: Int
        get() = stateMachine.curState

    override fun sendEvent(@PlayerEvent.Event event: Int) {
        TekiLog.i(TAG, "receiveEvent ${PlayerEvent.getEventText(event)}")
        stateMachine.sendEvent(event)
    }

}