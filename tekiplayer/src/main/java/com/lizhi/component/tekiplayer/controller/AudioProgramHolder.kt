package com.lizhi.component.tekiplayer.controller

import com.lizhi.component.tekiplayer.MediaItem
import com.lizhi.component.tekiplayer.Player
import com.lizhi.component.tekiplayer.audioprogram.Program
import com.lizhi.component.tekiplayer.util.TekiLog

/**
 * 文件名：AudioProgramHolder
 * 作用：AudioProgram链表封装
 * 作者：huangtianhao
 * 创建日期：2021/3/27
 */
class AudioProgramHolder(
    var position: Int,
    val mediaItem: MediaItem,
    val program: Program,
    val quality: Player.Quality
) : Iterable<AudioProgramHolder> {

    var next: AudioProgramHolder? = null
        private set

    var previous: AudioProgramHolder? = null
        private set

    var isPausedPreload: Boolean = false

    private inner class Itr : AbstractIterator<AudioProgramHolder>() {
        var nextItem: AudioProgramHolder? = this@AudioProgramHolder
        override fun computeNext() {
            val localNextItem = nextItem
            if (localNextItem == null) {
                done()
            } else {
                setNext(localNextItem)
                nextItem = localNextItem.next
            }
        }
    }

    fun setNextItem(audioProgramHolder: AudioProgramHolder?) {
        if (audioProgramHolder == this) {
            return
        }
        this.next = audioProgramHolder
        audioProgramHolder?.previous = this
    }

    fun hasPrevious() = previous != null

    fun last(): AudioProgramHolder {
        var curItem = this

        this.forEach { holder ->
            TekiLog.i("AudioProgramHolder", "get last() for each")
            curItem = holder
        }

        return curItem
    }

    fun first(): AudioProgramHolder {
        var cur = this
        var previous = this.previous
        while (previous != null) {
            cur = previous
            previous = cur.previous
        }
        return cur
    }

    fun clearLink() {
        this.previous = null
        this.next = null
    }

    fun clearPrevious() {
        this.previous = null
    }

    fun removeItem(audioProgramHolder: AudioProgramHolder?) {
        audioProgramHolder ?: return

        audioProgramHolder.previous?.next = audioProgramHolder.next
        audioProgramHolder.next?.previous = audioProgramHolder.previous
        audioProgramHolder.clearLink()
    }

    fun isExisted(audioProgramHolder: AudioProgramHolder?): Boolean {
        audioProgramHolder ?: return false

        first().forEach {
            if (it == audioProgramHolder) {
                return true
            }
        }
        return false
    }

    fun add(audioProgramHolder: AudioProgramHolder?) {
        audioProgramHolder ?: return
        if (audioProgramHolder == this) {
            return
        }
        audioProgramHolder.next = null
        last().setNextItem(audioProgramHolder)
    }

    fun size(): Int {
        var length = 0
        this.forEach { _ -> length++ }
        return length
    }

    override fun iterator(): Iterator<AudioProgramHolder> {
        return Itr()
    }

    override fun toString(): String {
        return this.fold("program list:"){ acc, audioProgramHolder ->
            "$acc -> ${audioProgramHolder.position}"
        }
    }
}