package com.lizhi.component.tekiplayer.audioprogram.request

import android.net.Uri
import com.lizhi.component.tekiplayer.audioprogram.request.RequestControl.RetryUri.Companion.ERROR
import com.lizhi.component.tekiplayer.audioprogram.request.RequestControl.RetryUri.Companion.FATAL_ERROR
import com.lizhi.component.tekiplayer.controller.cdn.CdnListHolder
import com.lizhi.component.tekiplayer.datasource.exception.IllegalContentTypeException
import com.lizhi.component.tekiplayer.datasource.exception.InvalidResponseCodeException
import com.lizhi.component.tekiplayer.datasource.exception.OutOfRangeException
import com.lizhi.component.tekiplayer.util.TekiLog
import com.lizhi.component.tekiplayer.util.aes.CipherException
import java.io.IOException
import java.net.URI
import java.util.regex.Pattern

/**
 * 文件名：RequestControl
 * 作用：请求重试控制
 * 作者：huangtianhao
 * 创建日期：2021/5/7
 */
class HostReplaceRequestControl(
    private val urlPattern: String?,
    defaultUri: Uri
) : RequestControl {

    companion object {
        private const val TAG = "HostReplaceRequestControl"
        private const val DEFAULT_ERROR_COUNT_LIMIT = 2
        private const val DEFAULT_INTERVAL = 2000L
        private const val NO_INTERVAL = 0L
    }

    private var currentIndex: Int = 0
    private var isUsingHttps: Boolean = false
    private var errorCountLimit = DEFAULT_ERROR_COUNT_LIMIT

    private var defaultCdnHost = defaultUri.host

    // 应该直接抛出错误，不进行重试的错误码
    private val fetalHttpResponseCode = listOf(416, 403)

    private var cdnList: List<String> = getCdnList()

    private val isMatchedUrlPattern = checkPatternMatched(urlPattern, defaultUri)

    /**
     * 检查传入的 defaultUri 是否匹配 urlPattern，匹配了才进行 CDN 替换
     */
    private fun checkPatternMatched(urlPattern: String?, defaultUri: Uri): Boolean {
        if (urlPattern.isNullOrBlank()) {
            return false
        }
        // cdnList 包括 scheme，避免将 https 替换为了 http
        return try {
            Pattern.matches(urlPattern, defaultUri.toString())
        } catch (e: Exception) {
            TekiLog.w(TAG, "checkPatternMatched error, please check the url pattern param: ${e.message}")
            false
        }
    }

    private var cdnListWithDefaultCdn: List<String> = if (isMatchedUrlPattern) {
        getCdnListWithDefaultCdn(cdnList)
    } else {
        // grt uri without path
        if (defaultUri.authority.isNullOrBlank()) {
            emptyList<String>()
        } else {
            try {
                Uri.Builder()
                    .scheme(defaultUri.scheme)
                    .encodedAuthority(defaultUri.encodedAuthority)
                    .build()
                    .let {
                        TekiLog.i(TAG, "get uri without path: ${it.toString()}")
                        listOf(it.toString())
                    }
            } catch (e: UnsupportedOperationException) {
                TekiLog.w(TAG, "get uri without path error: ${e.message}")
                emptyList<String>()
            }
        }
    }

    override fun getReplaceUri(uri: Uri): Uri {
        if (isMatchedUrlPattern) {
            checkCdnListVersion()
        } else if (cdnListWithDefaultCdn.isEmpty()) {
            val host = uri.host
            if (host != null) {
                cdnListWithDefaultCdn = listOf(host)
            } else {
                // 可能是本地文件
                return uri
            }
        }

        return replaceHost(cdnListWithDefaultCdn, uri, moveIndex = false)
    }

    override fun getRetryUri(
        uri: Uri,
        dataLevel: Int,
        currentError: IOException,
        errorCount: Int
    ): RequestControl.RetryUri {
        if (isMatchedUrlPattern) {
            checkCdnListVersion()
        } else if (cdnListWithDefaultCdn.isEmpty()) {
            val host = uri.host
            if (host != null) {
                cdnListWithDefaultCdn = listOf(host)
            } else {
                // 可能是本地文件错误，直接返回失败
                return FATAL_ERROR
            }
        }

        if (!isMatchedUrlPattern) {
            TekiLog.w(
                TAG,
                "skip replace [cdn host] ${uri.host} due to [isListEmpty] = ${cdnList.isEmpty()}, or [pattern is null] ${urlPattern == null} or not match the pattern"
            )
        }

        val errorCause = currentError.cause
        if (errorCause is OutOfRangeException) {
            return FATAL_ERROR
        }
        if (errorCause is InvalidResponseCodeException && fetalHttpResponseCode.contains(errorCause.responseCode)) {
            return FATAL_ERROR
        }
        if (errorCause is CipherException) {
            return FATAL_ERROR
        }

        if (errorCause is IllegalContentTypeException) {
            // 异常contentType直接切换至https重试流程
            isUsingHttps = true
        }

        val newUri: Uri = replaceHost(cdnListWithDefaultCdn, uri)
        errorCountLimit = cdnListWithDefaultCdn.size * 2
        TekiLog.i(TAG, "errorCountLimit $errorCountLimit")

        val needInterval =
            currentIndex != 0 && (currentIndex % cdnListWithDefaultCdn.size == 0) && !isUsingHttps
        val intervalMillis = if (needInterval) DEFAULT_INTERVAL else NO_INTERVAL

        when {
            dataLevel == RequestControl.DataLevel.PLAYING_LOW -> {
                // 低水位一直重试
                TekiLog.e(TAG, "load error when buffer size unsafe, load continue")
                return RequestControl.RetryUri(
                    newUri,
                    isFatalError = false,
                    resetErrorCount = true,
                    retryInterval = intervalMillis
                )
            }
            // [errorCount] 从 1 传入，所以这里要 > 而不是 >=
            errorCount > errorCountLimit -> {
                // 超过重试次数
                return if (dataLevel == RequestControl.DataLevel.PLAYING_HIGH) {
                    // 高水位阶段时，忽略
                    TekiLog.e(TAG, "load error when have safe size buffer, ignore")
                    ERROR
                } else {
                    // 非低水位、非高水位阶段时，抛出异常
                    FATAL_ERROR
                }
            }
            else -> {
                // 未超出
                return RequestControl.RetryUri(newUri, retryInterval = intervalMillis)
            }
        }

    }

    override fun resetIndex() {
        currentIndex = 0
        isUsingHttps = false
    }

    private fun checkCdnListVersion() {
        if (cdnList !== getCdnList()) {
            currentIndex = 0
            isUsingHttps = false
            cdnList = getCdnList()
            cdnListWithDefaultCdn = getCdnListWithDefaultCdn(cdnList)
        }
    }

    private fun getCdnList() = CdnListHolder.cdnList

    /**
     * 若CDN列表中没有原始地址，将原始地址的host添加到列表中
     */
    private fun getCdnListWithDefaultCdn(cdnList: List<String>): List<String> {
        val defaultCdnHost = defaultCdnHost ?: return cdnList

        return if (cdnList.find { it.contains(defaultCdnHost) } != null) {
            cdnList
        } else {
            ArrayList(cdnList).apply {
                add(defaultCdnHost)
            }
        }
    }

    private fun replaceHost(cdnList: List<String>, uri: Uri, moveIndex: Boolean = true): Uri {
        if (cdnList.isEmpty()) {
            return uri
        }
        var selectedCdnHost = cdnList[currentIndex % cdnList.size]
        TekiLog.i(TAG, "replacing host, [isUsingHttps] $isUsingHttps")
        if (isUsingHttps) {
            selectedCdnHost = selectedCdnHost.replaceFirst("http://", "https://")
        }

        var replacedUri = uri
        try {
            internalReplaceHost(uri.toString(), selectedCdnHost)?.let {
                replacedUri = Uri.parse(it)
            }
        } catch (t: Throwable) {
            TekiLog.e(TAG, "error on replacing host", t)
        }

        TekiLog.i(TAG, "original uri $uri, replaced uri $replacedUri")

        if (moveIndex) {
            currentIndex++
            if (currentIndex % cdnList.size == 0) {
                isUsingHttps = !isUsingHttps
            }
        }

        return replacedUri
    }

    private fun internalReplaceHost(url: String, cdnHost: String): String? {
        var cdnHost = cdnHost
        if (url.isEmpty()) {
            return null
        }
        if (cdnHost.isEmpty()) {
            return url
        }
        // 如果 cdn host 不带 scheme 的时候，根据[isUsingHttps]来判断是否使用https
        if (!cdnHost.startsWith("http")) {
            cdnHost = if (!isUsingHttps) {
                "http://$cdnHost"
            } else {
                "https://$cdnHost"
            }
        }
        if (!cdnHost.endsWith("/")) {
            cdnHost = "$cdnHost/"
        }
        val uri = URI.create(url)
        val cdn = uri.scheme + "://" + uri.host + (if (uri.port > 0) ":" + uri.port else "") + "/"
        return url.replaceFirst(cdn.toRegex(), cdnHost)
    }
}