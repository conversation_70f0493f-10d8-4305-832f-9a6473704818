package com.lizhi.component.tekiplayer.util

import android.net.Uri
import android.os.Parcel
import android.os.Parcelable
import android.util.SparseArray

class ParcelableSparseUriArray() : SparseArray<Uri>(), Parcelable {

    constructor(parcel: Parcel) : this() {
        val size = parcel.readInt()
        for (i in 0..size) {
            val key = parcel.readInt()
            val value = parcel.readParcelable<Uri>(Uri::class.java.classLoader) ?: continue
            put(key, value)
        }
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        val size = this.size()
        parcel.writeInt(size)
        for (i in 0..size) {
            parcel.writeInt(keyAt(i))
            parcel.writeParcelable(valueAt(i), flags)
        }
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<ParcelableSparseUriArray> {

        override fun createFromParcel(parcel: Parcel): ParcelableSparseUriArray {
            return ParcelableSparseUriArray(parcel)
        }

        override fun newArray(size: Int): Array<ParcelableSparseUriArray?> {
            return arrayOfNulls(size)
        }
    }
}