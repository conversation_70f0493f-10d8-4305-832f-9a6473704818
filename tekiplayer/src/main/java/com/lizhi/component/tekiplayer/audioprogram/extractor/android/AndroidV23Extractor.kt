//package com.lizhi.component.tekiplayer.audioprogram.extractor.android
//
//import android.media.MediaDataSource
//import android.media.MediaExtractor
//import android.media.MediaFormat
//import android.os.Build.VERSION_CODES
//import androidx.annotation.RequiresApi
//import androidx.collection.ArrayMap
//import com.lizhi.component.tekiplayer.audioprogram.extractor.Extractor
//import com.lizhi.component.tekiplayer.audioprogram.extractor.ExtractorInput
//import com.lizhi.component.tekiplayer.datasource.DataSource
//import com.lizhi.component.tekiplayer.engine.DataQueue
//import java.nio.ByteBuffer
//
///**
// * 文件名：V23Extractor
// * 作用：
// * 作者：<EMAIL>
// * 创建日期：2021/3/24
// */
//@RequiresApi(VERSION_CODES.M)
//class AndroidV23Extractor : Extractor {
//
//    companion object {
//        const val TAG = "AndroidV23Extractor"
//    }
//
//    private var dataSource: DataSource? = null
//    private var mediaExtractor = MediaExtractor()
//    override var mediaFormat: MediaFormat? = null
//    override val sampleTimeUs: Long
//        get() = mediaExtractor.sampleTime
//
//    private var init = false
//
//    override fun init(
//        filePath: DataQueue?,
//        dataSource: DataSource
//    ) {
//        this.dataSource = dataSource
//    }
//
//    override fun readSample(buffer: ByteBuffer): Int {
//        val len = mediaExtractor.readSampleData(buffer, 0)
//        mediaExtractor.advance()
//        return len
//    }
//
//    override fun timeToPosition(positionUs: Long): Long {
//        return 0
//    }
//
//    override fun sample(dataSource: ExtractorInput?): Int {
//        if (!init) {
//            mediaExtractor.setDataSource(V23MediaDataSource(dataSource))
//            mediaFormat = mediaExtractor.getTrackFormat(0)
//            mediaExtractor.selectTrack(0)
//            init = true
//        }
//        return 100
//    }
//
//    override val durationUs: Long?
//        get() = mediaFormat?.getLong(MediaFormat.KEY_DURATION)
//
//    override fun getTimeUs(position: Long): Long {
//        val mediaFormat = mediaFormat ?: return 0
//        val bitRate = mediaFormat.getLong(MediaFormat.KEY_BIT_RATE)
//        return (position * 8.0F / 1000 / bitRate).toLong()
//    }
//
//    override fun seek(positionUs: Long): Int {
//        mediaExtractor.seekTo(positionUs, MediaExtractor.SEEK_TO_PREVIOUS_SYNC)
//        return if (mediaExtractor.advance()) 0 else -1
//    }
//
//}
//
//@RequiresApi(VERSION_CODES.M)
//class V23MediaDataSource(private val dataSource: DataSource) : MediaDataSource() {
//
//    private var currPosition = 0L
//
//    private val dataSourceCache: ArrayMap<CacheKey, ByteArray> = ArrayMap(50)
//
//    class CacheKey(
//        val position: Long,
//        val size: Int
//    ) {
//        override fun equals(other: Any?): Boolean {
//            if (other is CacheKey) {
//                return position == other.position && size == other.size
//            }
//            return super.equals(other)
//        }
//
//        override fun hashCode(): Int {
//            return (position + size).toInt()
//        }
//    }
//
//    override fun readAt(
//        position: Long,
//        buffer: ByteArray?,
//        offset: Int,
//        size: Int
//    ): Int {
//        buffer ?: return -1
//
////        if (currPosition != position) {
////            dataSource.open(Range(position))
////            TekiLog.d(
////                AndroidV23Extractor.TAG,
////                "readAt position=$position, offset=$offset, size=$size"
////            )
////        }
//        val read = dataSource.read(buffer, offset, size)
//
//        currPosition = position + read
//        return read
//    }
//
//    override fun getSize(): Long {
//        return dataSource.contentLength ?: 0
//    }
//
//    override fun close() {
//        dataSource.close()
//    }
//}
