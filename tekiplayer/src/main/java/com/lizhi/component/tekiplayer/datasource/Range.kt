package com.lizhi.component.tekiplayer.datasource

import com.lizhi.component.tekiplayer.datasource.exception.IllegalRangeException

/**
 * 文件名：Range
 * 作用：范围定义
 * 作者：huangtianhao
 * 创建日期：2021/2/20
 */
data class Range(
    val start: Long,
    val end: Long? = null
)

fun Range.getRangeRequestStr(): String {
    return "bytes=${this.start}-${this.end ?: ""}"
}

fun Range.getNotNullEndOrThrow(): Long {
    return end ?: throw IllegalRangeException(this)
}