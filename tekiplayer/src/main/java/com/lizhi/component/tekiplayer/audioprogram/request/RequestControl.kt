package com.lizhi.component.tekiplayer.audioprogram.request

import android.net.Uri
import java.io.IOException

/**
 * 文件名：RequestControl
 * 作用：请求（重试）控制器的接口实现
 * 作者：huangtianhao
 * 创建日期：2021/5/7
 */
interface RequestControl {

    fun getReplaceUri(uri: Uri): Uri

    fun getRetryUri(uri: Uri, dataLevel: Int, currentError: IOException, errorCount: Int): RetryUri

    fun resetIndex()

    class DataLevel {
        companion object {
            const val PREPARING = 0
            const val EMPTY_DATA = 1
            const val PLAYING_LOW = 2
            const val PLAYING_HIGH = 3
            const val PRE_BUFFERING = 4
        }
    }


    data class RetryUri(
        val uri: Uri?,
        val isFatalError: Boolean = false,
        val resetErrorCount: Boolean = false,
        val retryInterval: Long = 0L
    ) {
        companion object {
            val FATAL_ERROR = RetryUri(null, true)

            val ERROR = RetryUri(null, false)

        }
    }
}

