package com.lizhi.component.tekiplayer.datasource

import android.net.Uri
import android.os.Bundle
import javax.crypto.Cipher

/**
 * 文件名：DataSource
 * 作用：数据源接口定义
 * 作者：<EMAIL>
 * 创建日期：2021/2/19
 */
interface DataSource : DataReader {

    interface Factory {
        fun create(uri: Uri, extraData: Bundle? = null): DataSource
    }

    var contentLength: Long?

    val responseHeaders: Map<String, List<String>>?

    var replacedCdnHost: String?

    fun open(position: Long): Boolean = open(Range(position))

    fun open(range: Range = Range(0)): Boolean

    fun close()

    fun getUrl(): String

    fun updateDataSourceCallback(dataSourceCallback: DataSourceCallback)

    /**
     * 设置加密信息
     */
    fun setAesInfo(aesKey: String?, aesIV: String?, isRealTimeDecrypt: Boolean = false, mode:Int = Cipher.DECRYPT_MODE)

    fun getAesInfo(): Pair<String?, String?>

    /**
     * 是否是加密缓存
     */
    fun hasAesInfo() : Boolean

    fun isRealTimeDecrypt(): Boolean

    /**
     * 设置缓存信息
     */
    fun setCacheInfo(encryptData: ByteArray, plainCacheLength:Long)

    /**
     * 设置加密回调
     */
    fun setEncryptCallback(callback: (encryptData: ByteArray) -> Unit)
}