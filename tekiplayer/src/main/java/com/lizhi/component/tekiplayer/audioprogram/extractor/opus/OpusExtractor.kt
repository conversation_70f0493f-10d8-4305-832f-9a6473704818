package com.lizhi.component.tekiplayer.audioprogram.extractor.opus

import android.media.MediaFormat
import com.lizhi.audiocore.AudioBufferProcess
import com.lizhi.component.tekiplayer.audioprogram.extractor.Extractor
import com.lizhi.component.tekiplayer.audioprogram.extractor.ExtractorInput
import com.lizhi.component.tekiplayer.audioprogram.extractor.ParsableByteArray
import com.lizhi.component.tekiplayer.audioprogram.extractor.PositionHolder
import com.lizhi.component.tekiplayer.audioprogram.extractor.SeekPoint
import com.lizhi.component.tekiplayer.audioprogram.extractor.Seeker
import com.lizhi.component.tekiplayer.engine.BufferHolder
import com.lizhi.component.tekiplayer.engine.DataQueue
import com.lizhi.component.tekiplayer.util.OpusUtil
import com.lizhi.component.tekiplayer.util.TekiLog
import java.io.EOFException

/**
 * 文件名：OpusExtractor
 * 作用：从 opus 流中提取音频数据。格式为荔枝定制的私有 opus 格式，没有封装到 ogg 中。
 * 作者：luo<PERSON><EMAIL>
 * 音频头格式：每帧Opus增加自定义的简单音频头（配合提供的播放器组件），音频头格式如下，总长度为64bit（32bit对齐）：
 * ｜___magic_number__|___primate___|___channel_number___|___sample_rate___|___encoded_length___|___resever___|__timestamp__|__rtp_length__|__na_length__|
 *      32b              2b              2b                  4b                  16b               8b             32b           8b           8b
 */
class OpusExtractor: Extractor {

    companion object {
        private const val MAGIC_NUMBER = 0x73d192b8 // "OPUS"
        private const val MAX_SNIFF_BYTES = 1024 * 10
        private const val OPUS_HEADER_SIZE = 12
        private const val TAG = "OpusExtractor"
    }

    override var mediaFormat: MediaFormat? = null
    override val durationUs: Long
        get() = if (mediaFormat == null) -1 else 100000
    override val seeker: Seeker?
        get() = null

    private var dataQueue: DataQueue? = null
//    private val scratch = ParsableByteArray(OPUS_HEADER_SIZE)
    private val scratch = ParsableByteArray(MAX_SNIFF_BYTES)
    private var remainingBytes = 0

    var aesKey: String? = null
    var aesIv: String? = null

    override fun init(dataQueue: DataQueue?) {
       this.dataQueue = dataQueue
    }

    override fun getDataQueue(): DataQueue? {
        return dataQueue
    }

    override fun sniff(extractorInput: ExtractorInput): Boolean {
        var searchBytes = 0
        var peekSize = 0
        aesKey = extractorInput.aesKey
        aesIv = extractorInput.aesIv
        while (true) {
            searchBytes += 512
            scratch.position = 0
            // peek data from extractorInput
            val peek = extractorInput.peek(scratch.data, peekSize, searchBytes)
            val isEnd = peek == -1
            if (!isEnd) {
                peekSize += peek
            }
            val opusFormat = AudioBufferProcess.probe(scratch.data, peekSize, isEnd)
            if (opusFormat == null || !opusFormat.result) {
                if (isEnd) {
                    dataQueue?.setEndFlag()
                    return false
                } else if (searchBytes >= MAX_SNIFF_BYTES) {
                    return false
                }
                continue
            } else if (opusFormat.result) {
                if (isEnd) {
                    dataQueue?.setEndFlag()
                }
                // check opus format
                if (opusFormat.sampleRate <= 0 || opusFormat.channel <= 0 || opusFormat.frameSize <= 0 || opusFormat.frameSize > 160) {
                    return false
                }
                mediaFormat = MediaFormat.createAudioFormat(
                    "audio/opus",
                    opusFormat.sampleRate,
                    opusFormat.channel
                )
                mediaFormat?.setInteger(MediaFormat.KEY_DURATION, opusFormat.frameSize)
                extractorInput.resetPeekPosition()
                return true
            } else {
                return false
            }

        }
//        return sniffByJava(extractorInput)
    }

    private fun sniffByJava(extractorInput: ExtractorInput): Boolean {
        // sniff opus header from extractorInput
        // return true if opus header is found
        var searchBytes = 0
        while (true) {
            scratch.position = 0
            try {
                extractorInput.peekFully(scratch.data, 0, OPUS_HEADER_SIZE)
            } catch (e: EOFException) {
                dataQueue?.setEndFlag()
                return false
            }
            // need read 2 header frame to check if it is opus header
            // read magic number on first packet
            val magicNumber = scratch.readUnsignedInt()
            if (magicNumber == MAGIC_NUMBER.toLong()) {
                // read length from opus header and skip first packet
                val head = ByteArray(10)
                scratch.readBytes(head, 0, 10)
                val length = (head[1].toUInt() shl 8) + (head[2].toUInt())
                val rtpLength = head[8].toUInt() shr 2
                val naLength = if (rtpLength > 0u) {
                    ((head[8].toUInt() and 3u) shl 8) + (head[9].toUInt())
                } else {
                    0u
                }

                // return true if just one packet
                val canFullyPeek =
                    extractorInput.advancePeekPosition(length.toInt() + naLength.toInt(), true)
                if (!canFullyPeek) {
                    return false
                }
                scratch.position = 0
                try {
                    extractorInput.peekFully(scratch.data, 0, OPUS_HEADER_SIZE)
                } catch (e: EOFException) {
                    dataQueue?.setEndFlag()
                    if ((length.toInt() + naLength.toInt() + OPUS_HEADER_SIZE + searchBytes).toLong() <= extractorInput.length) {
                        // just one packet
                        return setOutputFormat(head)
                    }
                    return false
                }
                // read magic number on second frame
                val secFrameMagicNum = scratch.readUnsignedInt()
                if (secFrameMagicNum != MAGIC_NUMBER.toLong()) {
                    // not opus header, skip 1 byte
                    searchBytes++
                    if (searchBytes > MAX_SNIFF_BYTES) {
                        return false
                    }
                    extractorInput.resetPeekPosition()
                    try {
                        extractorInput.advancePeekPosition(searchBytes, false)
                    } catch (e: EOFException) {
                        dataQueue?.setEndFlag()
                        return false
                    }
                    continue
                }
    //                try {
    //                    extractorInput.advancePeekPosition(1024 * 100, true)
    //                } catch (_: IndexOutOfBoundsException) {
    //                }
                // read sample rate and channel and duration
                scratch.readBytes(head, 0, 8)
                val outputFormat = setOutputFormat(head)
                extractorInput.resetPeekPosition()
                if (!outputFormat) {
                    return false
                }
                return true
            } else {
                // not opus header, skip 1 byte
                searchBytes++
                if (searchBytes > MAX_SNIFF_BYTES) {
                    return false
                }
                extractorInput.resetPeekPosition()
                try {
                    extractorInput.advancePeekPosition(searchBytes, false)
                } catch (e: EOFException) {
                    dataQueue?.setEndFlag()
                    return false
                }
            }
        }
    }

    private fun setOutputFormat(head: ByteArray, onlyOnePacket: Boolean = false) : Boolean {
        // read sample rate and channel and duration
        val sampleRate = OpusUtil.getSampleRate(head[0].toInt() and 0xf)
        val channel = (head[0].toInt() shr 4) and 0x3
        // NOTE: duration present packet timestamp, not a frame duration, if want to support seek, need to calculate frame duration on the other way!!!
        var duration =
            ((head[4].toUInt() shl 24) + (head[5].toUInt() shl 16) + (head[6].toUInt() shl 8) + (head[7].toUInt())).toInt()
        if (!onlyOnePacket) {
            duration /= 2
        }
        if (duration <= 0) {
            TekiLog.e(TAG, "duration is 0")
            return false
        }
        // set output format
        val format = MediaFormat()
        format.setString(MediaFormat.KEY_MIME, "audio/opus")
        format.setInteger(MediaFormat.KEY_CHANNEL_COUNT, channel)
        format.setInteger(MediaFormat.KEY_SAMPLE_RATE, sampleRate)
        format.setInteger(MediaFormat.KEY_DURATION, duration)
        mediaFormat = format
        return true
    }

    override fun sample(input: ExtractorInput, seekPosition: PositionHolder): Int {
        // read opus data from extractorInput

        val queue = dataQueue ?: return Extractor.RESULT_UNINITED

        if (remainingBytes <= 0) {
            remainingBytes = 512
            queue.sampleMetadata(0, 512, input.position)
        }
//        else {
//            TekiLog.w("opus", "sample remainingBytes:$remainingBytes")
//        }

        val result = try {
            queue.readToQueue(input, remainingBytes)
        } catch (e: IndexOutOfBoundsException) {
            TekiLog.e("opus", "readToQueue error: ${e.message}")
            -1
        }

        return if (result >= 0) {
            remainingBytes -= result
            Extractor.RESULT_CONTINUE
        } else {
            TekiLog.w("opus", "readToQueue setEndFlag $result")
            queue.setEndFlag()
            Extractor.RESULT_END_OF_INPUT
        }
    }

    override fun seek(timeUs: Long, position: Long) {
        reset()
        // not support on version 0.2.0
    }

    override fun readSample(buffer: BufferHolder): Int {
        return dataQueue?.blockDequeue(buffer) ?: -1
    }

    override fun reset() {
        remainingBytes = 0
    }

    override fun timeToPosition(timeUs: Long): SeekPoint {
        return SeekPoint.START
    }

    override fun release() {

    }
}