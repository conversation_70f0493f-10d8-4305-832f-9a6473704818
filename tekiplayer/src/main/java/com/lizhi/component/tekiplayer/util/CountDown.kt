package com.lizhi.component.tekiplayer.util

import android.os.Handler
import android.util.ArrayMap
import java.util.concurrent.CopyOnWriteArrayList

private val countDownMap by lazy {
    ArrayMap<Handler, CountDownRunnable>()
}

internal class CountDownRunnable(val handler: <PERSON><PERSON>, val callback: () -> Long) : Runnable {

    var stop = false

    private val callbackList by lazy {
        CopyOnWriteArrayList<() -> Long>().apply { add(callback) }
    }

    fun addCallback(callback: () -> Long) {
        callbackList.add(callback)
    }

    fun removeCallbacks() {
        callbackList.clear()
    }

    override fun run() {
        if (stop) {
            return
        }
        var interval = 1000L
        kotlin.runCatching {
            callbackList.forEach {
                interval = it()
            }
        }
        handler.postDelayed(this, interval)
    }

}

internal fun Handler.countDown(callback: () -> Long) {
    var countDownRunnable = countDownMap[this]
    if (countDownRunnable != null) {
        countDownRunnable.addCallback(callback)
    } else {
        countDownRunnable = CountDownRunnable(this, callback)
        post(countDownRunnable)
        countDownMap[this] = countDownRunnable
    }
}

internal fun Handler.stopCountDown() {
    val countDownRunnable = countDownMap[this] ?: return
    countDownRunnable.stop = true
    countDownRunnable.removeCallbacks()
    removeCallbacks(countDownRunnable)
    countDownMap[this] = null
}
