package com.lizhi.component.tekiplayer.engine.decode.codec

import android.media.MediaCodec
import android.media.MediaCodecList
import android.media.MediaFormat
import com.lizhi.component.tekiplayer.engine.decode.Decoder
import com.lizhi.component.tekiplayer.engine.decode.DecoderCallback
import com.lizhi.component.tekiplayer.engine.exception.RecoverableEngineException
import com.lizhi.component.tekiplayer.engine.exception.UnSupportFormatException
import com.lizhi.component.tekiplayer.util.TekiLog


/**
 * Desc: MediaCodec Decoder
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/3/7.
*/
class MediaCodecDecoder(private val format: MediaFormat) :
    Decoder<MediaCodecInputBufferHolder, MediaCodecOutputBufferHolder> {

    companion object {
        private const val TAG = "MediaCodecDecoder"
    }

    override var name: String? = null
    override var decoderCallback: DecoderCallback? = null
    private var zeroData = true
    private var detectZeroData = true
    private var codec: MediaCodec? = null
    private var outputBufferHolder: MediaCodecOutputBufferHolder = MediaCodecOutputBufferHolder()
    private var inputBufferHolder: MediaCodecInputBufferHolder = MediaCodecInputBufferHolder()
    private val ZERO_BYTE: Byte = 0

    private var receiveBuffer = false
    private var firstFrame = true
    private var upFrameTimePositionUs: Long = 0

    init {
        val mediaCodecList = MediaCodecList(MediaCodecList.REGULAR_CODECS)
        if (name.isNullOrEmpty()) {
            name = mediaCodecList.findDecoderForFormat(format)
        }
    }

    override fun start() {
        val decoderName = name?: throw UnSupportFormatException("can not find decoder for format")
        try {
            firstFrame = true
            codec = MediaCodec.createByCodecName(decoderName)
            codec?.configure(format, null, null, 0)
            codec?.start()
            TekiLog.i(TAG, "decode name ${codec?.name}")
        } catch (e: Exception) {
            onError(e)
        }
    }

    override fun dequeueInputBuffer(): MediaCodecInputBufferHolder? {
        try {
            val index = codec?.dequeueInputBuffer(0) ?: return null
            if (index < 0) {
                // TekiLog.d(TAG, "dequeueInputBuffer index < 0")
                return null
            }
            // TekiLog.d(TAG, "dequeueInputBuffer index = $index")
            val buffer = codec?.getInputBuffer(index)
            if (buffer != null) {
                receiveBuffer = true
                return inputBufferHolder.apply {
                    this.index = index
                    this.setData(buffer)
                }
            }
            return null
        } catch (e: Exception) {
            onError(e)
            return null
        }

    }

    private fun onError(e : Exception) {
        TekiLog.e(TAG, "onError", e)
        if (e is MediaCodec.CodecException && (e.isRecoverable || e.isTransient)) {
            if (e.isRecoverable) {
                codec?.stop()
                codec?.configure(format, null, null, 0)
                codec?.start()
            }
        } else if (e is IllegalStateException && !receiveBuffer) {
            codec?.release()
            start()
        } else {
            decoderCallback?.onDecodeError(e)
        }
    }

    override fun queueInputBuffer(buffer: MediaCodecInputBufferHolder) {
        try {
            if (buffer.isEndOfStream) {
                codec?.queueInputBuffer(buffer.index, 0, 0, 0, MediaCodec.BUFFER_FLAG_END_OF_STREAM)
                return
            }
            if (buffer.timePositionUs < 0) {
                TekiLog.e(TAG,"The timestamp of the incoming decoder should not be negative")
                decoderCallback?.onDecodeError(RecoverableEngineException(message = "time position negative"))
                buffer.timePositionUs = 0
            }
            if (firstFrame) {
                upFrameTimePositionUs = buffer.timePositionUs
                firstFrame = false
            } else {
                if (buffer.timePositionUs < upFrameTimePositionUs) {
                    TekiLog.w(TAG,"The incoming time is not incremental")
                    decoderCallback?.onDecodeError(RecoverableEngineException(message = "time not incremental, " +
                            "upFrameTimePosition: ${upFrameTimePositionUs}, bufferTimePosition: ${buffer.timePositionUs}"))
                }
                upFrameTimePositionUs = buffer.timePositionUs
            }
            codec?.queueInputBuffer(buffer.index, 0, buffer.size, buffer.timePositionUs, 0)
        } catch (e: Exception) {
            onError(e)
        }
    }

    override fun dequeueOutputBuffer(): MediaCodecOutputBufferHolder? {
        try {
            val info = MediaCodec.BufferInfo()
            val index = codec?.dequeueOutputBuffer(outputBufferHolder.info, 0)
            // TekiLog.w(TAG, "handleOutBufferFromCodec buffer index=$index")
            if (index != null && index >= 0) {
                val isEndOfStream = outputBufferHolder.info.flags and MediaCodec.BUFFER_FLAG_END_OF_STREAM != 0
                if (isEndOfStream) {
                    outputBufferHolder.isEndOfStream = true
                    outputBufferHolder.clear()
                    return outputBufferHolder.apply {
                        this.index = index
                    }
                }
                // 获取到 buffer
                val buffer = codec?.getOutputBuffer(index) ?: return null
                // The dequeued buffer is a media buffer. Do some initial setup.
                // It will be processed by calling processOutputBuffer (possibly multiple times).
                buffer.position(outputBufferHolder.info.offset)
                buffer.limit(outputBufferHolder.info.offset + outputBufferHolder.info.size)
                if (detectZeroData) {
                    try {
                        val startPosition = outputBufferHolder.info.offset
                        val endPosition = outputBufferHolder.info.offset + outputBufferHolder.info.size
                        for (byteIndex in startPosition until endPosition) {
                            val byte = buffer.get(byteIndex)
                            if (byte != ZERO_BYTE) {
                                detectZeroData = false
                                zeroData = false
                                outputBufferHolder
                            }
                        }
                    } catch (e: Exception) {
                        TekiLog.e(TAG, "onError", e)
                    }
                }

                return outputBufferHolder.apply {
                    this.index = index
                    setData(buffer)
                    zeroDataFlag = zeroData
                    this.info = info
                }
            } else if (index == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) {
                // Subsequent data will conform to new format.
                decoderCallback?.onFormatOutput(codec!!.outputFormat)
                return outputBufferHolder.apply {
                    this.index = index
                    clear()
                }
            } else {
//                TekiLog.w(TAG, "handleOutBufferFromCodec 获取不到更多 buffer index=$index")
            }
        } catch (e: Exception) {
            onError(e)
        }

        return null
    }

    override fun releaseOutputBuffer(outputBuffer: MediaCodecOutputBufferHolder) {
        try {
            codec?.releaseOutputBuffer(outputBuffer.index, false)
        } catch (e: Exception) {
            onError(e)
        }
    }

    override fun flush() {
        try {
            codec?.flush()
        } catch (e: Exception) {
            TekiLog.e(TAG, "flush", e)
        }
    }

    override fun release() {
        try {
            codec?.stop()
            codec?.release()
        } catch (e: Exception) {
            TekiLog.e(TAG, "release codec error", e)
        }
    }
}
