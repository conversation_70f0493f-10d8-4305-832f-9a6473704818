package com.lizhi.component.tekiplayer.controller

import androidx.annotation.IntDef
import kotlin.annotation.AnnotationTarget.*

/**
 * 文件名：PlayerStatus
 * 作用：
 * 作者：l<PERSON><PERSON><PERSON><PERSON>@lizhi.fm
 * 创建日期：2021/2/19
 */
class PlayerState {

    companion object {
        /**
         * 空闲状态
         */
        const val STATE_IDLE = 0

        /**
         * 缓冲中
         */
        const val STATE_BUFFERING = 1

        /**
         * 准备就绪
         */
        const val STATE_READY = 2

        /**
         * 播放中
         */
        const val STATE_PLAYING = 3

        /**
         * 播放暂停
         */
        const val STATE_PAUSED = 4

        /**
         * 播放结束
         */
        const val STATE_ENDED = 5

        fun getStateName(@State state: Int): String {
            return when (state) {
                STATE_IDLE -> "STATE_IDLE"
                STATE_BUFFERING -> "STATE_BUFFERING"
                STATE_READY -> "STATE_READY"
                STATE_PLAYING -> "STATE_PLAYING"
                STATE_PAUSED -> "STATE_PAUSED"
                STATE_ENDED -> "STATE_ENDED"
                else -> ""
            }
        }
    }

    /**
     * 播放器状态. One of [STATE_IDLE], [STATE_BUFFERING], [STATE_READY], [STATE_PLAYING], [STATE_PAUSED], [STATE_ENDED].
     */
    @MustBeDocumented
    @kotlin.annotation.Retention(AnnotationRetention.SOURCE)
    @Target(VALUE_PARAMETER, FIELD, PROPERTY, FUNCTION)
    @IntDef(value = [STATE_IDLE, STATE_BUFFERING, STATE_READY, STATE_PLAYING, STATE_PAUSED, STATE_ENDED])
    annotation class State

}