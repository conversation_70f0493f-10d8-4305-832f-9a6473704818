package com.lizhi.component.tekiplayer

const val ERR_ENGINE = 1
const val ERR_ENGINE_UNSUPPORT = 2
const val ERR_DATASOURCE_FILE = 3
const val ERR_DATASOURCE_CACHE_FILE_NOT_INIT = 4
const val ERR_DATASOURCE_ILLEGAL_RANGE = 5
const val ERR_DATASOURCE_NOT_OPEN = 6
const val ERR_DATASOURCE_HTTP_CONNECTION = 7
const val ERR_DATASOURCE_ILLEGAL_CONTENT_TYPE = 8
const val ERR_DATASOURCE_HTTP_OUT_OF_RANGE = 9
const val ERR_DATASOURCE_HTTP_TIMEOUT = 10
const val ERR_DATASOURCE_HTTP_IO = 11
const val ERR_PROGRAM = 30
const val ERR_PROGRAM_NOT_FOUND = 31
const val ERR_CIPHE = 50
const val ERR_CIPHER_ILLEGALBLOCKSIZE = 51
const val ERR_CIPHER_BADPADDING = 52
const val ERR_CIPHER_SHORTBUFFER = 53
const val ERR_CIPHER_ILLEGALSTATE = 54

abstract class TekiException(
    val code: Int, override val message: String,
    override val cause: Throwable?
) : RuntimeException(message, cause)