package com.lizhi.component.tekiplayer.audioprogram.listener

import com.lizhi.component.tekiplayer.audioprogram.AudioProgram
import com.lizhi.component.tekiplayer.engine.DataQueue.DataLevel

/**
 * 文件名：AudioProgramListener
 * 作用：回调，未必全部接口都会用上
 * 作者：liaodongming
 * 创建日期：2021/2/22
 */
interface AudioProgramListener {

    companion object {
        const val REASON_BUFFERING_PREPARE = 0
        const val REASON_BUFFERING_NOT_ENOUGH = 1
    }

    fun onPlaybackStarted(program: AudioProgram)

    fun onPlaybackStopped(program: AudioProgram)

    fun onPlaybackBuffering(
        program: AudioProgram,
        reason: Int
    )

    fun onPlaybackResumed(audioProgram: AudioProgram)

    fun onPlaybackPaused(program: AudioProgram)

    fun onPlaybackSeeking(
        program: AudioProgram,
        currBufferedTimeUs: Long,
        currBufferedPosition: Long,
        destSeekTimeUs: Long,
        destSeekPosition: Long
    )

    fun onPlaybackSeekSuccess(
        program: AudioProgram,
        destSeekTimeUs: Long,
        destSeekPosition: Long
    )

    fun onNeedMoreData(program: AudioProgram, seeking: Boolean)

    fun onBufferEnough(program: AudioProgram)

    fun onPlaybackCompletion(program: AudioProgram)

    fun onPlaybackFailed(
        program: AudioProgram,
        error: Throwable,
        recoverable: Boolean
    )

    fun onBufferedComplete(program: AudioProgram)

    fun onBufferSizeStateChange(
        program: AudioProgram,
        @DataLevel dataLevel: Int,
        cacheSize: Long,
        firstFullCacheCallback: Boolean
    )

    fun onRetryingStarted(program: AudioProgram)

    fun onRetryingSuccess(program: AudioProgram)

    fun onRetryingFailed(program: AudioProgram)

    fun onStreamErrorOccurred(
        errorCode: Int,
        errorDesc: String
    )

    fun onStreamMetaDataAvailable(metaData: String)

    fun onStreamSamplesAvailable(program: AudioProgram)

    fun onPlaybackFirstFrameRender(audioProgram: AudioProgram)

    fun onBandwidthQualityChanged(program: AudioProgram, qualityLevel: Int, speed: Int)

    fun onPlayZero(program: AudioProgram)

}