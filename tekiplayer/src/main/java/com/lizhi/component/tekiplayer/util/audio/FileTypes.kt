/*
 * Copyright 2020 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.lizhi.component.tekiplayer.util.audio

import android.net.Uri
import androidx.annotation.IntDef
import androidx.annotation.VisibleForTesting

/** Defines common file type constants and helper methods.  */
object FileTypes {
    /** Unknown file type.  */
    const val UNKNOWN = -1

    /** File type for the AC-3 and E-AC-3 formats.  */
    const val AC3 = 0

    /** File type for the AC-4 format.  */
    const val AC4 = 1

    /** File type for the ADTS format.  */
    const val ADTS = 2

    /** File type for the AMR format.  */
    const val AMR = 3

    /** File type for the FLAC format.  */
    const val FLAC = 4

    /** File type for the FLV format.  */
    const val FLV = 5

    /** File type for the Matroska and WebM formats.  */
    const val MATROSKA = 6

    /** File type for the MP3 format.  */
    const val MP3 = 7

    /** File type for the MP4 format.  */
    const val MP4 = 8

    /** File type for the Ogg format.  */
    const val OGG = 9

    /** File type for the MPEG-PS format.  */
    const val PS = 10

    /** File type for the MPEG-TS format.  */
    const val TS = 11

    /** File type for the WAV format.  */
    const val WAV = 12

    /** File type for the WebVTT format.  */
    const val WEBVTT = 13

    /** File type for the JPEG format.  */
    const val JPEG = 14

    /** File type for the OPUS format.  */
    const val OPUS = 15

    @VisibleForTesting /* package */
    val HEADER_CONTENT_TYPE = "Content-Type"
    private const val EXTENSION_AC3 = ".ac3"
    private const val EXTENSION_EC3 = ".ec3"
    private const val EXTENSION_AC4 = ".ac4"
    private const val EXTENSION_ADTS = ".adts"
    private const val EXTENSION_AAC = ".aac"
    private const val EXTENSION_AMR = ".amr"
    private const val EXTENSION_FLAC = ".flac"
    private const val EXTENSION_FLV = ".flv"
    private const val EXTENSION_PREFIX_MK = ".mk"
    private const val EXTENSION_WEBM = ".webm"
    private const val EXTENSION_PREFIX_OG = ".og"
    private const val EXTENSION_OPUS = ".opus"
    private const val EXTENSION_MP3 = ".mp3"
    private const val EXTENSION_MP4 = ".mp4"
    private const val EXTENSION_PREFIX_M4 = ".m4"
    private const val EXTENSION_PREFIX_MP4 = ".mp4"
    private const val EXTENSION_PREFIX_CMF = ".cmf"
    private const val EXTENSION_PS = ".ps"
    private const val EXTENSION_MPEG = ".mpeg"
    private const val EXTENSION_MPG = ".mpg"
    private const val EXTENSION_M2P = ".m2p"
    private const val EXTENSION_TS = ".ts"
    private const val EXTENSION_PREFIX_TS = ".ts"
    private const val EXTENSION_WAV = ".wav"
    private const val EXTENSION_WAVE = ".wave"
    private const val EXTENSION_VTT = ".vtt"
    private const val EXTENSION_WEBVTT = ".webvtt"
    private const val EXTENSION_JPG = ".jpg"
    private const val EXTENSION_JPEG = ".jpeg"

    /** Returns the [Type] corresponding to the response headers provided.  */
    @Type
    fun inferFileTypeFromResponseHeaders(responseHeaders: Map<String, List<String?>?>?): Int {
        responseHeaders ?: return UNKNOWN
        val contentTypes = responseHeaders[HEADER_CONTENT_TYPE]
        val mimeType = if (contentTypes == null || contentTypes.isEmpty()) null else contentTypes[0]
        return inferFileTypeFromMimeType(mimeType)
    }

    /**
     * Returns the [Type] corresponding to the MIME type provided.
     *
     *
     * Returns [.UNKNOWN] if the mime type is `null`.
     */
    @Type
    fun inferFileTypeFromMimeType(mimeType: String?): Int {
        mimeType ?: return UNKNOWN
        return when (normalizeMimeType(mimeType)) {
            MimeTypes.AUDIO_AC3, MimeTypes.AUDIO_E_AC3, MimeTypes.AUDIO_E_AC3_JOC -> AC3
            MimeTypes.AUDIO_AC4 -> AC4
            MimeTypes.AUDIO_AMR, MimeTypes.AUDIO_AMR_NB, MimeTypes.AUDIO_AMR_WB -> AMR
            MimeTypes.AUDIO_FLAC -> FLAC
            MimeTypes.VIDEO_FLV -> FLV
            MimeTypes.VIDEO_MATROSKA, MimeTypes.AUDIO_MATROSKA, MimeTypes.VIDEO_WEBM, MimeTypes.AUDIO_WEBM, MimeTypes.APPLICATION_WEBM -> MATROSKA
            MimeTypes.AUDIO_MPEG -> MP3
            MimeTypes.VIDEO_MP4, MimeTypes.AUDIO_MP4, MimeTypes.APPLICATION_MP4 -> MP4
            MimeTypes.AUDIO_OGG -> OGG
            MimeTypes.AUDIO_OPUS -> OPUS
            MimeTypes.VIDEO_PS -> PS
            MimeTypes.VIDEO_MP2T -> TS
            MimeTypes.AUDIO_WAV -> WAV
            MimeTypes.TEXT_VTT -> WEBVTT
            MimeTypes.IMAGE_JPEG -> JPEG
            else -> UNKNOWN
        }
    }

    /**
     * Normalizes the MIME type provided so that equivalent MIME types are uniquely represented.
     *
     * @param mimeType A MIME type to normalize.
     * @return The normalized MIME type, or the argument MIME type if its normalized form is unknown.
     */
    fun normalizeMimeType(mimeType: String): String {
        return when (mimeType) {
            MimeTypes.BASE_TYPE_AUDIO + "/x-flac" -> MimeTypes.AUDIO_FLAC
            MimeTypes.BASE_TYPE_AUDIO + "/mp3" -> MimeTypes.AUDIO_MPEG
            MimeTypes.BASE_TYPE_AUDIO + "/x-wav" -> MimeTypes.AUDIO_WAV
            else -> mimeType
        }
    }

    /** Returns the [Type] corresponding to the [Uri] provided.  */
    @Type
    fun inferFileTypeFromUri(uri: Uri): Int {
        val filename = uri.lastPathSegment
        return when {
            filename == null -> {
                UNKNOWN
            }
            filename.endsWith(EXTENSION_AC3) || filename.endsWith(EXTENSION_EC3) -> {
                AC3
            }
            filename.endsWith(EXTENSION_AC4) -> {
                AC4
            }
            filename.endsWith(EXTENSION_ADTS) || filename.endsWith(EXTENSION_AAC) -> {
                ADTS
            }
            filename.endsWith(EXTENSION_AMR) -> {
                AMR
            }
            filename.endsWith(EXTENSION_FLAC) -> {
                FLAC
            }
            filename.endsWith(EXTENSION_FLV) -> {
                FLV
            }
            filename.startsWith(
                EXTENSION_PREFIX_MK,  /* toffset= */
                filename.length - (EXTENSION_PREFIX_MK.length + 1)
            ) || filename.endsWith(EXTENSION_WEBM) -> {
                MATROSKA
            }
            filename.endsWith(EXTENSION_MP3) -> {
                MP3
            }
            filename.endsWith(EXTENSION_MP4) || filename.startsWith(
                EXTENSION_PREFIX_M4,  /* toffset= */
                filename.length - (EXTENSION_PREFIX_M4.length + 1)
            ) || filename.startsWith(
                EXTENSION_PREFIX_MP4,  /* toffset= */
                filename.length - (EXTENSION_PREFIX_MP4.length + 1)
            ) || filename.startsWith(
                EXTENSION_PREFIX_CMF,  /* toffset= */
                filename.length - (EXTENSION_PREFIX_CMF.length + 1)
            ) -> {
                MP4
            }
            filename.startsWith(
                EXTENSION_PREFIX_OG,  /* toffset= */
                filename.length - (EXTENSION_PREFIX_OG.length + 1)
            ) || filename.endsWith(EXTENSION_OPUS) -> {
                OGG
            }
            filename.endsWith(EXTENSION_PS) || filename.endsWith(EXTENSION_MPEG) || filename.endsWith(EXTENSION_MPG) || filename.endsWith(EXTENSION_M2P) -> {
                PS
            }
            filename.endsWith(EXTENSION_TS) || filename.startsWith(
                EXTENSION_PREFIX_TS,  /* toffset= */
                filename.length - (EXTENSION_PREFIX_TS.length + 1)
            ) -> {
                TS
            }
            filename.endsWith(EXTENSION_WAV) || filename.endsWith(EXTENSION_WAVE) -> {
                WAV
            }
            filename.endsWith(EXTENSION_VTT) || filename.endsWith(EXTENSION_WEBVTT) -> {
                WEBVTT
            }
            filename.endsWith(EXTENSION_JPG) || filename.endsWith(EXTENSION_JPEG) -> {
                JPEG
            }
            else -> {
                UNKNOWN
            }
        }
    }

    /**
     * File types. One of [.UNKNOWN], [.AC3], [.AC4], [.ADTS], [.AMR],
     * [.FLAC], [.FLV], [.MATROSKA], [.MP3], [.MP4], [.OGG],
     * [.PS], [.TS], [.WAV], [.WEBVTT] and [.JPEG].
     */
    @MustBeDocumented
    @kotlin.annotation.Retention(AnnotationRetention.SOURCE)
    @IntDef(
        UNKNOWN, AC3, AC4, ADTS, AMR, FLAC, FLV,
        MATROSKA, MP3, MP4, OGG, PS, TS, WAV,
        WEBVTT, JPEG
    )
    annotation class Type
}