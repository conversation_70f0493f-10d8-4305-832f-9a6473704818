/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.lizhi.component.tekiplayer.util;

import android.media.AudioFormat;
import android.media.MediaCodec;
import android.os.Build;

import androidx.annotation.Nullable;

import com.lizhi.component.tekiplayer.audioprogram.extractor.mpeg4.Format;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import kotlin.text.Charsets;

import static java.lang.Math.max;
import static java.lang.Math.min;

/**
 * Miscellaneous utility methods.
 */
public final class Util {
    /**
     * Represents an unset or unknown index.
     */
    public static final int INDEX_UNSET = -1;
    /**
     * Indicates that a buffer holds a synchronization sample.
     */
    public static final int BUFFER_FLAG_KEY_FRAME = MediaCodec.BUFFER_FLAG_KEY_FRAME;
    /**
     * Flag for empty buffers that signal that the end of the stream was reached.
     */
    public static final int BUFFER_FLAG_END_OF_STREAM = MediaCodec.BUFFER_FLAG_END_OF_STREAM;
    /**
     * Indicates that a buffer has supplemental data.
     */
    public static final int BUFFER_FLAG_HAS_SUPPLEMENTAL_DATA = 1 << 28; // 0x10000000
    /**
     * Indicates that a buffer is known to contain the last media sample of the stream.
     */
    public static final int BUFFER_FLAG_LAST_SAMPLE = 1 << 29; // 0x20000000
    /**
     * Indicates that a buffer is (at least partially) encrypted.
     */
    public static final int BUFFER_FLAG_ENCRYPTED = 1 << 30; // 0x40000000
    /**
     * Indicates that a buffer should be decoded but not rendered.
     */
    public static final int BUFFER_FLAG_DECODE_ONLY = 1 << 31; // 0x80000000

    /**
     * The number of milliseconds in one second.
     */
    public static final long MILLIS_PER_SECOND = 1000L;

    /**
     * The number of microseconds in one second.
     */
    public static final long MICROS_PER_SECOND = 1000000L;

    /**
     * The number of nanoseconds in one second.
     */
    public static final long NANOS_PER_SECOND = 1000000000L;

    /**
     * The number of bits per byte.
     */
    public static final int BITS_PER_BYTE = 8;

    /**
     * The number of bytes per float.
     */
    public static final int BYTES_PER_FLOAT = 4;

    /**
     * Like {@link Build.VERSION#SDK_INT}, but in a place where it can be conveniently
     * overridden for local testing.
     */
    public static final int SDK_INT =
            "S".equals(Build.VERSION.CODENAME)
                    ? 31
                    : "R".equals(Build.VERSION.CODENAME) ? 30 : Build.VERSION.SDK_INT;

    /**
     * Like {@link Build#DEVICE}, but in a place where it can be conveniently overridden for local
     * testing.
     */
    public static final String DEVICE = Build.DEVICE;

    /**
     * Like {@link Build#MANUFACTURER}, but in a place where it can be conveniently overridden for
     * local testing.
     */
    public static final String MANUFACTURER = Build.MANUFACTURER;

    /**
     * Like {@link Build#MODEL}, but in a place where it can be conveniently overridden for local
     * testing.
     */
    public static final String MODEL = Build.MODEL;

    public static final long LENGTH_UNSET = -1;

    /** A type constant for tracks of unknown type. */
    public static final int TRACK_TYPE_UNKNOWN = -1;
    /** A type constant for tracks of some default type, where the type itself is unknown. */
    public static final int TRACK_TYPE_DEFAULT = 0;
    /** A type constant for audio tracks. */
    public static final int TRACK_TYPE_AUDIO = 1;
    /** A type constant for video tracks. */
    public static final int TRACK_TYPE_VIDEO = 2;
    /** A type constant for text tracks. */
    public static final int TRACK_TYPE_TEXT = 3;
    /** A type constant for image tracks. */
    public static final int TRACK_TYPE_IMAGE = 4;
    /** A type constant for metadata tracks. */
    public static final int TRACK_TYPE_METADATA = 5;

    /**
     * Four initial bytes that must prefix NAL units for decoding.
     */
    public static final byte[] NAL_START_CODE = new byte[]{0, 0, 0, 1};
    public static final long TIME_UNSET = -1;
    public static final long POSITION_UNSET = -1;

    /** "cenc" scheme type name as defined in ISO/IEC 23001-7:2016. */
    @SuppressWarnings("ConstantField")
    public static final String CENC_TYPE_cenc = "cenc";

    /** "cbc1" scheme type name as defined in ISO/IEC 23001-7:2016. */
    @SuppressWarnings("ConstantField")
    public static final String CENC_TYPE_cbc1 = "cbc1";

    /** "cens" scheme type name as defined in ISO/IEC 23001-7:2016. */
    @SuppressWarnings("ConstantField")
    public static final String CENC_TYPE_cens = "cens";

    /** "cbcs" scheme type name as defined in ISO/IEC 23001-7:2016. */
    @SuppressWarnings("ConstantField")
    public static final String CENC_TYPE_cbcs = "cbcs";

    private Util() {
    }

    /**
     * Converts an integer to a long by unsigned conversion.
     *
     * <p>This method is equivalent to {@link Integer#toUnsignedLong(int)} for API 26+.
     */
    public static long toUnsignedLong(int x) {
        // x is implicitly casted to a long before the bit operation is executed but this does not
        // impact the method correctness.
        return x & 0xFFFFFFFFL;
    }

    /**
     * Constrains a value to the specified bounds.
     *
     * @param value The value to constrain.
     * @param min   The lower bound.
     * @param max   The upper bound.
     * @return The constrained value {@code Math.max(min, Math.min(value, max))}.
     */
    public static int constrainValue(int value, int min, int max) {
        return max(min, min(value, max));
    }

    /**
     * Constrains a value to the specified bounds.
     *
     * @param value The value to constrain.
     * @param min   The lower bound.
     * @param max   The upper bound.
     * @return The constrained value {@code Math.max(min, Math.min(value, max))}.
     */
    public static long constrainValue(long value, long min, long max) {
        return max(min, min(value, max));
    }

    /**
     * Returns the index of the largest element in {@code array} that is less than (or optionally
     * equal to) a specified {@code value}.
     *
     * <p>The search is performed using a binary search algorithm, so the array must be sorted. If the
     * array contains multiple elements equal to {@code value} and {@code inclusive} is true, the
     * index of the first one will be returned.
     *
     * @param array        The array to search.
     * @param value        The value being searched for.
     * @param inclusive    If the value is present in the array, whether to return the corresponding
     *                     index. If false then the returned index corresponds to the largest element strictly less
     *                     than the value.
     * @param stayInBounds If true, then 0 will be returned in the case that the value is smaller than
     *                     the smallest element in the array. If false then -1 will be returned.
     * @return The index of the largest element in {@code array} that is less than (or optionally
     * equal to) {@code value}.
     */
    public static int binarySearchFloor(
            int[] array, int value, boolean inclusive, boolean stayInBounds) {
        int index = Arrays.binarySearch(array, value);
        if (index < 0) {
            index = -(index + 2);
        } else {
            while (--index >= 0 && array[index] == value) {
            }
            if (inclusive) {
                index++;
            }
        }
        return stayInBounds ? max(0, index) : index;
    }

    /**
     * Returns the index of the largest element in {@code array} that is less than (or optionally
     * equal to) a specified {@code value}.
     * <p>
     * The search is performed using a binary search algorithm, so the array must be sorted. If the
     * array contains multiple elements equal to {@code value} and {@code inclusive} is true, the
     * index of the first one will be returned.
     *
     * @param array        The array to search.
     * @param value        The value being searched for.
     * @param inclusive    If the value is present in the array, whether to return the corresponding
     *                     index. If false then the returned index corresponds to the largest element strictly less
     *                     than the value.
     * @param stayInBounds If true, then 0 will be returned in the case that the value is smaller than
     *                     the smallest element in the array. If false then -1 will be returned.
     * @return The index of the largest element in {@code array} that is less than (or optionally
     * equal to) {@code value}.
     */
    public static int binarySearchFloor(long[] array, long value, boolean inclusive,
                                        boolean stayInBounds) {
        int index = Arrays.binarySearch(array, value);
        if (index < 0) {
            index = -(index + 2);
        } else {
            while (--index >= 0 && array[index] == value) {
            }
            if (inclusive) {
                index++;
            }
        }
        return stayInBounds ? max(0, index) : index;
    }

    /**
     * Return the long that is composed of the bits of the 2 specified integers.
     *
     * @param mostSignificantBits  The 32 most significant bits of the long to return.
     * @param leastSignificantBits The 32 least significant bits of the long to return.
     * @return a long where its 32 most significant bits are {@code mostSignificantBits} bits and its
     * 32 least significant bits are {@code leastSignificantBits}.
     */
    public static long toLong(int mostSignificantBits, int leastSignificantBits) {
        return (toUnsignedLong(mostSignificantBits) << 32) | toUnsignedLong(leastSignificantBits);
    }

    /**
     * Allows the CRC-32 calculation to be done byte by byte instead of bit per bit in the order "most
     * significant bit first".
     */
    private static final int[] CRC32_BYTES_MSBF = {
            0X00000000, 0X04C11DB7, 0X09823B6E, 0X0D4326D9, 0X130476DC, 0X17C56B6B, 0X1A864DB2,
            0X1E475005, 0X2608EDB8, 0X22C9F00F, 0X2F8AD6D6, 0X2B4BCB61, 0X350C9B64, 0X31CD86D3,
            0X3C8EA00A, 0X384FBDBD, 0X4C11DB70, 0X48D0C6C7, 0X4593E01E, 0X4152FDA9, 0X5F15ADAC,
            0X5BD4B01B, 0X569796C2, 0X52568B75, 0X6A1936C8, 0X6ED82B7F, 0X639B0DA6, 0X675A1011,
            0X791D4014, 0X7DDC5DA3, 0X709F7B7A, 0X745E66CD, 0X9823B6E0, 0X9CE2AB57, 0X91A18D8E,
            0X95609039, 0X8B27C03C, 0X8FE6DD8B, 0X82A5FB52, 0X8664E6E5, 0XBE2B5B58, 0XBAEA46EF,
            0XB7A96036, 0XB3687D81, 0XAD2F2D84, 0XA9EE3033, 0XA4AD16EA, 0XA06C0B5D, 0XD4326D90,
            0XD0F37027, 0XDDB056FE, 0XD9714B49, 0XC7361B4C, 0XC3F706FB, 0XCEB42022, 0XCA753D95,
            0XF23A8028, 0XF6FB9D9F, 0XFBB8BB46, 0XFF79A6F1, 0XE13EF6F4, 0XE5FFEB43, 0XE8BCCD9A,
            0XEC7DD02D, 0X34867077, 0X30476DC0, 0X3D044B19, 0X39C556AE, 0X278206AB, 0X23431B1C,
            0X2E003DC5, 0X2AC12072, 0X128E9DCF, 0X164F8078, 0X1B0CA6A1, 0X1FCDBB16, 0X018AEB13,
            0X054BF6A4, 0X0808D07D, 0X0CC9CDCA, 0X7897AB07, 0X7C56B6B0, 0X71159069, 0X75D48DDE,
            0X6B93DDDB, 0X6F52C06C, 0X6211E6B5, 0X66D0FB02, 0X5E9F46BF, 0X5A5E5B08, 0X571D7DD1,
            0X53DC6066, 0X4D9B3063, 0X495A2DD4, 0X44190B0D, 0X40D816BA, 0XACA5C697, 0XA864DB20,
            0XA527FDF9, 0XA1E6E04E, 0XBFA1B04B, 0XBB60ADFC, 0XB6238B25, 0XB2E29692, 0X8AAD2B2F,
            0X8E6C3698, 0X832F1041, 0X87EE0DF6, 0X99A95DF3, 0X9D684044, 0X902B669D, 0X94EA7B2A,
            0XE0B41DE7, 0XE4750050, 0XE9362689, 0XEDF73B3E, 0XF3B06B3B, 0XF771768C, 0XFA325055,
            0XFEF34DE2, 0XC6BCF05F, 0XC27DEDE8, 0XCF3ECB31, 0XCBFFD686, 0XD5B88683, 0XD1799B34,
            0XDC3ABDED, 0XD8FBA05A, 0X690CE0EE, 0X6DCDFD59, 0X608EDB80, 0X644FC637, 0X7A089632,
            0X7EC98B85, 0X738AAD5C, 0X774BB0EB, 0X4F040D56, 0X4BC510E1, 0X46863638, 0X42472B8F,
            0X5C007B8A, 0X58C1663D, 0X558240E4, 0X51435D53, 0X251D3B9E, 0X21DC2629, 0X2C9F00F0,
            0X285E1D47, 0X36194D42, 0X32D850F5, 0X3F9B762C, 0X3B5A6B9B, 0X0315D626, 0X07D4CB91,
            0X0A97ED48, 0X0E56F0FF, 0X1011A0FA, 0X14D0BD4D, 0X19939B94, 0X1D528623, 0XF12F560E,
            0XF5EE4BB9, 0XF8AD6D60, 0XFC6C70D7, 0XE22B20D2, 0XE6EA3D65, 0XEBA91BBC, 0XEF68060B,
            0XD727BBB6, 0XD3E6A601, 0XDEA580D8, 0XDA649D6F, 0XC423CD6A, 0XC0E2D0DD, 0XCDA1F604,
            0XC960EBB3, 0XBD3E8D7E, 0XB9FF90C9, 0XB4BCB610, 0XB07DABA7, 0XAE3AFBA2, 0XAAFBE615,
            0XA7B8C0CC, 0XA379DD7B, 0X9B3660C6, 0X9FF77D71, 0X92B45BA8, 0X9675461F, 0X8832161A,
            0X8CF30BAD, 0X81B02D74, 0X857130C3, 0X5D8A9099, 0X594B8D2E, 0X5408ABF7, 0X50C9B640,
            0X4E8EE645, 0X4A4FFBF2, 0X470CDD2B, 0X43CDC09C, 0X7B827D21, 0X7F436096, 0X7200464F,
            0X76C15BF8, 0X68860BFD, 0X6C47164A, 0X61043093, 0X65C52D24, 0X119B4BE9, 0X155A565E,
            0X18197087, 0X1CD86D30, 0X029F3D35, 0X065E2082, 0X0B1D065B, 0X0FDC1BEC, 0X3793A651,
            0X3352BBE6, 0X3E119D3F, 0X3AD08088, 0X2497D08D, 0X2056CD3A, 0X2D15EBE3, 0X29D4F654,
            0XC5A92679, 0XC1683BCE, 0XCC2B1D17, 0XC8EA00A0, 0XD6AD50A5, 0XD26C4D12, 0XDF2F6BCB,
            0XDBEE767C, 0XE3A1CBC1, 0XE760D676, 0XEA23F0AF, 0XEEE2ED18, 0XF0A5BD1D, 0XF464A0AA,
            0XF9278673, 0XFDE69BC4, 0X89B8FD09, 0X8D79E0BE, 0X803AC667, 0X84FBDBD0, 0X9ABC8BD5,
            0X9E7D9662, 0X933EB0BB, 0X97FFAD0C, 0XAFB010B1, 0XAB710D06, 0XA6322BDF, 0XA2F33668,
            0XBCB4666D, 0XB8757BDA, 0XB5365D03, 0XB1F740B4
    };

    /**
     * Allows the CRC-8 calculation to be done byte by byte instead of bit per bit in the order "most
     * significant bit first".
     */
    private static final int[] CRC8_BYTES_MSBF = {
            0x00, 0x07, 0x0E, 0x09, 0x1C, 0x1B, 0x12, 0x15, 0x38, 0x3F, 0x36, 0x31, 0x24, 0x23, 0x2A,
            0x2D, 0x70, 0x77, 0x7E, 0x79, 0x6C, 0x6B, 0x62, 0x65, 0x48, 0x4F, 0x46, 0x41, 0x54, 0x53,
            0x5A, 0x5D, 0xE0, 0xE7, 0xEE, 0xE9, 0xFC, 0xFB, 0xF2, 0xF5, 0xD8, 0xDF, 0xD6, 0xD1, 0xC4,
            0xC3, 0xCA, 0xCD, 0x90, 0x97, 0x9E, 0x99, 0x8C, 0x8B, 0x82, 0x85, 0xA8, 0xAF, 0xA6, 0xA1,
            0xB4, 0xB3, 0xBA, 0xBD, 0xC7, 0xC0, 0xC9, 0xCE, 0xDB, 0xDC, 0xD5, 0xD2, 0xFF, 0xF8, 0xF1,
            0xF6, 0xE3, 0xE4, 0xED, 0xEA, 0xB7, 0xB0, 0xB9, 0xBE, 0xAB, 0xAC, 0xA5, 0xA2, 0x8F, 0x88,
            0x81, 0x86, 0x93, 0x94, 0x9D, 0x9A, 0x27, 0x20, 0x29, 0x2E, 0x3B, 0x3C, 0x35, 0x32, 0x1F,
            0x18, 0x11, 0x16, 0x03, 0x04, 0x0D, 0x0A, 0x57, 0x50, 0x59, 0x5E, 0x4B, 0x4C, 0x45, 0x42,
            0x6F, 0x68, 0x61, 0x66, 0x73, 0x74, 0x7D, 0x7A, 0x89, 0x8E, 0x87, 0x80, 0x95, 0x92, 0x9B,
            0x9C, 0xB1, 0xB6, 0xBF, 0xB8, 0xAD, 0xAA, 0xA3, 0xA4, 0xF9, 0xFE, 0xF7, 0xF0, 0xE5, 0xE2,
            0xEB, 0xEC, 0xC1, 0xC6, 0xCF, 0xC8, 0xDD, 0xDA, 0xD3, 0xD4, 0x69, 0x6E, 0x67, 0x60, 0x75,
            0x72, 0x7B, 0x7C, 0x51, 0x56, 0x5F, 0x58, 0x4D, 0x4A, 0x43, 0x44, 0x19, 0x1E, 0x17, 0x10,
            0x05, 0x02, 0x0B, 0x0C, 0x21, 0x26, 0x2F, 0x28, 0x3D, 0x3A, 0x33, 0x34, 0x4E, 0x49, 0x40,
            0x47, 0x52, 0x55, 0x5C, 0x5B, 0x76, 0x71, 0x78, 0x7F, 0x6A, 0x6D, 0x64, 0x63, 0x3E, 0x39,
            0x30, 0x37, 0x22, 0x25, 0x2C, 0x2B, 0x06, 0x01, 0x08, 0x0F, 0x1A, 0x1D, 0x14, 0x13, 0xAE,
            0xA9, 0xA0, 0xA7, 0xB2, 0xB5, 0xBC, 0xBB, 0x96, 0x91, 0x98, 0x9F, 0x8A, 0x8D, 0x84, 0x83,
            0xDE, 0xD9, 0xD0, 0xD7, 0xC2, 0xC5, 0xCC, 0xCB, 0xE6, 0xE1, 0xE8, 0xEF, 0xFA, 0xFD, 0xF4,
            0xF3
    };

    public static void checkState(boolean e) {

    }


    /**
     * Returns the frame size for audio with {@code channelCount} channels in the specified encoding.
     *
     * @param pcmEncoding The encoding of the audio data.
     * @param channelCount The channel count.
     * @return The size of one audio frame in bytes.
     */
    public static int getPcmFrameSize(int pcmEncoding, int channelCount) {
        switch (pcmEncoding) {
            case AudioFormat.ENCODING_PCM_8BIT:
                return channelCount;
            case AudioFormat.ENCODING_PCM_16BIT:
                return channelCount * 2;
            case AudioFormat.ENCODING_PCM_FLOAT:
                return channelCount * 4;
            case AudioFormat.ENCODING_INVALID:
            case Format.NO_VALUE:
            default:
                throw new IllegalArgumentException();
        }
    }

    public static void checkArgument(boolean b) {

    }

    public static void printStack(String tag) {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        for (StackTraceElement e : stackTrace) {
            TekiLog.w(tag, "at " + e.getFileName() + ": method=" + e.getMethodName() + ", line=" + e.getLineNumber());
        }
    }

    /**
     * Returns whether the given character is a carriage return ('\r') or a line feed ('\n').
     *
     * @param c The character.
     * @return Whether the given character is a linebreak.
     */
    public static boolean isLinebreak(int c) {
        return c == '\n' || c == '\r';
    }

    /**
     * Returns a new {@link String} constructed by decoding UTF-8 encoded bytes.
     *
     * @param bytes The UTF-8 encoded bytes to decode.
     * @return The string.
     */
    public static String fromUtf8Bytes(byte[] bytes) {
        return new String(bytes, Charsets.UTF_8);
    }

    /**
     * Returns a new {@link String} constructed by decoding UTF-8 encoded bytes in a subarray.
     *
     * @param bytes  The UTF-8 encoded bytes to decode.
     * @param offset The index of the first byte to decode.
     * @param length The number of bytes to decode.
     * @return The string.
     */
    public static String fromUtf8Bytes(byte[] bytes, int offset, int length) {
        return new String(bytes, offset, length, Charsets.UTF_8);
    }

    /**
     * Scales a large timestamp.
     * <p>
     * Logically, scaling consists of a multiplication followed by a division. The actual operations
     * performed are designed to minimize the probability of overflow.
     *
     * @param timestamp  The timestamp to scale.
     * @param multiplier The multiplier.
     * @param divisor    The divisor.
     * @return The scaled timestamp.
     */
    public static long scaleLargeTimestamp(long timestamp, long multiplier, long divisor) {
        if (divisor >= multiplier && (divisor % multiplier) == 0) {
            long divisionFactor = divisor / multiplier;
            return timestamp / divisionFactor;
        } else if (divisor < multiplier && (multiplier % divisor) == 0) {
            long multiplicationFactor = multiplier / divisor;
            return timestamp * multiplicationFactor;
        } else {
            double multiplicationFactor = (double) multiplier / divisor;
            return (long) (timestamp * multiplicationFactor);
        }
    }

    public static long msToUs(long timeMs) {
        return timeMs < 0 ? timeMs : timeMs * 1000;
    }

    public static long usToMs(@Nullable Long timeUs) {
        return timeUs == null ? -1 : timeUs < 0 ? timeUs : timeUs / 1000;
    }


    /**
     * Creates a new array containing the concatenation of two non-null type arrays.
     *
     * @param first  The first array.
     * @param second The second array.
     * @return The concatenated result.
     */
    @SuppressWarnings({"nullness:assignment.type.incompatible"})
    public static <T> T[] nullSafeArrayConcatenation(T[] first, T[] second) {
        T[] concatenation = Arrays.copyOf(first, first.length + second.length);
        System.arraycopy(
                /* src= */ second,
                /* srcPos= */ 0,
                /* dest= */ concatenation,
                /* destPos= */ first.length,
                /* length= */ second.length);
        return concatenation;
    }


    /**
     * Divides a {@code numerator} by a {@code denominator}, returning the ceiled result.
     *
     * @param numerator The numerator to divide.
     * @param denominator The denominator to divide by.
     * @return The ceiled result of the division.
     */
    public static int ceilDivide(int numerator, int denominator) {
        return (numerator + denominator - 1) / denominator;
    }

    /**
     * Divides a {@code numerator} by a {@code denominator}, returning the ceiled result.
     *
     * @param numerator The numerator to divide.
     * @param denominator The denominator to divide by.
     * @return The ceiled result of the division.
     */
    public static long ceilDivide(long numerator, long denominator) {
        return (numerator + denominator - 1) / denominator;
    }


    /**
     * Returns the index of the smallest element in {@code array} that is greater than (or optionally
     * equal to) a specified {@code value}.
     *
     * <p>The search is performed using a binary search algorithm, so the array must be sorted. If the
     * array contains multiple elements equal to {@code value} and {@code inclusive} is true, the
     * index of the last one will be returned.
     *
     * @param array        The array to search.
     * @param value        The value being searched for.
     * @param inclusive    If the value is present in the array, whether to return the corresponding
     *                     index. If false then the returned index corresponds to the smallest element strictly
     *                     greater than the value.
     * @param stayInBounds If true, then {@code (a.length - 1)} will be returned in the case that the
     *                     value is greater than the largest element in the array. If false then {@code a.length} will
     *                     be returned.
     * @return The index of the smallest element in {@code array} that is greater than (or optionally
     * equal to) {@code value}.
     */
    public static int binarySearchCeil(
            int[] array, int value, boolean inclusive, boolean stayInBounds) {
        int index = Arrays.binarySearch(array, value);
        if (index < 0) {
            index = ~index;
        } else {
            while (++index < array.length && array[index] == value) {
            }
            if (inclusive) {
                index--;
            }
        }
        return stayInBounds ? min(array.length - 1, index) : index;
    }

    /**
     * Returns the index of the smallest element in {@code array} that is greater than (or optionally
     * equal to) a specified {@code value}.
     *
     * <p>The search is performed using a binary search algorithm, so the array must be sorted. If the
     * array contains multiple elements equal to {@code value} and {@code inclusive} is true, the
     * index of the last one will be returned.
     *
     * @param array        The array to search.
     * @param value        The value being searched for.
     * @param inclusive    If the value is present in the array, whether to return the corresponding
     *                     index. If false then the returned index corresponds to the smallest element strictly
     *                     greater than the value.
     * @param stayInBounds If true, then {@code (a.length - 1)} will be returned in the case that the
     *                     value is greater than the largest element in the array. If false then {@code a.length} will
     *                     be returned.
     * @return The index of the smallest element in {@code array} that is greater than (or optionally
     * equal to) {@code value}.
     */
    public static int binarySearchCeil(
            long[] array, long value, boolean inclusive, boolean stayInBounds) {
        int index = Arrays.binarySearch(array, value);
        if (index < 0) {
            index = ~index;
        } else {
            while (++index < array.length && array[index] == value) {
            }
            if (inclusive) {
                index--;
            }
        }
        return stayInBounds ? min(array.length - 1, index) : index;
    }

    /**
     * Returns the index of the smallest element in {@code list} that is greater than (or optionally
     * equal to) a specified value.
     *
     * <p>The search is performed using a binary search algorithm, so the list must be sorted. If the
     * list contains multiple elements equal to {@code value} and {@code inclusive} is true, the index
     * of the last one will be returned.
     *
     * @param <T>          The type of values being searched.
     * @param list         The list to search.
     * @param value        The value being searched for.
     * @param inclusive    If the value is present in the list, whether to return the corresponding
     *                     index. If false then the returned index corresponds to the smallest element strictly
     *                     greater than the value.
     * @param stayInBounds If true, then {@code (list.size() - 1)} will be returned in the case that
     *                     the value is greater than the largest element in the list. If false then {@code
     *                     list.size()} will be returned.
     * @return The index of the smallest element in {@code list} that is greater than (or optionally
     * equal to) {@code value}.
     */
    public static <T extends Comparable<? super T>> int binarySearchCeil(
            List<? extends Comparable<? super T>> list,
            T value,
            boolean inclusive,
            boolean stayInBounds) {
        int index = Collections.binarySearch(list, value);
        if (index < 0) {
            index = ~index;
        } else {
            int listSize = list.size();
            while (++index < listSize && list.get(index).compareTo(value) == 0) {
            }
            if (inclusive) {
                index--;
            }
        }
        return stayInBounds ? min(list.size() - 1, index) : index;
    }

    /**
     * Throws {@link NullPointerException} if {@code reference} is null.
     *
     * @param <T> The type of the reference.
     * @param reference The reference.
     * @return The non-null reference that was validated.
     * @throws NullPointerException If {@code reference} is null.
     */
    @SuppressWarnings({"contracts.postcondition.not.satisfied", "return.type.incompatible"})
    public static <T> T checkNotNull(@Nullable T reference) {
        if (reference == null) {
            throw new NullPointerException();
        }
        return reference;
    }


    /**
     * Applies {@link #scaleLargeTimestamp(long, long, long)} to a list of unscaled timestamps.
     *
     * @param timestamps The timestamps to scale.
     * @param multiplier The multiplier.
     * @param divisor The divisor.
     * @return The scaled timestamps.
     */
    public static long[] scaleLargeTimestamps(List<Long> timestamps, long multiplier, long divisor) {
        long[] scaledTimestamps = new long[timestamps.size()];
        if (divisor >= multiplier && (divisor % multiplier) == 0) {
            long divisionFactor = divisor / multiplier;
            for (int i = 0; i < scaledTimestamps.length; i++) {
                scaledTimestamps[i] = timestamps.get(i) / divisionFactor;
            }
        } else if (divisor < multiplier && (multiplier % divisor) == 0) {
            long multiplicationFactor = multiplier / divisor;
            for (int i = 0; i < scaledTimestamps.length; i++) {
                scaledTimestamps[i] = timestamps.get(i) * multiplicationFactor;
            }
        } else {
            double multiplicationFactor = (double) multiplier / divisor;
            for (int i = 0; i < scaledTimestamps.length; i++) {
                scaledTimestamps[i] = (long) (timestamps.get(i) * multiplicationFactor);
            }
        }
        return scaledTimestamps;
    }

    /**
     * Applies {@link #scaleLargeTimestamp(long, long, long)} to an array of unscaled timestamps.
     *
     * @param timestamps The timestamps to scale.
     * @param multiplier The multiplier.
     * @param divisor The divisor.
     */
    public static void scaleLargeTimestampsInPlace(long[] timestamps, long multiplier, long divisor) {
        if (divisor >= multiplier && (divisor % multiplier) == 0) {
            long divisionFactor = divisor / multiplier;
            for (int i = 0; i < timestamps.length; i++) {
                timestamps[i] /= divisionFactor;
            }
        } else if (divisor < multiplier && (multiplier % divisor) == 0) {
            long multiplicationFactor = multiplier / divisor;
            for (int i = 0; i < timestamps.length; i++) {
                timestamps[i] *= multiplicationFactor;
            }
        } else {
            double multiplicationFactor = (double) multiplier / divisor;
            for (int i = 0; i < timestamps.length; i++) {
                timestamps[i] = (long) (timestamps[i] * multiplicationFactor);
            }
        }
    }

}
