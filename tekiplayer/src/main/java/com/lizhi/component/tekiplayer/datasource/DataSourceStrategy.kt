package com.lizhi.component.tekiplayer.datasource

import com.lizhi.component.tekiplayer.controller.dns.DnsResolver

/**
 * 文件名：DataSourceStrategy
 * 作用：数据源策略类（待填充内容）
 * 作者：huangtianhao
 * 创建日期：2021/3/15
 */
data class DataSourceStrategy(
    val userAgent: String,
    val connectTimeout: Int,
    val readTimeout: Int,
    val rejectedContentType: List<String>,
    val extraRequestProperties: Map<String, String>,
    val dnsResolver: DnsResolver?
) {
    class Builder {

        companion object {
            /** 默认连接超时时间（毫秒）  */
            const val DEFAULT_CONNECT_TIMEOUT_MILLIS = 8_000

            /** 默认读超时时间（毫秒） */
            const val DEFAULT_READ_TIMEOUT_MILLIS = 8_000
        }

        private var userAgent: String = ""

        private var connectTimeout: Int = DEFAULT_CONNECT_TIMEOUT_MILLIS
        private var readTimeout: Int = DEFAULT_READ_TIMEOUT_MILLIS

        private var rejectedContentType: List<String> = listOf("text", "xml", "html")

        private var extraRequestProperties: Map<String, String> = emptyMap()

        private var dnsResolver: DnsResolver? = null

        fun setRejectedContentType(list: List<String>) = apply {
            this.rejectedContentType = list
        }

        fun setUserAgent(ua: String) = apply {
            this.userAgent = ua
        }

        fun setConnectTimeoutMillis(timeout: Int) = apply {
            this.connectTimeout = timeout
        }

        fun setReadTimeoutMillis(timeout: Int) = apply {
            this.readTimeout = timeout
        }

        fun setExtraRequestProperties(map: Map<String, String>) = apply {
            this.extraRequestProperties = map
        }

        fun setDnsResolver(dnsResolver: DnsResolver) = apply {
            this.dnsResolver = dnsResolver
        }

        fun build(): DataSourceStrategy {
            return DataSourceStrategy(
                userAgent,
                connectTimeout,
                readTimeout,
                rejectedContentType,
                extraRequestProperties,
                dnsResolver
            )
        }
    }
}