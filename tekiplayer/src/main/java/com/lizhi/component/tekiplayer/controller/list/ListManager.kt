package com.lizhi.component.tekiplayer.controller.list

import com.lizhi.component.tekiplayer.MediaItem
import com.lizhi.component.tekiplayer.Player
import com.lizhi.component.tekiplayer.controller.AudioProgramHolder

/**
 * 文件名：ListManager
 * 作用：播放列表层接口
 * 作者：huangtianhao
 * 创建日期：2021/3/31
 */
interface ListManager {

    var quality: Player.Quality

    @Player.RepeatMode
    var repeatMode: Int

    fun addMediaItem(item: MediaItem)

    fun addMediaItem(position: Int, item: MediaItem)

    fun addMediaItem(list: List<MediaItem>)

    fun addMediaItem(position: Int, list: List<MediaItem>)

    fun removeRange(startPosition: Int, length: Int)

    fun removeItem(item: MediaItem)

    fun removeItem(position: Int)

    fun getMediaItem(position: Int): MediaItem?

    fun getMediaItemList(): List<MediaItem>

    fun clear()

    fun shouldPrepareNextProgram(): Boolean

    fun moveToNextProgram(force: Boolean = true): AudioProgramHolder?

    fun moveToPreviousProgram(): AudioProgramHolder?

    fun moveToProgramOnPosition(
        position: Int,
        pendingSeekPositionMs: Long = -1L
    ): AudioProgramHolder?

    fun prepareNextProgram(callback: (() -> Unit)? = null): AudioProgramHolder?

    fun hasNext(): Boolean

    fun hasPrevious(): Boolean

    fun setOnPlayListUpdateListener(listener: OnPlayListUpdateListener)

    fun getCurrentProgram(): AudioProgramHolder?

    fun pausePreload()

    fun resumePreload()

    fun updatePreBufferingProgramSpeed(speed: Float)

    fun updatePreBufferingProgramVolume(volume: Float)

    fun clearPlayingAndPreBufferingProgram()

}