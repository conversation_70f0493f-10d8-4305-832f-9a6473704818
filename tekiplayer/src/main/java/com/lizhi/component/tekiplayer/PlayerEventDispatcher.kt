package com.lizhi.component.tekiplayer

import android.os.Handler
import android.os.Looper
import com.lizhi.component.tekiplayer.util.TekiLog
import java.util.concurrent.CopyOnWriteArrayList

class PlayerEventDispatcher : PlayEventListener, NetworkWatcher {

    private val listeners by lazy {
        CopyOnWriteArrayList<PlayEventListener>()
    }
    private val watchers = CopyOnWriteArrayList<(Int, Int) -> Unit>()

    private val uiHandler = Handler(Looper.getMainLooper())

    fun addPlayListEventListener(listener: PlayEventListener) {
        listeners.add(listener)
    }

    fun removePlayListEventListener(listener: PlayEventListener) {
        listeners.remove(listener)
    }

    fun addNetworkWatcher(watcher: (Int, Int) -> Unit) {
        watchers.add(watcher)
    }

    fun removeNetworkWatcher(watcher: (Int, Int) -> Unit) {
        watchers.remove(watcher)
    }

    override fun onPlaybackStateChange(status: Int) {
        listeners.callForEachOnUiThread("onPlaybackStateChange") {
            it.onPlaybackStateChange(status)
        }
    }

    override fun onError(errCode: Int, message: String) {
        listeners.callForEachOnUiThread("onError") {
            it.onError(errCode, message)
        }
    }

    override fun onPlaybackRemoveOnList() {
        listeners.callForEachOnUiThread("onPlaybackRemoveOnList") {
            it.onPlaybackRemoveOnList()
        }
    }

    override fun onPlayListUpdate() {
        listeners.callForEachOnUiThread("onPlayListUpdate") {
            it.onPlayListUpdate()
        }
    }

    override fun onPlayListFinished() {
        listeners.callForEachOnUiThread("onPlayListFinished") {
            it.onPlayListFinished()
        }
    }

    override fun onPlaybackChange(
        index: Int,
        item: MediaItem?,
        lastPosition: Long,
        reason: Int
    ) {
        TekiLog.i(
            "DefaultPlaybackStateListener",
            "onPlaybackChange index=$index, lastPosition=$lastPosition, reason=$reason"
        )

        listeners.callForEachOnUiThread("onPlaybackChange") {
            it.onPlaybackChange(index, item, lastPosition, reason)
        }
    }

    override fun onPlayedPositionUpdate(index: Int, item: MediaItem?, position: Long) {
        listeners.callForEachOnUiThread("onPlayedPositionUpdate") {
            it.onPlayedPositionUpdate(index, item, position)
        }
    }

    override fun onPlayZeroItem(item: MediaItem?) {
        TekiLog.i(
            "DefaultPlaybackStateListener",
            "onPlayZeroItem   item:${item}"
        )
        listeners.callForEachOnUiThread("onPlayZeroItem") {
            it.onPlayZeroItem(item)
        }
    }

    override fun onBufferedPositionUpdate(index: Int, item: MediaItem?, bufferPosition: Long) {
        listeners.callForEachOnUiThread("onBufferedPositionUpdate") {
            it.onBufferedPositionUpdate(index, item, bufferPosition)
        }
    }

    override fun onAudioQualityChange(to: Player.Quality) {
        listeners.callForEachOnUiThread("onAudioQualityChange") {
            it.onAudioQualityChange(to)
        }
    }

    override fun onItemRemainingUpdate(index: Int, item: MediaItem?, remainingItem: Int) {
        listeners.callForEachOnUiThread("onItemRemainingUpdate") {
            it.onItemRemainingUpdate(index, item, remainingItem)
        }
    }

    override fun onTimeRemainingUpdate(index: Int, item: MediaItem?, remainingMs: Long) {
        listeners.callForEachOnUiThread("onTimeRemainingUpdate") {
            it.onTimeRemainingUpdate(index, item, remainingMs)
        }
    }

    override fun onWatch(qualityLevel: Int, speedBps: Int) {
        watchers.forEach {
            uiHandler.post {
                runCatching {
                    it(qualityLevel, speedBps)
                }.onFailure {
                    TekiLog.e("DefaultPlaybackStateListener", "invoke", it)
                }
            }
        }
    }

    private fun List<PlayEventListener>.callForEachOnUiThread(
        method: String,
        action: (PlayEventListener) -> Unit
    ) {
        listeners.forEach {
            uiHandler.post {
                runCatching { action(it) }.onFailure {
                    TekiLog.e("DefaultPlaybackStateListener", method, it)
                }
            }
        }
    }
}