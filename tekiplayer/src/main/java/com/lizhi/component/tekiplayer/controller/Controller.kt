package com.lizhi.component.tekiplayer.controller

import android.os.Looper
import com.lizhi.component.tekiplayer.MediaItem
import com.lizhi.component.tekiplayer.Player

/**
 * 文件名：Controller
 * 作用：控制层接口
 * 作者：huangtianhao
 * 创建日期：2021/3/31
 */
interface Controller {

    var repeatMode: Int

    val position: Long

    val bufferedPosition: Long

    val duration: Long

    var speed: Float

    val volume: Float

    var pauseWhenAudioFocusLost: Boolean

    var autoHandleAudioFocus: Boolean

    fun addMediaItem(item: MediaItem)

    fun addMediaItem(position: Int, item: MediaItem)

    fun addMediaItem(list: List<MediaItem>)

    fun addMediaItem(position: Int, list: List<MediaItem>)

    fun removeRange(startPosition: Int, length: Int)

    fun removeItem(item: MediaItem)

    fun removeItem(position: Int)

    fun getCurrentMediaItem(): MediaItem?

    fun getMediaItemAt(position: Int): MediaItem?

    fun getAllMediaItems(): List<MediaItem>

    fun hasNextItem(): Boolean

    fun hasPreviousItem(): Boolean

    fun prepare()

    fun play()

    fun play(position: Int)

    fun playNext()

    fun playPrevious()

    fun stop()

    fun pause()

    fun resume()

    fun seekBy(relativeMs: Long)

    fun seekTo(absoluteMs: Long)

    fun seekTo(index: Int, absoluteMs: Long)

    fun seekTo(index: Int, percent: Float)

    fun getStatus(): Int

    /**
     * 时长结束
     */
    fun stopAfterTime(timeMs: Long)

    fun stopAtTime(absoluteTimestamp: Long)

    /**
     * [itemCount]个节目后自动暂停
     */
    fun stopAfterMediaItemFinish(itemCount: Int = 1)

    fun cancelStop()

    fun clearCache()

    fun clearAllHighPriorityCache()

    fun clear()

    fun setCdnList(cdnList: List<String>)

    fun setQuality(quality: Player.Quality)

    fun getQuality(): Player.Quality

    fun getActualQuality(): Player.Quality

    fun getCurrentItemIndex(): Int

    fun getRemainingTimeBeforeStop(): Long

    fun getRemainingItemBeforeStop(): Int

    fun getLooper(): Looper
}