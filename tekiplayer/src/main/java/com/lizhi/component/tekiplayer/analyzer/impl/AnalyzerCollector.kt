package com.lizhi.component.tekiplayer.analyzer.impl

import com.lizhi.component.tekiplayer.analyzer.Analyzer
import com.lizhi.component.tekiplayer.analyzer.BandwidthListener

/**
 * 文件名：AnalyzerCollector
 * 作用：分析器实际实现层
 * 作者：huangtianhao
 * 创建日期：2021/2/20
 */
class AnalyzerCollector : Analyzer {

    private val bandwidthSampler = BandwidthSampler()

    override fun addBandwidthListener(listener: BandwidthListener) {
        bandwidthSampler.addBandwidthListener(listener)
    }

    override fun removeBandwidthListener(listener: BandwidthListener) {
        bandwidthSampler.removeBandwidthListener(listener)
    }
}