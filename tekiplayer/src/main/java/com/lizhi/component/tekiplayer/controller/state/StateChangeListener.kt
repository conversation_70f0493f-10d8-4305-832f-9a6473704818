package com.lizhi.component.tekiplayer.controller.state

import com.lizhi.component.tekiplayer.controller.PlayerState

/**
 * 文件名：StateChangeListener
 * 作用：状态变动监听接口
 * 作者：huangtianhao
 * 创建日期：2021/4/1
 */
interface StateChangeListener {

    /**
     * 退出某个状态时的回调
     * [state] 退出的状态
     * [causeEvent] 导致退出的事件
     */
    fun onStateExit(@PlayerState.State state: Int, @PlayerEvent.Event causeEvent: Int)

    /**
     * 进入某个状态时的回调
     * [state] 进入的状态
     * [causeEvent] 导致进入的事件
     */
    fun onStateEnter(@PlayerState.State state: Int, @PlayerEvent.Event causeEvent: Int)
}