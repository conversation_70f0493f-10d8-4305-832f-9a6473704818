package com.lizhi.component.tekiplayer.util.aes

import android.util.Base64
import android.util.Log
import java.io.InputStream
import javax.crypto.Cipher

class AesComponent {
    private val TAG = "AesComponent"

    var decryptCipher: Cipher?= null
    var encryptCipher: Cipher?= null
    var cipherInputStream: LzCipherInputStream?= null

    @Throws(Exception::class)
    fun setCipher(key:String, iv:String, mode:Int = Cipher.DECRYPT_MODE) {
        val key = Base64.decode(key, Base64.NO_WRAP)
        val iv = Base64.decode(iv, Base64.NO_WRAP)
//        val sbkey = java.lang.StringBuilder()
//        for (b in key) {
//            val value = b.toInt() and 0xFF
//            sbkey.append(String.format(Locale.getDefault(), "%02x ", value))
//        }
//        val sbiv = StringBuilder()
//        for (b in iv) {
//            val value = b.toInt() and 0xFF
//            sbiv.append(String.format(Locale.getDefault(), "%02x ", value))
//        }
//        Log.e(TAG, "setCipher key:$sbkey iv:$sbiv")
        val secretKeySpec = AESUtil.generateSecretKeySpec(key)
        val ivSpec = AESUtil.generateIvParameterSpec(iv)
        if (mode == Cipher.DECRYPT_MODE) {
            decryptCipher = AESUtil.getCipher(Cipher.DECRYPT_MODE, secretKeySpec, ivSpec)
        } else if (mode == Cipher.ENCRYPT_MODE) {
            encryptCipher = AESUtil.getCipher(Cipher.ENCRYPT_MODE, secretKeySpec, ivSpec)
        }
    }

    @Throws(Exception::class)
    fun decryptData(data: ByteArray) : ByteArray? {
        return try {
            decryptCipher?.doFinal(data)
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e(TAG, "decryptData data exception:$e")
            throw Exception(e)
        }
    }

    @Throws(Exception::class)
    fun convertDecryptInputStream(key:String, iv:String, inputStream: InputStream, preData: ByteArray? = null, ignoreLength:Long, encryptCallback: (encryptData: ByteArray) -> Unit ?): InputStream {
        setCipher(key, iv)
        cipherInputStream = LzCipherInputStream(inputStream, key, iv, decryptCipher!!, preData, ignoreLength, encryptCallback)
        return cipherInputStream!!
    }

    fun getUnUseCacheCount(): Int{
        return cipherInputStream?.getUnUseCacheCount() ?: 0
    }

}