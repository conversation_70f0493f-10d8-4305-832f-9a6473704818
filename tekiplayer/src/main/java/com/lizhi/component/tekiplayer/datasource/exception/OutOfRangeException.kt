package com.lizhi.component.tekiplayer.datasource.exception

import com.lizhi.component.tekiplayer.ERR_DATASOURCE_HTTP_OUT_OF_RANGE
import com.lizhi.component.tekiplayer.datasource.Range
import com.lizhi.component.tekiplayer.datasource.getRangeRequestStr

/**
 * 文件名：InvalidResponseCodeException
 * 作用：非正常响应码
 * 作者：huangtianhao
 * 创建日期：2021/3/17
 */
class OutOfRangeException(
    val url: String,
    val range: Range
) : HttpDataSourceException(
    code = ERR_DATASOURCE_HTTP_OUT_OF_RANGE,
    message = "Connecting $url out of range ${range.getRangeRequestStr()}"
)