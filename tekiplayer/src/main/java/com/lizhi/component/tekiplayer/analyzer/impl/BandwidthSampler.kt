package com.lizhi.component.tekiplayer.analyzer.impl

import android.os.SystemClock
import com.lizhi.component.tekiplayer.analyzer.BandwidthListener
import com.lizhi.component.tekiplayer.analyzer.BandwidthQuality
import com.lizhi.component.tekiplayer.analyzer.Sampler
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 文件名：BandwidthSampler
 * 作用：带宽采样分析
 * 作者：huangtianhao
 * 创建日期：2021/2/20
 */
class BandwidthSampler : Sampler {

    private val SAMPLE_INTERVAL = 2_000L
    private val weights = arrayOf(1, 1, 2, 3, 3)
    private val bandwidthListeners = CopyOnWriteArrayList<BandwidthListener>()
    private var currentProgramDataAmount: Long = -1
    private var startSampleTime = Long.MAX_VALUE
    private var onError = false
    private val sampleDataList = ArrayList<Pair<Int, Long>>()
    private val lock = Any()

    override fun startSample() {
        // 重置部分用于流量分析打点的数据
        currentProgramDataAmount = 0L
        startSampleTime = SystemClock.elapsedRealtime()
    }

    override fun endSample() = synchronized(lock) {
        // 如果已经停止，无法再次停止，请进行start
        if (isNotStarted()) {
            return
        }
        // 结束采样，停止结果返回
        val sampleDuration = SystemClock.elapsedRealtime() - startSampleTime

        // 时长 > 0 才处理带宽计算的逻辑
        if (sampleDuration > 0) {
            // 速度 bps
            val speed = (currentProgramDataAmount * 8 / sampleDuration * 1000).toInt()
            // 计算5次（10秒内）采样平均值
            if (sampleDataList.size == 5) {
                sampleDataList.removeAt(0)
            }
            sampleDataList.add(Pair(speed, sampleDuration))
            // 带权重的网速计算
            val sampleSizeWithWeight = sampleDataList.foldIndexed(0L) { i, sum, pair ->
                sum + pair.first * pair.second * weights[i]
            }
            val sampleTime = sampleDataList.fold(0L) { sum, pair ->
                sum + pair.second
            }
            val weightSum = weights.take(sampleDataList.size).sum() * 1.0f / sampleDataList.size
            val avgSpeed = if (sampleTime == 0L || weightSum == 0f) {
                0
            } else {
                (sampleSizeWithWeight / sampleTime / weightSum).toInt()
            }
            // 通知
            bandwidthListeners.forEach {
                it.onBandwidthQualityChanged(BandwidthQuality.getLevel(avgSpeed), avgSpeed)
            }
        }
        //
        currentProgramDataAmount = -1
        startSampleTime = Long.MAX_VALUE
    }

    override fun enterError() {
        onError = true
        endSample()
        bandwidthListeners.forEach {
            it.onNetError(true)
        }
    }

    private fun isNotStarted() = currentProgramDataAmount < 0 && startSampleTime == Long.MAX_VALUE

    override fun sampleData(duration: Int, length: Long) {
        if (isNotStarted()) {
            startSample()
        }
        // 采样时如果发现处于错误状态，则先退出弱网
        if (onError) {
            onError = false
            bandwidthListeners.forEach {
                it.onNetError(false)
            }
        }
        //
        currentProgramDataAmount += length
        val elapsedRealtime = SystemClock.elapsedRealtime()
        if (elapsedRealtime - startSampleTime >= SAMPLE_INTERVAL) {
            endSample()
            startSample()
        }
    }

    override fun addBandwidthListener(listener: BandwidthListener) {
        bandwidthListeners.addIfAbsent(listener)
    }

    override fun removeBandwidthListener(listener: BandwidthListener) {
        bandwidthListeners.remove(listener)
    }

}