@file:Suppress("unused", "MemberVisibilityCanBePrivate")

package com.lizhi.component.tekiplayer.audioprogram

import android.media.AudioTrack
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.os.Process
import android.os.SystemClock
import com.lizhi.component.tekiplayer.ERR_DATASOURCE_HTTP_CONNECTION
import com.lizhi.component.tekiplayer.analyzer.BandwidthListener
import com.lizhi.component.tekiplayer.analyzer.BandwidthQuality
import com.lizhi.component.tekiplayer.analyzer.Reporter.Companion.REPORT_STATUS_EMPTY
import com.lizhi.component.tekiplayer.analyzer.Reporter.Companion.REPORT_STATUS_INIT
import com.lizhi.component.tekiplayer.analyzer.Reporter.Companion.REPORT_STATUS_SEEK
import com.lizhi.component.tekiplayer.analyzer.impl.BandwidthSampler
import com.lizhi.component.tekiplayer.analyzer.impl.EventReporter
import com.lizhi.component.tekiplayer.analyzer.impl.EventReporter.Companion.CIPHER_IS_REAL_TIME_DECRYPT
import com.lizhi.component.tekiplayer.analyzer.impl.EventReporter.Companion.CIPHER_IV
import com.lizhi.component.tekiplayer.analyzer.impl.EventReporter.Companion.CIPHER_KEY
import com.lizhi.component.tekiplayer.audioprogram.extractor.BundledExtractorsAdapter
import com.lizhi.component.tekiplayer.audioprogram.extractor.Extractor
import com.lizhi.component.tekiplayer.audioprogram.extractor.ExtractorsFactory
import com.lizhi.component.tekiplayer.audioprogram.extractor.PositionHolder
import com.lizhi.component.tekiplayer.audioprogram.extractor.SeekPoint
import com.lizhi.component.tekiplayer.audioprogram.listener.AudioProgramListener
import com.lizhi.component.tekiplayer.audioprogram.listener.AudioProgramListener.Companion.REASON_BUFFERING_NOT_ENOUGH
import com.lizhi.component.tekiplayer.audioprogram.listener.AudioProgramListener.Companion.REASON_BUFFERING_PREPARE
import com.lizhi.component.tekiplayer.audioprogram.loader.HandlerLoader
import com.lizhi.component.tekiplayer.audioprogram.loader.Loader
import com.lizhi.component.tekiplayer.audioprogram.loader.Loader.LoadErrorAction
import com.lizhi.component.tekiplayer.audioprogram.request.HostReplaceRequestControl
import com.lizhi.component.tekiplayer.audioprogram.request.RequestControl
import com.lizhi.component.tekiplayer.configuration.BufferSizePolicy
import com.lizhi.component.tekiplayer.configuration.NetType.TYPE_WIFI
import com.lizhi.component.tekiplayer.datasource.BaseDataSource
import com.lizhi.component.tekiplayer.datasource.CacheableDataSource
import com.lizhi.component.tekiplayer.datasource.DataSource
import com.lizhi.component.tekiplayer.datasource.DataSourceCallback
import com.lizhi.component.tekiplayer.datasource.exception.CacheFileNotInitException
import com.lizhi.component.tekiplayer.datasource.exception.HttpDataSourceException
import com.lizhi.component.tekiplayer.datasource.exception.OutOfRangeException
import com.lizhi.component.tekiplayer.engine.AudioDataQueue
import com.lizhi.component.tekiplayer.engine.DataQueue
import com.lizhi.component.tekiplayer.engine.DataQueue.DataLevel
import com.lizhi.component.tekiplayer.engine.DataQueue.DataLevelWatcher
import com.lizhi.component.tekiplayer.engine.Engine.Callback
import com.lizhi.component.tekiplayer.engine.MediaAudioEngine
import com.lizhi.component.tekiplayer.util.TekiLog
import com.lizhi.component.tekiplayer.util.Util
import com.lizhi.component.tekiplayer.util.aes.AESUtil
import com.lizhi.component.tekiplayer.util.aes.CipherException
import java.io.IOException
import java.util.UUID
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger

/**
 * 文件名：AudioProgram
 * 作用：音频节目（包含播放控制、缓冲控制、和Analyser交互的作用）
 * 作者：<EMAIL>
 * 创建日期：2021/2/19
 */
class AudioProgramFactory(
    var volume: Float,
    var speed: Float,
    private val bufferPolicy: BufferSizePolicy,
    private var programListener: AudioProgramListener? = null,
    private val extractorsFactory: ExtractorsFactory,
    private val dataSourceFactory: DataSource.Factory,
    private val cdnUrlPattern: String?,
    private val cacheDuration: Int
) : Program.Factory {

    override fun createProgram(
        uri: Uri,
        seekTimeUs: Long,
        preBuffering: Boolean,
        fixedDuration: Long,
        category: String?,
        extraData: Bundle?
    ): Program {
        return AudioProgram(
            uri,
            preBuffering,
            volume,
            speed,
            bufferPolicy,
            seekTimeUs,
            programListener,
            extractorsFactory,
            dataSourceFactory,
            cdnUrlPattern,
            UUID.randomUUID().toString(),
            cacheDuration,
            fixedDuration,
            category,
            extraData
        )
    }
}

class AudioProgram(
    private val uri: Uri,
    var preBuffering: Boolean,
    volume: Float,
    private val speed: Float,
    private val bufferPolicy: BufferSizePolicy,
    private var seekPositionUs: Long = 0,
    private var programListener: AudioProgramListener? = null,
    private val extractorsFactory: ExtractorsFactory,
    private val dataSourceFactory: DataSource.Factory,
    cdnUrlPattern: String?,
    override val uuid: String,
    private val cacheDuration: Int,
    private val fixedDuration: Long,
    category: String? = null,
    private val extraData: Bundle? = null
) : Program, DataLevelWatcher, DataSourceCallback, Callback {

    companion object {
        private val TAG = "AudioProgram"

        const val NON_EMPTY = 0 // 不卡顿
        const val EMPTY_ON_SEEKING = 1 // 刚seek没有buffer
        const val EMPTY_NO_MORE_DATA = 2 // 暂时没有数据

        const val PREBUFFER_NONE = 0
        const val PREBUFFER_INIT = 1
        const val PREBUFFER_STARTED = 2
        const val PREBUFFER_FINISH = 3
    }

    private val dataQueue: DataQueue = AudioDataQueue(bufferPolicy, this)

    private val requestControl: RequestControl by lazy {
        HostReplaceRequestControl(cdnUrlPattern, uri)
    }
    private val loader = SampleLoader()

    private val reporter = EventReporter(uuid, uri.toString(), category = category, extraData = extraData)
    private val bandwidthSampler = BandwidthSampler().apply { addBandwidthListener(loader) }

    private var dataSource: BaseDataSource = createDataSource()

    private val extractorAdapter by lazy {
        BundledExtractorsAdapter(extractorsFactory)
    }


    private val engine by lazy {
        MediaAudioEngine(extractorAdapter, bufferPolicy, uuid, volume, speed).apply {
            setCallback(this@AudioProgram, handler)
        }
    }
    private val handler = Handler(Looper.myLooper() ?: throw Exception("所在线程没有looper"))

    private var prepareCallback: ((AudioProgram) -> Unit)? = null
    private var preparingErrorCallback: ((AudioProgram, Throwable?) -> Unit)? = null

    @Volatile
    private var firstFullCacheCallback = true

    private var pendingSeek = seekPositionUs != 0L
    private var seekPercent: Float = -1.0f
    private val emptyData = AtomicInteger(0) // 判断是否卡顿
    private val loadFinished = AtomicBoolean(false)

    @Volatile
    private var prepared: Boolean? = null
    private var playbackEnd: Boolean = false
    private var prebufferFinishFlag = if (preBuffering) PREBUFFER_INIT else PREBUFFER_NONE

    private var readyCostTime = 0L
    private var playStartTime = 0L

    private val positionHolder = PositionHolder()

    private val cipherKey: String? by lazy {
        extraData?.getString(AESUtil.AES_KEY, null)
    }
    private val cipherIv: String? by lazy {
        extraData?.getString(AESUtil.AES_IV, null)
    }
    private val isRealTimeDecrypt: Boolean by lazy {
        extraData?.getBoolean(AESUtil.AES_IS_REAL_TIME_DECRYPT, false) ?: false
    }

    override var volume: Float = volume
        set(value) {
            if (value >= AudioTrack.getMinVolume() && value <= AudioTrack.getMaxVolume()) {
                field = value
                engine.setVolume(field)
            }
        }

    override val durationUs: Long
        get() = if(fixedDuration < 1L) extractorAdapter.durationUs ?: -1 else fixedDuration

    override var lastFatalError: Throwable? = null

    override fun onLevelChange(
        level: Int,
        cacheSize: Long,
        reason: Int
    ) {
    }

    override fun isReady() = prepared == true

    override fun isBuffering() = prepared == false || loader.isLoading

    override fun isPlaying() = isReady() && engine.isPlaying()

    override fun isEnd() = playbackEnd

    override fun isOnError(): Boolean = lastFatalError != null

    private fun createDataSource(): BaseDataSource {
        val replacedUri = try {
            requestControl.getReplaceUri(uri)
        } catch (e: Exception) {
            TekiLog.e(TAG, "failed to replace uri", e)
            uri
        }
        return (dataSourceFactory.create(replacedUri, extraData) as BaseDataSource).apply {
            updateDataSourceCallback(this@AudioProgram)
            dataSourceCallback = this@AudioProgram
            if (replacedUri.host != uri.host) {
                this.replacedCdnHost = replacedUri.host
            } else {
                this.replacedCdnHost = null
            }
            reporter.cdnStatus = this.replacedCdnHost
            sampler = <EMAIL>
        }
    }


    /**
     * 预加载
     */
    override fun preload(
        prepareCallback: (AudioProgram) -> Unit,
        error: (AudioProgram, Throwable?) -> Unit
    ) {
        if (prepared != null) {
            return
        }
        TekiLog.i(TAG, "$uuid: program=$this start preload")
        val startTime = SystemClock.elapsedRealtime()
        prepared = false
        this.prepareCallback = {
            readyCostTime = SystemClock.elapsedRealtime() - startTime
            prepareCallback(this)
        }
        this.preparingErrorCallback = error

        startLoading(0L)
    }

    private fun startLoading(position: Long) {
        if (loadFinished.get()) {
            TekiLog.w(TAG, "$uuid: $uri has load complete, will not start loading")
            return
        }
//        val loadable = SampleLoadable()
//        loadable.loadPosition = position
//        TekiLog.e(TAG, "$uuid: start loading task=$loadable position=$position")
        loader.startLoading(position)

        val reason = if (!isReady()) REASON_BUFFERING_PREPARE else REASON_BUFFERING_NOT_ENOUGH
        programListener?.onPlaybackBuffering(this, reason)
    }

    /**
     * 播放
     */
    override fun play() {
        preBuffering = false

        if (isReady() && !isPlaying()) {
            if (!engine.configurated) {
                playStartTime = System.currentTimeMillis()
                engine.start()
                programListener?.onPlaybackStarted(this)
            } else {
                engine.resume()
                programListener?.onPlaybackResumed(this)

            }
        } else {
            TekiLog.w(TAG, "$uuid: program=$this is not ready, can not play")
        }
    }

    override fun stopLoading() {
        if (loader.isLoading) {
            TekiLog.i(TAG, "$uuid: stop loading force")
            loader.cancelLoading()
        }
    }

    override fun resume() {
        play()
    }

    override fun seek(positionUs: Long) {
        TekiLog.i(TAG, "seek positionUs:$positionUs")
        seek(positionUs, false)
    }

    override fun seek(percent: Float) {
        if (percent < 0) {
            TekiLog.e(TAG, "seek percent less than 0")
            return
        }
        if (!isReady()) {
            this.seekPercent = percent
            pendingSeek = true
        } else {
            TekiLog.i(TAG, "seek percent:$percent")
            seek((durationUs * percent).toLong(), false)
        }
    }

    private fun seek(
        positionUs: Long,
        pendingPrepareCallback: Boolean = false
    ) {
        if (!isReady()) {
            pendingSeek = true
            seekPositionUs = positionUs
            return
        }
        engine.flush()
        reporter.bufferingStatus = if(pendingPrepareCallback) REPORT_STATUS_INIT else REPORT_STATUS_SEEK
        programListener?.onNeedMoreData(this, true)

        val currBufferedPosition = dataQueue.getWrittenPosition()
        val currBufferedTimeUs = getBufferedPositionUs()

        val (seekSuccess, seekPoint) = seekInBuffer(positionUs)
        programListener?.onPlaybackSeeking(
            this,
            currBufferedTimeUs,
            currBufferedPosition,
            seekPoint.timeUs,
            seekPoint.position
        )
        // 如果在buffer里面seek成功，则不需要到数据层去seek
        if (seekSuccess) {
            pendingSeek = false
            emptyData.set(NON_EMPTY)
            seekPositionUs = 0
            TekiLog.i(TAG, "$uuid: seek in time=$positionUs")
            // 回调
            if (pendingPrepareCallback) {
                prepareCallback?.invoke(this)
            } else {
                programListener?.onBufferEnough(this)
            }
            programListener?.onPlaybackSeekSuccess(this, seekPoint.timeUs, seekPoint.position)
        } else {
            TekiLog.i(TAG, "$uuid: start seeking time=$positionUs, position=$seekPoint")
            seekPositionUs = seekPoint.timeUs
            loader.seek(seekPoint.position)
        }
        requestControl.resetIndex()
        engine.resetPositionUs(seekPoint.timeUs)
    }

    private fun seekInBuffer(timeUs: Long): Pair<Boolean, SeekPoint> {
        val dataQueue = dataQueue
        val position = extractorAdapter.timeToPosition(timeUs)
        val seekOnQueue = dataQueue.seek(position.position)
        return Pair(seekOnQueue, position)
    }

    override fun continueLoading() {
        val position = dataQueue.getWrittenPosition()
        TekiLog.i(TAG, "$uuid: continueLoading position=$position")
        startLoading(position)
    }

    /**
     * 停止播放
     */
    override fun stop() {
        if (prepared == null) {
            return
        }
        prepared = null
        pendingSeek = false

        engine.stop()
        loader.release()

//        改到 loader release 的时候 close
//        try {
//            dataSource.close()
//        } catch (e: CacheFileNotInitException) {
//            TekiLog.e(TAG, "stop() failed to close cache", e)
//        }
        (dataSource as CacheableDataSource).releaseCache()
        TekiLog.w(TAG, "stop(). dataQueue.clear")
        dataQueue.clear()
        extractorAdapter.release()

        bandwidthSampler.removeBandwidthListener(loader)
        programListener?.onPlaybackStopped(this)
    }

    /**
     * 暂停播放
     */
    override fun pause() {
        if (!engine.isPlaying()) {
            return
        }
//        emptyData.set(NON_EMPTY)
        pendingSeek = false

        engine.pause()

        programListener?.onPlaybackPaused(this)
    }

    /**
     * 当前缓冲的位置
     * @return
     */
    override fun getBufferedPositionUs(): Long {
        return dataQueue.getBufferPositionUs()
    }

    /**
     * 设置播放速率
     */
    override fun setPlayRate(rate: Float) {
        engine.setSpeed(rate)
    }

    var vailFrameCount = 0

    inner class SampleLoader :
        HandlerLoader("TekiPlayer:Loader:$uuid", Process.THREAD_PRIORITY_FOREGROUND),
        BandwidthListener {


        private var result = 0
        private var shouldContinue = true

        override fun load(message: Message): Message? {
            return when (message.what) {
                MSG_OPEN -> {
                    val loadPosition = message.obj as Long
                    TekiLog.i(
                        TAG, "$uuid: start load position=${loadPosition} pendingSeek=${pendingSeek}"
                    )
                    with(dataSource) {
                        setAesInfo(cipherKey, cipherIv, isRealTimeDecrypt)
                        open(loadPosition)
                    }
                    reporter.cacheStatus =
                        (dataSource as CacheableDataSource).hasCacheOnPosition(loadPosition)

                    extractorAdapter.init(
                        dataSource,
                        uri,
                        dataSource.responseHeaders,
                        loadPosition,
                        dataSource.contentLength
                            ?: -1,
                        dataQueue
                    )
                    message.apply {
                        what = MSG_READ
                        obj = loadPosition
                    }
                }
                MSG_READ -> {
                    result = extractorAdapter.read(positionHolder)

                    // 如果还没ready，判断是否可以到达ready状态
                    // 很短的音频（例如1kb的aac）可能在一次 read 就结束，但它的seekMap、长度等需要再第二轮 read 才准备完成
                    // 需要去掉 result > 0 的判断
                    if (!isReady() && durationUs > 0) {
                        engine.configFormat(extractorAdapter.mediaFormat!!)
                        engine.setAesInfo(cipherKey, cipherIv, isRealTimeDecrypt)
                        prepared = true

                        doOnPlayerThread {
                            if (pendingSeek) {
                                // TODO 打补丁把百分比 seek 的加上
                                if (seekPercent >= 0) {
                                    seekPositionUs = (durationUs * seekPercent).toLong()
//                                    TekiLog.d(TAG, "pendingSeek seekPercent=$seekPercent, seekPositionUs=$seekPositionUs, duration=$durationUs")
                                }
                                TekiLog.d(TAG, "MSG_READ seek seekPositionUs=$seekPositionUs")
                                seek(seekPositionUs, pendingPrepareCallback = true)
                            } else {
                                prepareCallback?.invoke(this@AudioProgram)
                                programListener?.onBufferEnough(this@AudioProgram)
                            }
                        }
                    } else if (emptyData.get() != NON_EMPTY && result != Extractor.RESULT_SEEK) {
                        // 如果是seeking或者空数据状态，先加载多几帧再播放
                        if (vailFrameCount < 4 && result >= 0) {
                            vailFrameCount++
                        } else if ((vailFrameCount > 0 && result < 0) || vailFrameCount >= 4 && isBufferEnough()) {
                            // 如果是数量不足4帧但已经到结尾 或者 已经超过4帧，往下执行
                            val emptyReason = emptyData.get()
                            emptyData.set(NON_EMPTY)
                            vailFrameCount = 0

                            doOnPlayerThread {
                                TekiLog.i(TAG, "$uuid: seeking after load $this")
                                if (pendingSeek) {
                                    prepareCallback?.invoke(this@AudioProgram)
                                    pendingSeek = false
                                }
                                programListener?.onBufferEnough(this@AudioProgram)
                                if (emptyReason == EMPTY_ON_SEEKING) {
                                    programListener?.onPlaybackSeekSuccess(
                                        this@AudioProgram,
                                        seekPositionUs,
                                        loadPosition
                                    )
                                }
                                seekPositionUs = 0L
                            }
                        } else if (vailFrameCount <= 0 && result < 0) {
                            // 一开始就超出读取范围
                            // 空数据状态下，如果读到 -1 则认为节目已经完成
                            TekiLog.w(TAG, "$uuid: playbackCompletion on read -1")
                            doOnPlayerThread {
                                playbackEnd = true
                                programListener?.onPlaybackCompletion(this@AudioProgram)
                            }
                        }
                    }
                    // 如果返回-1，说明没有更多可加载数据了
                    when (result) {
                        Extractor.RESULT_END_OF_INPUT -> {
                            shouldContinue = false
                            loadFinished.set(true)
                            TekiLog.i(TAG, "$uuid: onLoadCompleted")
                            doOnPlayerThread {
                                programListener?.onBufferedComplete(this@AudioProgram)
                                if (firstFullCacheCallback) {
                                    programListener?.onBufferSizeStateChange(
                                        this@AudioProgram,
                                        DataLevel.HIGH,
                                        -1,
                                        firstFullCacheCallback
                                    )
                                    firstFullCacheCallback = false
                                }
                            }
                        }
                        Extractor.RESULT_CONTINUE -> {
                            shouldContinue = true
                        }
                        else -> {
                            shouldContinue = false
                        }
                    }
                    // 缓存水位检查
                    val bufferSize = dataQueue.getBufferSize()
                    val writtenPosition = dataQueue.getWrittenPosition()
                    // 是否预缓冲
                    val prebufferFull = bufferSize >= bufferPolicy.preloadBufferSize() && preBuffering
                    if (prebufferFull) {
                        prebufferFinishFlag = PREBUFFER_FINISH
                    } else if (preBuffering) {
                        prebufferFinishFlag = PREBUFFER_STARTED
                    }
                    reporter.prebufferStatus = prebufferFinishFlag
                    // 缓存已经满的情况下，暂停下载
                    if (bufferSize >= bufferPolicy.playingProgramBufferSize(TYPE_WIFI) || prebufferFull) {
                        shouldContinue = false
                        TekiLog.i(
                            TAG,
                            "$uuid: 暂停下载，缓冲已经到达设定上限, cacheSize=$bufferSize, bufferPosition=$writtenPosition"
                        )
                        doOnPlayerThread {
                            programListener?.onBufferSizeStateChange(
                                this@AudioProgram,
                                DataLevel.HIGH,
                                bufferSize,
                                firstFullCacheCallback
                            )
                            firstFullCacheCallback = false
                        }
                    }
                    // 下一次加载消息
                    if (shouldContinue) {
                        message.apply {
                            if (!pendingSeek) {
                                obj = writtenPosition
                            }
                            what = MSG_READ
                        }
                    } else {
                        // MP4有可能读取的过程中位置不是连续的，有较大间隙的话可以选择seek，而不是一直读到那个位置为止
                        if (result == Extractor.RESULT_SEEK) {
                            message.what = MSG_OPEN
                            message.obj = positionHolder.position
                            message
                        } else {
                            TekiLog.w(
                                TAG,
                                "$uuid: stop loading, result=$result, shouldLoad=$shouldContinue, canceled=${isCanceled}"
                            )
                            null
                        }
                    }
                }
                MSG_SEEK -> {
                    removeMessage(MSG_OPEN)
                    removeMessage(MSG_READ)

                    TekiLog.i(TAG, "$uuid: clear dataQueue when seek to $seekPositionUs, ${message.obj}")
                    emptyData.set(EMPTY_ON_SEEKING)
                    dataQueue.clear()
                    extractorAdapter.seek(seekPositionUs, message.obj as Long)
                    loadFinished.set(false)
                    vailFrameCount = 0

                    message.what = 0
                    message
                }
                else -> null
            }
        }

        override fun onLoadCanceled() {
            try {
                dataSource.close()
            } catch (e: CacheFileNotInitException) {
                TekiLog.e(AudioProgram.TAG, "onLoadCanceled failed to close cache", e)
            }
            bandwidthSampler.endSample()
        }

        override fun onLoadError(
            currentError: IOException,
            errorCount: Int
        ): LoadErrorAction {
            TekiLog.e(AudioProgram.TAG, "onLoadError on program:$uuid", currentError.cause ?: currentError)
            // 网络错误上报弱网
            if (currentError.cause is HttpDataSourceException && (currentError.cause as HttpDataSourceException).code == ERR_DATASOURCE_HTTP_CONNECTION
                || currentError.cause == null
            ) {
                bandwidthSampler.enterError()
            }

            val action: LoadErrorAction

            if (currentError.cause?.message == "Searched too many bytes." ||
                currentError.cause?.message?.startsWith("None of the available extractors") == true ||
                currentError.cause is CipherException
            ) {
                TekiLog.e(
                    AudioProgram.TAG,
                    "${currentError.cause?.message}, delete cache"
                )
                (dataSource as CacheableDataSource).deleteCache()
            }

            // preparingErrorCallback?.invoke(this, error?.cause ?: error)
            val dataLevel = when {
                prepared == false -> {
                    RequestControl.DataLevel.PREPARING
                }
                emptyData.get() != NON_EMPTY -> {
                    RequestControl.DataLevel.EMPTY_DATA
                }
                preBuffering -> {
                    RequestControl.DataLevel.PRE_BUFFERING
                }
                dataQueue.getBufferSize() > bufferPolicy.shouldLoadingThresholds(TYPE_WIFI) -> {

                    RequestControl.DataLevel.PLAYING_HIGH
                }
                dataQueue.getBufferSize() <= bufferPolicy.shouldLoadingThresholds(TYPE_WIFI) -> {
                    RequestControl.DataLevel.PLAYING_LOW
                }
                else -> throw IllegalStateException("unreachable state")
            }

            val replacedUrl = requestControl.getRetryUri(uri, dataLevel, currentError, errorCount)
            action = if (replacedUrl.uri != null) {
                if (replacedUrl.uri.toString() != dataSource.getUrl()) {
                    try {
                        dataSource.close()
                    } catch (e: CacheFileNotInitException) {
                        TekiLog.e(AudioProgram.TAG, "onLoadError failed to close cache", e)
                    }
                    dataSource =
                        (dataSourceFactory.create(replacedUrl.uri, extraData) as BaseDataSource).apply {
                            updateDataSourceCallback(this@AudioProgram)
                            dataSourceCallback = this@AudioProgram
                            sampler = <EMAIL>
                        }
                    if (replacedUrl.uri.host != uri.host) {
                        dataSource.replacedCdnHost = replacedUrl.uri.host
                    } else {
                        dataSource.replacedCdnHost = null
                    }
                    reporter.cdnStatus = dataSource.replacedCdnHost
                }
                if (replacedUrl.resetErrorCount) {
                    Loader.RETRY_RESET_ERROR_COUNT
                } else {
                    Loader.RETRY
                }
            } else {
                if (replacedUrl.isFatalError) {
                    Loader.DONT_RETRY_FATAL
                } else {
                    Loader.DONT_RETRY
                }
            }

            if (action == Loader.DONT_RETRY_FATAL) {
                //
                programListener?.onPlaybackFailed(this@AudioProgram, currentError, false)
                (dataSource as CacheableDataSource).releaseCache()
                // 部分特殊异常还是需要AudioProgram进行处理
                if (currentError.cause is OutOfRangeException) {
                    playbackEnd = true
                    TekiLog.w(TAG, "$uuid: playbackCompletion on read OutOfRangeException")
                    programListener?.onPlaybackCompletion(this@AudioProgram)
                    return Loader.DONT_RETRY
                }

                when (dataLevel) {
                    RequestControl.DataLevel.PREPARING -> {
                        preparingErrorCallback?.invoke(
                            this@AudioProgram, currentError.cause
                                ?: currentError
                        )
                        TekiLog.e(AudioProgram.TAG, "$uuid: error when preparing")
                    }
                    RequestControl.DataLevel.EMPTY_DATA -> {
                        TekiLog.e(AudioProgram.TAG, "$uuid: error when emptyData")
                    }
                    RequestControl.DataLevel.PRE_BUFFERING -> {
                        TekiLog.e(AudioProgram.TAG, "$uuid: error when preBuffering")
                    }
                }
                lastFatalError = currentError
                TekiLog.e(AudioProgram.TAG, "$uuid: on fatal error, trigger end")
            } else {
                programListener?.onPlaybackFailed(this@AudioProgram, currentError, true)
            }
            TekiLog.d(TAG, "$uuid: [uri] $uri [retryInterval] is ${replacedUrl.retryInterval}")
            action.retryDelayMillis = replacedUrl.retryInterval

            return action
        }

        override fun onNetError(enter: Boolean) {
            reporter.onWeakNetworkChecked(BandwidthQuality.LEVEL_UNKNOWN, 0, enter)
        }

        private var currNetQuality = BandwidthQuality.LEVEL_UNKNOWN
        private var currNetSpeed = 0

        override fun onBandwidthQualityChanged(qualityLevel: Int, speed: Int) {
            programListener?.onBandwidthQualityChanged(this@AudioProgram, qualityLevel, speed)
            currNetSpeed = speed
            currNetQuality = qualityLevel
            // 预缓冲的数据不上报
            if (preBuffering) {
                return
            }
            reporter.onWeakNetworkChecked(qualityLevel, speed, null)
        }

        fun isBufferEnough(): Boolean {
            val contentLength = dataSource.contentLength ?: return false
            val avgBitrate = (contentLength * 8f / (durationUs / 1000 / 1000)).toInt()
            // 至少要加载2秒的缓存
            if (dataQueue.getBufferSize() * 8 < avgBitrate * 2) {
                return false
            }
            val needDataLength = avgBitrate * cacheDuration
            val bufferSize = dataQueue.getBufferSize() * 8
            val willBufferSizeInTime = currNetSpeed * cacheDuration
//            TekiLog.d(
//                TAG,
//                "isBufferEnough needDataLength=$needDataLength, bufferSize=$bufferSize, willBufferSize=$willBufferSizeInTime"
//            )
            return willBufferSizeInTime + bufferSize >= needDataLength
        }

    }

    private fun doOnPlayerThread(block: () -> Unit) {
        handler.post(block)
    }

    override fun onReadyRead() {
    }

    override fun onEndEncountered() {
    }

    override fun onErrorOccurred(throwable: Throwable) {
//        Log.e("main", "onErrorOccurred", throwable.cause)
        // 把错误抛出去，此回调运行在Loader线程，Loader会进行catch包装分发到[onLoadError]
        throw throwable.cause ?: throwable
    }

    override fun getPositionUs(): Long {
        return engine.getPositionUs()
    }

    override fun reportBuffering() {
        reporter.onBuffering()
    }

    override fun reportError(throwable: Throwable, recoverable: Boolean) {
        var extra:Bundle? = if (null != cipherKey && null != cipherIv) {
            Bundle().apply {
                putString(CIPHER_KEY, cipherKey)
                putString(CIPHER_IV, cipherIv)
                putBoolean(CIPHER_IS_REAL_TIME_DECRYPT, isRealTimeDecrypt)
            }
        } else null
        reporter.onError(throwable, recoverable, extra)
    }

    override fun reportPlaying() {
        reporter.onPlaying(Util.usToMs(durationUs))
    }

    override fun reportFinish() {
        reporter.onPlayEnd(Util.usToMs(getPositionUs()), Util.usToMs(durationUs))
    }

    override fun reportTransactionEnd() {
        reporter.onTransactionEnd()
    }

    override fun reportForceStop() {
        reporter.onForceStop()
    }

    // 以下是播放引擎回调，运行在PlayController线程，跟AudioProgram一致

    override fun onPlaybackEnd(zeroData:Boolean) {
        if (!this.playbackEnd) {
            TekiLog.i(TAG, "$uuid: onPlaybackEnd, uri=$uri")
            this.playbackEnd = true
            if (zeroData) {
                programListener?.onPlayZero(this)
            }
            programListener?.onPlaybackCompletion(this)
            (dataSource as CacheableDataSource).releaseCache()
        }
    }

    override fun onPlaybackFirstFrameRender() {
        TekiLog.i(
            TAG,
            "$uuid: first frame cost ${System.currentTimeMillis() - playStartTime + readyCostTime} ms"
        )
        programListener?.onPlaybackFirstFrameRender(this)
    }

    override fun onPlaybackPlayed() {
        TekiLog.i(TAG, "$uuid: onPlaybackPlayed on Engine, uri=$uri")
    }

    override fun onPlaybackPaused() {
        TekiLog.i(TAG, "$uuid: onPlaybackPaused on Engine, uri=$uri")
    }

    override fun onPlaybackFlush() {
        TekiLog.i(TAG, "$uuid: onPlaybackFlush on Engine, uri=$uri")
    }

    override fun onPlaybackStopped() {
        TekiLog.i(TAG, "$uuid: onPlaybackStopped on Engine, uri=$uri")
    }

    override fun onPlaybackResumed() {
        TekiLog.i(TAG, "$uuid: onPlaybackResumed on Engine, uri=$uri")
    }

    override fun onPlaybackException(
        exception: Exception,
        recoverable: Boolean
    ) {
        TekiLog.w(TAG, "$uuid: onPlaybackException on Engine, uri=$uri")
        if (!recoverable) {
            lastFatalError = exception
            programListener?.onPlaybackFailed(this, exception, recoverable)
        } else {
            this.reportError(exception, recoverable)
        }
    }

    override fun onNeedMoreData() {
        if (loadFinished.get()) {
            return
        }
        if (emptyData.get() != NON_EMPTY) {
            return
        }
        TekiLog.i(TAG, "onNeedMoreData(), requestControl.resetIndex()")
        requestControl.resetIndex()
        bandwidthSampler.endSample()

        val bufferSize = dataQueue.getBufferSize()
        TekiLog.i(TAG, "$uuid: 缓存为空，暂停播放, cacheSize=$bufferSize loading=${loader.isLoading}")
        emptyData.set(EMPTY_NO_MORE_DATA)
        reporter.bufferingStatus = REPORT_STATUS_EMPTY
        programListener?.onNeedMoreData(this, false)
        programListener?.onBufferSizeStateChange(
            this,
            DataLevel.EMPTY,
            bufferSize,
            false
        )

        if (!loader.isLoading) {
            continueLoading()
        }
    }

    override fun onShouldContinueFeedData() {
        if (!loader.isLoading && !loadFinished.get()) {
            val bufferSize = dataQueue.getBufferSize()
            TekiLog.i(
                TAG,
                "$uuid: 缓存不足，恢复下载, cacheSize=${bufferSize} seeking=${seekPositionUs > 0} emptyData=$emptyData"
            )
            programListener?.onBufferSizeStateChange(
                this,
                DataLevel.LOW,
                bufferSize,
                false
            )
            continueLoading()
        }
    }

    override fun toString(): String {
        return "[program:$uuid]"
    }
}