package com.lizhi.component.tekiplayer.engine

import android.media.AudioAttributes
import android.media.AudioFormat
import android.media.AudioTrack
import android.media.MediaFormat
import android.os.ConditionVariable
import com.lizhi.component.tekiplayer.util.TekiLog
import com.lizhi.component.tekiplayer.util.Util
import java.nio.ByteBuffer
import java.nio.ByteOrder
import kotlin.math.max
import kotlin.math.roundToInt

/**
 * 文件名：DefaultAudioSink
 * 作用：
 * 作者：<EMAIL>
 * 创建日期：2021/3/16
 */
class DefaultAudioSink(override val bufferEndListener: (zeroData:Boolean) -> Unit) : AudioSink {

    companion object {
        const val TAG = "DefaultAudioSink"
        const val ERROR_NATIVE_DEAD_OBJECT = -32
    }

    private var volume: Float = 1.0F
    private var audioTrack: AudioTrack? = null
    private var sonic: Sonic? = null

    private var channels: Int = 2
    private var sampleRate: Int = 0
    private var bufferSize = 0

    private var attributes: AudioAttributes? = null
    private var audioFormat: AudioFormat? = null
    private var outFormat: MediaFormat? = null
    private var throwOnDeadObjectError = false

    private var writtenBytes = 0L
    private var isPlaying = false
    private var releaseFlag = false
    private val releaseCondition = ConditionVariable(true)
    private var timeline: Timeline? = null

    override fun configure(
        sampleRate: Int,
        channels: Int,
        speed: Float,
        volume: Float
    ) {
        this.sampleRate = sampleRate
        this.channels = channels
        this.volume = volume
        val channelMask = getChannelMask(channels)

        attributes = AudioAttributes.Builder()
            .setUsage(AudioAttributes.USAGE_MEDIA)
            .setFlags(0)
            .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
            .build()

        audioFormat = AudioFormat.Builder()
            .setEncoding(AudioFormat.ENCODING_PCM_16BIT)
            .setSampleRate(sampleRate)
            .setChannelMask(channelMask)
            .build()

        val minBufferSize =
            getPcmDefaultBufferSize(
                sampleRate,
                channelMask,
                AudioFormat.ENCODING_PCM_16BIT,
                speed
            )
//        val minBufferSize = AudioTrack.getMinBufferSize(sampleRate, channelMask, AudioFormat.ENCODING_PCM_16BIT)
        this.bufferSize = minBufferSize
        while (bufferSize % 2 == 1) {
            bufferSize += 1
        }

        if (speed != 1F) {
            sonic = Sonic(sampleRate, channels, speed, 1F, sampleRate)
        }

        audioTrack = buildAudioTrack()
        synchronized(this) {
            if (!releaseFlag) {
                // config 方法可能多次调用，所以非空时无须重新创建
                if (timeline == null) {
                    timeline = DefaultTimeline(sampleRate, channels, speed)
                }
                timeline?.setBufferEndFlowListener(bufferEndListener)
            } else {
                release()
            }
        }
    }

    private fun getPcmDefaultBufferSize(
        outputSampleRate: Int,
        outputChannelConfig: Int,
        outputEncoding: Int,
        maxAudioTrackPlaybackSpeed: Float
    ): Int {
        val minBufferSize =
            AudioTrack.getMinBufferSize(outputSampleRate, outputChannelConfig, outputEncoding)
        val multipliedBufferSize: Int = minBufferSize * 2
        val minAppBufferSize: Int =
            durationUsToFrames(150_000).toInt() * channels * 2
        val maxAppBufferSize = max(
            minBufferSize,
            durationUsToFrames(750_000).toInt() * channels * 2
        )
        var bufferSize: Int =
            Util.constrainValue(multipliedBufferSize, minAppBufferSize, maxAppBufferSize)
        if (maxAudioTrackPlaybackSpeed != 1f) {
            // Maintain the buffer duration by scaling the size accordingly.
            bufferSize = (bufferSize * maxAudioTrackPlaybackSpeed).roundToInt()
        }
        return bufferSize
    }

    fun durationUsToFrames(durationUs: Long): Long {
        return durationUs * sampleRate / Util.MICROS_PER_SECOND
    }

    private fun getChannelMask(mask: Int): Int {
        return when (mask) {
            1 -> AudioFormat.CHANNEL_OUT_MONO
            2 -> AudioFormat.CHANNEL_OUT_STEREO
            else -> AudioFormat.CHANNEL_OUT_STEREO
        }
    }


    override fun play() {
        synchronized(this) {
            isPlaying = true
            audioTrack?.play()
        }
    }

    override fun pause() {
        synchronized(this) {
            if (isPlaying) {
                isPlaying = false
                audioTrack?.pause()
                timeline?.pause()
            }
        }
    }

    override fun getCurrentPositionUs(): Long {
        // 每秒PCM大小： 采样率 * 采样字节数（16 / 8）* 声道数
//        return (writtenBytes / (sampleRate * channels * 2.0f) * 1000 * 1000).toLong()
        return timeline?.getPositionUs() ?: 0
    }

    override fun resetPositionUs(positionUs: Long): Boolean {
        return timeline?.seek(positionUs) ?: false
    }

    override fun setVolume(volume: Float) {
        if (volume >= 0 && volume <= 1.0F) {
            this.volume = volume
            audioTrack?.setVolume(volume)
        }
    }

    override fun flush() {
        synchronized(this) {
            TekiLog.i(TAG, "flush on AudioTrack")
            audioTrack?.pause()
            timeline?.pause()
            val audioTrack = audioTrack ?: return
            this.audioTrack = null
            Thread {
                releaseCondition.close()
                kotlin.runCatching {
                    audioTrack.flush()
                    audioTrack.release()
                }
                releaseCondition.open()
            }.start()
        }
    }

    override fun playToEnd(zeroData: Boolean) {
        audioTrack?.stop()
        if (this.timeline == null) {
            bufferEndListener(zeroData)
        } else {
            this.timeline?.playToEnd(zeroData)
        }
    }

    override fun setOutputFormat(format: MediaFormat) {
        this.outFormat = format
    }

    override fun handleBuffer(outputBuffer: ByteBuffer, timeUs: Long): Boolean {
        val audioTrack = mapReInitialAudioTrack()

        val audioData: ByteBuffer = sonic?.let {
            val buffer = outputBuffer.asShortBuffer()
            it.queueInput(buffer)
            val audioData = ByteBuffer.allocate(it.outputSize).order(ByteOrder.nativeOrder())
            it.getOutput(audioData.asShortBuffer())
            audioData
        } ?: outputBuffer

        val remaining = outputBuffer.remaining()

        val write: Int =
            synchronized(this) {
                if (releaseFlag) {
                    release()
                    return false
                }
                timeline?.addBuffer(remaining.toLong(), timeUs, isPlaying)
                audioTrack.write(audioData, audioData.remaining(), AudioTrack.WRITE_BLOCKING)
            }
//        TekiLog.d(TAG, "handleBuffer $remaining")
//        when {
//            write > 0 -> {
//                throwOnDeadObjectError = false
//            }
//            write == AudioTrack.ERROR_DEAD_OBJECT || write == ERROR_NATIVE_DEAD_OBJECT -> {
//                TekiLog.e(TAG, "audioTrack.write when ERROR_DEAD_OBJECT, try release and recreate")
//                release()
//                // 第二次
//                if (throwOnDeadObjectError) {
//                    throw EngineException()
//                }
//                throwOnDeadObjectError = true
//                handleBuffer(outputBuffer)
//            }
//            else -> {
////                throw EngineException()
//            }
//        }
        return write > 0
    }

    private fun mapReInitialAudioTrack(): AudioTrack {
        var audioTrack = audioTrack
        return if (audioTrack != null) {
            audioTrack
        } else {
            releaseCondition.block()
            audioTrack = buildAudioTrack()
            if (isPlaying) {
                audioTrack.play()
            }
            this.audioTrack = audioTrack
            audioTrack
        }
    }

    private fun buildAudioTrack(): AudioTrack {
        TekiLog.i(
            TAG,
            "create audioTrack, attributes=$attributes, audioFormat=$audioFormat, bufferSize=$bufferSize"
        )
        val audioTrack = AudioTrack(
            attributes,
            audioFormat,
            bufferSize,
            AudioTrack.MODE_STREAM,
            0
        )
        audioTrack.setVolume(volume)
        return audioTrack
    }

    override fun release() {
        synchronized(this) {
            TekiLog.i(TAG, "release AudioSink")
            releaseFlag = true
            val track = audioTrack ?: return
            audioTrack = null
            track.flush()
            track.release()
            timeline?.stop()
        }
    }

    override fun setSpeed(speed: Float) {
        sonic = Sonic(sampleRate, channels, speed, 1F, sampleRate)
        timeline?.setSpeed(speed)
    }
}