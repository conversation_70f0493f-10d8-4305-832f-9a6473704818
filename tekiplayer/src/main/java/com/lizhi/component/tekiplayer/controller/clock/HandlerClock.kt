package com.lizhi.component.tekiplayer.controller.clock

import android.os.Handler
import android.os.Looper
import android.os.SystemClock
import com.lizhi.component.tekiplayer.controller.Controller
import com.lizhi.component.tekiplayer.util.TekiLog
import com.lizhi.component.tekiplayer.util.countDown
import com.lizhi.component.tekiplayer.util.stopCountDown

class HandlerClock(
    looper: Looper,
    private val controller: Controller
) : Clock {

    companion object {
        const val TAG = "HandlerClock"
    }

    private val handler = Handler(looper)

    private var cancel = false
    private var remainPlaybackCount: Int? = null
    private var stopTime: Long? = null

    override var stoped: Boolean = false

    override var onItemRemainingUpdate: (count: Int) -> Unit = {}

    override var onTimeRemainingUpdate: (duration: Long) -> Unit = {}

    override fun stopAtTime(time: Long) {
        stopAfterTime(time - System.currentTimeMillis())
    }

    override fun stopAfterTime(time: Long) {
        cancelStop()
        cancel = false
        stopTime = SystemClock.elapsedRealtime() + time
        val startTime = SystemClock.elapsedRealtime()
        // 开始回调
        handler.countDown {
            if (SystemClock.elapsedRealtime() - startTime >= time) {
                controller.pause()
                TekiLog.i(TAG, "stop clock trigger")
                cancelStop()
            }
            onTimeRemainingUpdate(getRemainingTimeBeforeStop())
            1000L
        }
    }

    override fun stopAfterItem(count: Int) {
        if (count <= 0) {
            return
        }
        cancelStop()
        cancel = false
        remainPlaybackCount = count
        // 设置节目暂停时，先回调一次
        onItemRemainingUpdate(count)
    }

    override fun getRemainingTimeBeforeStop(): Long {
        val willStopTime = stopTime ?: return 0
        val left = willStopTime - SystemClock.elapsedRealtime()
        return if (left > 0) left else 0
    }

    override fun getRemainingItemBeforeStop(): Int {
        return remainPlaybackCount ?: 0
    }

    override fun addFinishedItem() {
        var remaining = remainPlaybackCount ?: return
        remaining--
        remainPlaybackCount = remaining
        //
        onItemRemainingUpdate(getRemainingItemBeforeStop())
        //
        if (remaining == 0) {
            stoped = true
        }
    }

    override fun cancelStop() {
        cancel = true
        stoped = false
        remainPlaybackCount = null
        stopTime = null
        // 停止回调
        handler.stopCountDown()
    }
}