package com.lizhi.component.tekiplayer.process

import android.content.Context
import com.lizhi.component.tekiplayer.*
import com.lizhi.component.tekiplayer.controller.PlayerState
import com.lizhi.component.tekiplayer.controller.dns.DnsResolver
import com.lizhi.component.tekiplayer.util.TekiLog

/**
 * 文件名：PlayerStub
 * 作用：
 * 作者：luo<PERSON><PERSON><EMAIL>
 * 创建日期：2021/4/6
 */
class PlayerStub(
    private val context: Context,
    private val listener: PlayEventListener
) : IPlayer.Stub() {

    private lateinit var player: TekiPlayer

    var internalDnsResolver: DnsResolver? = null

    companion object {
        private const val TAG = "PlayerStub"
    }

    override fun getCurrentMediaItem(): MediaItem? {
        return player.getCurrentMediaItem()
    }

    override fun removePlayListEventListener(var1: IPlayEventListener?) {
    }

    override fun getDuration(): Long {
        return player.getDuration()
    }

    override fun getVolume(): Float {
        return player.volume
    }

    override fun play() {
        return player.play()
    }

    override fun removeItemAt(index: Int) {
        return player.removeItemAt(index)
    }

    override fun setVolume(volume: Float) {
        player.volume = volume
    }

    override fun getMediaItem(index: Int): MediaItem? {
        return player.getMediaItem(index)
    }

    override fun stopAtTime(absoluteTimestamp: Long) {
        player.stopAtTime(absoluteTimestamp)
    }

    override fun resume() {
        player.resume()
    }

    override fun seekToIndex(index: Int, percent: Float) {
        player.seekTo(index, percent)
    }

    override fun addPlayListEventListener(var1: IPlayEventListener?) {
        TekiLog.d(TAG, "set player process listener $var1")
        player.addPlayEventListener(object : PlayEventListener {
            override fun onPlaybackStateChange(status: Int) {
                var1?.onPlaybackStateChange(status)
            }

            override fun onError(errCode: Int, message: String) {
                var1?.onError(errCode, message)
            }

            override fun onPlaybackRemoveOnList() {
                var1?.onPlaybackRemoveOnList()
            }

            override fun onPlayListUpdate() {
                var1?.onPlayListUpdate()
            }

            override fun onPlaybackChange(
                index: Int,
                item: MediaItem?,
                lastPosition: Long,
                reason: Int
            ) {
                var1?.onPlaybackChange(index, item, lastPosition, reason)
            }

            override fun onPlayedPositionUpdate(index: Int, item: MediaItem?, position: Long) {
                var1?.onPlayedPositionUpdate(index, item, position)
            }

            override fun onPlayZeroItem(item: MediaItem?) {
                var1?.onPlayZeroItem(item)
            }

            override fun onBufferedPositionUpdate(
                index: Int,
                item: MediaItem?,
                bufferPosition: Long
            ) {
                var1?.onBufferedPositionUpdate(index, item, bufferPosition)
            }

            override fun onItemRemainingUpdate(index: Int, item: MediaItem?, remainingItem: Int) {
                var1?.onItemRemainingUpdate(index, item, remainingItem)
            }

            override fun onTimeRemainingUpdate(index: Int, item: MediaItem?, remainingMs: Long) {
                var1?.onTimeRemainingUpdate(index, item, remainingMs)
            }

            override fun onPlayListFinished() {
                var1?.onPlayListFinished()
            }
        })
        player.addNetworkQualityWatcher { qualityLevel, speedBps ->
            var1?.onNetworkQualitySample(qualityLevel, speedBps)
        }
        if (player.getStatus() != PlayerState.STATE_IDLE) {
            var1?.onPlaybackStateChange(player.getStatus())
            var1?.onPlaybackChange(
                player.getCurrentIndexOnList(),
                player.getCurrentMediaItem(),
                -1,
                5
            )
        }
    }

    override fun hasNext(): Boolean {
        return player.hasNext()
    }

    override fun getActualQuality(): Int {
        return player.getActualAudioQuality().value
    }

    override fun getSpeed(): Float {
        return player.speed
    }

    override fun getStatus(): Int {
        return player.getStatus()
    }

    override fun getRepeatMode(): Int {
        return player.repeatMode
    }

    override fun addMediaItemListAt(
        index: Int,
        list: MutableList<MediaItem>?
    ) {
        list?.let {
            player.addMediaItem(index, list)
        }
    }

    override fun stop() {
        player.stop()
    }

    override fun playNext() {
        player.playNext()
    }

    override fun getBufferedPosition(): Long {
        return player.getBufferedPosition()
    }

    override fun getItemRemainingBeforeStop(): Int {
        return player.getItemRemainingBeforeStop()
    }

    override fun getMediaItemList(): List<MediaItem> {
        return player.getMediaItemList()
    }

    override fun clear() {
        player.clear()
    }

    override fun getTimeRemainingBeforeStop(): Long {
        return player.getTimeRemainingBeforeStop()
    }

    override fun hasPrevious(): Boolean {
        return player.hasPrevious()
    }

    override fun seekTo(positionMs: Long) {
        player.seekTo(positionMs)
    }

    override fun playAt(index: Int) {
        player.play(index)
    }

    override fun cancelStop() {
        player.cancelStop()
    }

    override fun stopAfterTime(timeMs: Long) {
        player.stopAfterTime(timeMs)
    }

    override fun addMediaItem(mediaItem: MediaItem) {
        player.addMediaItem(mediaItem)
    }

    override fun setRepeatMode(repeatMode: Int) {
        player.repeatMode = repeatMode
    }

    override fun stopAfterMediaItemFinish(itemCount: Int) {
        player.stopAfterMediaItemFinish(itemCount)
    }

    override fun getCurrentIndexOnList(): Int {
        return player.getCurrentIndexOnList()
    }

    override fun removeRange(
        index: Int,
        length: Int
    ) {
        player.removeRange(index, length)
    }

    override fun getAudioQuality(): Int {
        return player.getAudioQuality().value
    }

    override fun setAudioQuality(value: Int) {
        val quality = when (value) {
            Player.Quality.LOSSLESS.value -> Player.Quality.LOSSLESS
            Player.Quality.SUPER.value -> Player.Quality.SUPER
            Player.Quality.HIGH.value -> Player.Quality.HIGH
            Player.Quality.LOW.value -> Player.Quality.LOW
            else -> Player.Quality.HIGH
        }
        player.setAudioQuality(quality)
    }

    override fun addMediaItemAt(
        index: Int,
        mediaItem: MediaItem?
    ) {
        mediaItem ?: return
        player.addMediaItem(index, mediaItem)
    }

    override fun setCdnList(cdnList: MutableList<String>?) {
        cdnList ?: return
        player.setCdnList(cdnList)
    }

    override fun prepare() {
        player.prepare()
    }

    override fun removeItem(mediaItem: MediaItem?) {
        mediaItem ?: return
        player.removeItem(mediaItem)
    }

    override fun pause() {
        player.pause()
    }

    override fun getPosition(): Long {
        return player.getPosition()
    }

    override fun playPrevious() {
        player.playPrevious()
    }

    override fun addMediaItemList(list: MutableList<MediaItem>?) {
        list ?: return
        player.addMediaItem(list)
    }


    override fun updateConfiguration(
        recordPosition: Boolean,
        preBufferSize: Long,
        autoPrepareMediaCount: Int,
        maxBufferSizeOnWifi: Long,
        maxBufferSizeOn5G: Long,
        maxBufferSizeOn4G: Long,
        maxBufferSizeOn3G: Long,
        maxBufferSizeOn2G: Long,
        shouldLoadingThresholds: Long,
        cdnUrlPattern: String,
        cacheDuration: Int
    ) {
        player = TekiPlayer.Builder(context)
            .recordPlaybackPosition(recordPosition)
            .setShouldLoadingThresholds(shouldLoadingThresholds)
            .setMaxBufferSizeOnWifi(maxBufferSizeOnWifi)
            .setMaxBufferSizeOn2G(maxBufferSizeOn2G)
            .setMaxBufferSizeOn3G(maxBufferSizeOn3G)
            .setMaxBufferSizeOn4G(maxBufferSizeOn4G)
            .setMaxBufferSizeOn5G(maxBufferSizeOn5G)
            .setAutoPrepareMediaCount(autoPrepareMediaCount)
            .setCdnUrlPattern(cdnUrlPattern)
            .setCacheDuration(cacheDuration)
            .apply {
                <EMAIL>?.let {
                    setDnsResolver(it)
                }
            }
            .build()
            .apply {
                addPlayEventListener(listener)
            }
    }

    override fun seekBy(relativeMs: Long) {
        player.seekBy(relativeMs)
    }

    override fun setSpeed(speed: Float) {
        player.speed = speed
    }

}