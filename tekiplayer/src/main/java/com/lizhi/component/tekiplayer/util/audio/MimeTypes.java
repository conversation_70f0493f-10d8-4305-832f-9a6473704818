/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.lizhi.component.tekiplayer.util.audio;

import androidx.annotation.Nullable;

import java.util.regex.Pattern;

/**
 * Defines common MIME types and helper methods.
 */
public final class MimeTypes {

    public static final String BASE_TYPE_VIDEO = "video";
    public static final String BASE_TYPE_AUDIO = "audio";
    public static final String BASE_TYPE_TEXT = "text";
    public static final String BASE_TYPE_IMAGE = "image";
    public static final String BASE_TYPE_APPLICATION = "application";

    public static final String VIDEO_MP4 = BASE_TYPE_VIDEO + "/mp4";
    public static final String VIDEO_MATROSKA = BASE_TYPE_VIDEO + "/x-matroska";
    public static final String VIDEO_WEBM = BASE_TYPE_VIDEO + "/webm";
    public static final String VIDEO_H263 = BASE_TYPE_VIDEO + "/3gpp";
    public static final String VIDEO_H264 = BASE_TYPE_VIDEO + "/avc";
    public static final String VIDEO_H265 = BASE_TYPE_VIDEO + "/hevc";
    public static final String VIDEO_VP8 = BASE_TYPE_VIDEO + "/x-vnd.on2.vp8";
    public static final String VIDEO_VP9 = BASE_TYPE_VIDEO + "/x-vnd.on2.vp9";
    public static final String VIDEO_AV1 = BASE_TYPE_VIDEO + "/av01";
    public static final String VIDEO_MP2T = BASE_TYPE_VIDEO + "/mp2t";
    public static final String VIDEO_MP4V = BASE_TYPE_VIDEO + "/mp4v-es";
    public static final String VIDEO_MPEG = BASE_TYPE_VIDEO + "/mpeg";
    public static final String VIDEO_PS = BASE_TYPE_VIDEO + "/mp2p";
    public static final String VIDEO_MPEG2 = BASE_TYPE_VIDEO + "/mpeg2";
    public static final String VIDEO_VC1 = BASE_TYPE_VIDEO + "/wvc1";
    public static final String VIDEO_DIVX = BASE_TYPE_VIDEO + "/divx";
    public static final String VIDEO_FLV = BASE_TYPE_VIDEO + "/x-flv";
    public static final String VIDEO_DOLBY_VISION = BASE_TYPE_VIDEO + "/dolby-vision";
    public static final String VIDEO_OGG = BASE_TYPE_VIDEO + "/ogg";
    public static final String VIDEO_UNKNOWN = BASE_TYPE_VIDEO + "/x-unknown";

    public static final String AUDIO_MP4 = BASE_TYPE_AUDIO + "/mp4";
    public static final String AUDIO_AAC = BASE_TYPE_AUDIO + "/mp4a-latm";
    public static final String AUDIO_MATROSKA = BASE_TYPE_AUDIO + "/x-matroska";
    public static final String AUDIO_WEBM = BASE_TYPE_AUDIO + "/webm";
    public static final String AUDIO_MPEG = BASE_TYPE_AUDIO + "/mpeg";
    public static final String AUDIO_MPEG_L1 = BASE_TYPE_AUDIO + "/mpeg-L1";
    public static final String AUDIO_MPEG_L2 = BASE_TYPE_AUDIO + "/mpeg-L2";
    public static final String AUDIO_RAW = BASE_TYPE_AUDIO + "/raw";
    public static final String AUDIO_ALAW = BASE_TYPE_AUDIO + "/g711-alaw";
    public static final String AUDIO_MLAW = BASE_TYPE_AUDIO + "/g711-mlaw";
    public static final String AUDIO_AC3 = BASE_TYPE_AUDIO + "/ac3";
    public static final String AUDIO_E_AC3 = BASE_TYPE_AUDIO + "/eac3";
    public static final String AUDIO_E_AC3_JOC = BASE_TYPE_AUDIO + "/eac3-joc";
    public static final String AUDIO_AC4 = BASE_TYPE_AUDIO + "/ac4";
    public static final String AUDIO_TRUEHD = BASE_TYPE_AUDIO + "/true-hd";
    public static final String AUDIO_DTS = BASE_TYPE_AUDIO + "/vnd.dts";
    public static final String AUDIO_DTS_HD = BASE_TYPE_AUDIO + "/vnd.dts.hd";
    public static final String AUDIO_DTS_EXPRESS = BASE_TYPE_AUDIO + "/vnd.dts.hd;profile=lbr";
    public static final String AUDIO_VORBIS = BASE_TYPE_AUDIO + "/vorbis";
    public static final String AUDIO_OPUS = BASE_TYPE_AUDIO + "/opus";
    public static final String AUDIO_AMR = BASE_TYPE_AUDIO + "/amr";
    public static final String AUDIO_AMR_NB = BASE_TYPE_AUDIO + "/3gpp";
    public static final String AUDIO_AMR_WB = BASE_TYPE_AUDIO + "/amr-wb";
    public static final String AUDIO_FLAC = BASE_TYPE_AUDIO + "/flac";
    public static final String AUDIO_ALAC = BASE_TYPE_AUDIO + "/alac";
    public static final String AUDIO_MSGSM = BASE_TYPE_AUDIO + "/gsm";
    public static final String AUDIO_OGG = BASE_TYPE_AUDIO + "/ogg";
    public static final String AUDIO_WAV = BASE_TYPE_AUDIO + "/wav";
    public static final String AUDIO_UNKNOWN = BASE_TYPE_AUDIO + "/x-unknown";

    public static final String TEXT_VTT = BASE_TYPE_TEXT + "/vtt";
    public static final String TEXT_SSA = BASE_TYPE_TEXT + "/x-ssa";

    public static final String APPLICATION_MP4 = BASE_TYPE_APPLICATION + "/mp4";
    public static final String APPLICATION_WEBM = BASE_TYPE_APPLICATION + "/webm";
    public static final String APPLICATION_MATROSKA = BASE_TYPE_APPLICATION + "/x-matroska";
    public static final String APPLICATION_MPD = BASE_TYPE_APPLICATION + "/dash+xml";
    public static final String APPLICATION_M3U8 = BASE_TYPE_APPLICATION + "/x-mpegURL";
    public static final String APPLICATION_SS = BASE_TYPE_APPLICATION + "/vnd.ms-sstr+xml";
    public static final String APPLICATION_ID3 = BASE_TYPE_APPLICATION + "/id3";
    public static final String APPLICATION_CEA608 = BASE_TYPE_APPLICATION + "/cea-608";
    public static final String APPLICATION_CEA708 = BASE_TYPE_APPLICATION + "/cea-708";
    public static final String APPLICATION_SUBRIP = BASE_TYPE_APPLICATION + "/x-subrip";
    public static final String APPLICATION_TTML = BASE_TYPE_APPLICATION + "/ttml+xml";
    public static final String APPLICATION_TX3G = BASE_TYPE_APPLICATION + "/x-quicktime-tx3g";
    public static final String APPLICATION_MP4VTT = BASE_TYPE_APPLICATION + "/x-mp4-vtt";
    public static final String APPLICATION_MP4CEA608 = BASE_TYPE_APPLICATION + "/x-mp4-cea-608";
    public static final String APPLICATION_RAWCC = BASE_TYPE_APPLICATION + "/x-rawcc";
    public static final String APPLICATION_VOBSUB = BASE_TYPE_APPLICATION + "/vobsub";
    public static final String APPLICATION_PGS = BASE_TYPE_APPLICATION + "/pgs";
    public static final String APPLICATION_SCTE35 = BASE_TYPE_APPLICATION + "/x-scte35";
    public static final String APPLICATION_CAMERA_MOTION = BASE_TYPE_APPLICATION + "/x-camera-motion";
    public static final String APPLICATION_EMSG = BASE_TYPE_APPLICATION + "/x-emsg";
    public static final String APPLICATION_DVBSUBS = BASE_TYPE_APPLICATION + "/dvbsubs";
    public static final String APPLICATION_EXIF = BASE_TYPE_APPLICATION + "/x-exif";
    public static final String APPLICATION_ICY = BASE_TYPE_APPLICATION + "/x-icy";
    public static final String APPLICATION_AIT = BASE_TYPE_APPLICATION + "/vnd.dvb.ait";

    public static final String IMAGE_JPEG = BASE_TYPE_IMAGE + "/jpeg";

    private static final Pattern MP4A_RFC_6381_CODEC_PATTERN =
        Pattern.compile("^mp4a\\.([a-zA-Z0-9]{2})(?:\\.([0-9]{1,2}))?$");


    /**
     * Returns the MIME type corresponding to an MP4 object type identifier, as defined in RFC 6381
     * and https://mp4ra.org/#/object_types.
     *
     * @param objectType An MP4 object type identifier.
     * @return The corresponding MIME type, or {@code null} if it could not be determined.
     */
    @Nullable
    public static String getMimeTypeFromMp4ObjectType(int objectType) {
        switch (objectType) {
            case 0x20:
                return MimeTypes.VIDEO_MP4V;
            case 0x21:
                return MimeTypes.VIDEO_H264;
            case 0x23:
                return MimeTypes.VIDEO_H265;
            case 0x60:
            case 0x61:
            case 0x62:
            case 0x63:
            case 0x64:
            case 0x65:
                return MimeTypes.VIDEO_MPEG2;
            case 0x6A:
                return MimeTypes.VIDEO_MPEG;
            case 0x69:
            case 0x6B:
                return MimeTypes.AUDIO_MPEG;
            case 0xA3:
                return MimeTypes.VIDEO_VC1;
            case 0xB1:
                return MimeTypes.VIDEO_VP9;
            case 0x40:
            case 0x66:
            case 0x67:
            case 0x68:
                return MimeTypes.AUDIO_AAC;
            case 0xA5:
                return MimeTypes.AUDIO_AC3;
            case 0xA6:
                return MimeTypes.AUDIO_E_AC3;
            case 0xA9:
            case 0xAC:
                return MimeTypes.AUDIO_DTS;
            case 0xAA:
            case 0xAB:
                return MimeTypes.AUDIO_DTS_HD;
            case 0xAD:
                return MimeTypes.AUDIO_OPUS;
            case 0xAE:
                return MimeTypes.AUDIO_AC4;
            default:
                return null;
        }
    }
}
