// IPlayListEventListener.aidl
package com.lizhi.component.tekiplayer.controller.dns;

// Declare any non-default types here with import statements
import com.lizhi.component.tekiplayer.controller.dns.InetAddressWrapper;

interface IDnsResolver {

    // 域名解析
    List<InetAddressWrapper> lookup(String hostname);

    // 反馈结果
    void mark(
        String domain,
        String ip,
        boolean connResult,
        boolean requestResult,
        String path,
        long connCost,
        long requestCost
    );

    boolean isEnableCustomDns();
}