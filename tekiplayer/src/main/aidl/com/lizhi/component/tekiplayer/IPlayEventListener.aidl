// IPlayListEventListener.aidl
package com.lizhi.component.tekiplayer;

// Declare any non-default types here with import statements
import com.lizhi.component.tekiplayer.MediaItem;

interface IPlayEventListener {

    void onPlaybackStateChange(int status);


    void onError(int errCode, String message);
  
      /**
       * 当前播放的节目被移除
       */
    void onPlaybackRemoveOnList();
  
      /**
       * 播放列表变化
       */
    void onPlayListUpdate();
  
    /**
     * 播放的歌曲变化
     *
     * @param index 第几首
     * @param item 当前歌曲
     * @param reason 上一首是否播放完成
     */
    void onPlaybackChange(
          int index,
          in MediaItem item,
          long lastPosition,
          int reason
      );

    void onNetworkQualitySample(int quality, int speedBps);

    void onPlayedPositionUpdate(int index, in MediaItem item, long position);

    void onBufferedPositionUpdate(int index, in MediaItem item, long bufferPosition);

    void onTimeRemainingUpdate(int index, in MediaItem item, long remainingMs);

    void onItemRemainingUpdate(int index, in MediaItem item, int remainingItem);

    void onPlayListFinished();

    void onPlayZeroItem(in MediaItem item);
}