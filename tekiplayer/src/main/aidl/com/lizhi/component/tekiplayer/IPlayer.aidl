// IPlayer.aidl
package com.lizhi.component.tekiplayer;

// Declare any non-default types here with import statements
import com.lizhi.component.tekiplayer.MediaItem;
import com.lizhi.component.tekiplayer.IPlayEventListener;
import com.lizhi.component.tekiplayer.controller.dns.IDnsResolver;

interface IPlayer {
    
   int getRepeatMode();

   void setRepeatMode(int repeatMode);

   float getSpeed();

   void setSpeed(float speed);

   float getVolume();

   void setVolume(float volume);

   void addMediaItem(in MediaItem mediaItem);

   void addMediaItemAt(int index, in MediaItem mediaItem);

   void addMediaItemList(in List<MediaItem> list);

   void addMediaItemListAt(int index, in List<MediaItem> list);

   void removeRange(int index, int length);

   void removeItem(in MediaItem mediaItem);

   void removeItemAt(int index);

   MediaItem getMediaItem(int index);

   List<MediaItem> getMediaItemList();

   int getCurrentIndexOnList();

   MediaItem getCurrentMediaItem();

   void clear();

   void prepare();

   void play();

   void playAt(int index);

   void playNext();

   void playPrevious();

   void pause();

   void resume();

   void stop();

   void seekTo(long positionMs);

   void seekBy(long relativeMs);

   long getPosition();

   long getBufferedPosition();

   int getStatus();

   long getDuration();

   boolean hasNext();

   boolean hasPrevious();

   void stopAfterTime(long timeMs);

   void stopAtTime(long absoluteTimestamp);

   void stopAfterMediaItemFinish(int itemCount);

   void cancelStop();

   void addPlayListEventListener(IPlayEventListener var1);

   void removePlayListEventListener(IPlayEventListener var1);

   void updateConfiguration(boolean recordPosition, long preBufferSize, int autoPrepareMediaCount, long maxBufferSizeOnWifi, long maxBufferSizeOn5G, long maxBufferSizeOn4G, long maxBufferSizeOn3G, long maxBufferSizeOn2G, long shouldLoadingThresholds, String cdnUrlPattern, int cacheDuration);

   void setCdnList(in List<String> cdnList);

   void setAudioQuality(int value);

   int getActualQuality();

   int getAudioQuality();

   int getItemRemainingBeforeStop();

   long getTimeRemainingBeforeStop();

   void seekToIndex(int index, float percent);
}