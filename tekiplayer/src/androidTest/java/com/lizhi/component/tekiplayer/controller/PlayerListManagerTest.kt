package com.lizhi.component.tekiplayer.controller

import android.net.Uri
import androidx.test.InstrumentationRegistry
import com.lizhi.component.tekiplayer.MediaItem
import com.lizhi.component.tekiplayer.controller.list.OnPlayListUpdateListener
import com.lizhi.component.tekiplayer.controller.list.PlayerListManager
import com.lizhi.component.tekiplayer.mock.MockProgramFactory
import com.lizhi.component.tekiplayer.util.TekiLog
import org.junit.Assert.assertEquals
import org.junit.Test

/**
 * 文件名：PlayerListManagerTest
 * 作用：
 * 作者：huangtianhao
 * 创建日期：2021/3/30
 */
class PlayerListManagerTest {

    companion object {
        private const val TAG = "PlayerListManagerTest"
    }

    @Test
    fun testAddItem() {
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext
        val manager = PlayerListManager(MockProgramFactory(), 3)
        var playPosition = -1
        var removedCount = 0
        manager.setOnPlayListUpdateListener(
            object : OnPlayListUpdateListener {
                override fun onItemReady(item: AudioProgramHolder) {

                }

                override fun onPlayingItemPreloadFailed() {
                }

                override fun onPlayPositionChanged(
                    position: Int,
                    lastPositionUs: Long,
                    reason: Int
                ) {
                    TekiLog.i(TAG, "onPlayPositionChanged $position")
                    playPosition = position
                }

                override fun onPlayListChanged(reason: Int) {

                }

                override fun onPlayingItemRemoved(reason: Int) {
                    removedCount++
                }
            }
        )

        manager.addMediaItem(createMockMediaItem())
        manager.addMediaItem(createMockMediaItem())

        manager.prepareNextProgram()
        assertEquals(0, playPosition)
        manager.moveToNextProgram()
        assertEquals(1, playPosition)
        manager.clear()
        assertEquals(1, removedCount)
    }

    private fun createMockMediaItem(): MediaItem {
        return MediaItem.Builder().setUri(
            Uri.parse("http://cdn.lizhi.fm/audio/2019/06/23/2744633406271451142_hd.mp3")
        ).setPendingStartPosition(0L).build()
    }
}