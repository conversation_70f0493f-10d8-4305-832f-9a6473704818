package com.lizhi.component.tekiplayer.datasource.impl

import androidx.test.InstrumentationRegistry
import androidx.test.runner.AndroidJUnit4
import com.lizhi.component.tekiplayer.datasource.Range
import com.lizhi.component.tekiplayer.datasource.cache.CacheMmkvStorage
import com.lizhi.component.tekiplayer.datasource.cache.LruCacheEvictor
import com.lizhi.component.tekiplayer.datasource.cache.SimpleCache
import com.lizhi.component.tekiplayer.util.TekiLog
import com.tencent.mmkv.MMKV
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import java.util.concurrent.CountDownLatch
import kotlin.concurrent.thread
import kotlin.random.Random

/**
 * 文件名：DefaultHttpDataSourceTest
 * 作用：CacheDataSource
 * 作者：huangtianhao
 * 创建日期：2021/3/18
 */
@RunWith(AndroidJUnit4::class)
class CacheSourceTest {

    companion object {
        private const val TAG = "CacheSourceTest"
    }

    @Before
    fun setUp() {
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext
        MMKV.initialize(appContext)
    }

    /**
     * 验证使用CacheDataSource读取指定链接的文件，完整读取（从0到contentLength）后，文件大小等于[9267615]
     * 并且第一次完整读取应全部从http读取，第二次完整读取全部从cache中读取
     */
    @Test
    fun testFileReadFileSize() {
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext
        val countDownLatch = CountDownLatch(1)
        val url = "http://cdn.lizhi.fm/audio/2019/06/23/2744633406271451142_hd.mp3"
        val downloadDirectory = appContext.getExternalFilesDir("test")!!
        val simpleCache = SimpleCache(
            downloadDirectory,
            CacheMmkvStorage.getInstance(),
            LruCacheEvictor(
                CacheMmkvStorage.getInstance(),
                downloadDirectory,
                300 * 1024 * 1024,
                300 * 1024 * 1024
            )
        )

        thread {
            val dataSource = CacheDataSource.CacheDataSourceFactory().create {
                setCache(simpleCache)
                setUrl(url)
            }

            dataSource.open()
            val buffer = ByteArray(2048)

            // 从0读取到EOF
            TekiLog.d(TAG, "Should all read from http")
            var realLength = dataSource.read(buffer, 0, 2048)
            while (realLength > 0) {
                realLength = dataSource.read(buffer, 0, 2048)
            }

            TekiLog.d(TAG, "sleeping for 5secs, should all read from cache later")
//            dataSource.close()
//            dataSource.open()
            val dataSource2 = CacheDataSource.CacheDataSourceFactory().create {
                setCache(simpleCache)
                setUrl(url)
            }
            dataSource2.open()
            // 再次从0读取到EOF，应观察到日志这时候应该能够全部从缓存中读取
            realLength = dataSource2.read(buffer, 0, 2048)
            while (realLength > 0) {
                realLength = dataSource2.read(buffer, 0, 2048)
            }

            countDownLatch.countDown()
        }

        countDownLatch.await()
        assertEquals(9267615, simpleCache.file!!.length())
    }

    /**
     * 验证使用CacheDataSource读取指定链接的文件，第一次从中间位置读取，第二次从0读取到末尾。
     * 验证读取后的文件长度为指定值，并且缓存片段信息应该只有1段，头尾值应分别为0和contentLength
     */
    @Test
    fun testReadFromMiddle() {
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext
        val countDownLatch = CountDownLatch(1)
        val url = "http://cdn.lizhi.fm/audio/2019/06/23/2744633406271451142_hd.mp3"
        val downloadDirectory = appContext.getExternalFilesDir("test")!!
        val simpleCache = SimpleCache(
            downloadDirectory, CacheMmkvStorage.getInstance(), LruCacheEvictor(
                CacheMmkvStorage.getInstance(),
                downloadDirectory,
                300 * 1024 * 1024,
                300 * 1024 * 1024
            )
        )

        thread {
            val dataSource = CacheDataSource.CacheDataSourceFactory()
                .create {
                    setCache(simpleCache)
                    setUrl(url)
                }

            // 在中间位置开始读取到EOF
            dataSource.open(Range(4267615))
            val buffer = ByteArray(2048)

            TekiLog.d(TAG, "Should all read from http")
            var realLength = dataSource.read(buffer, 0, 2048)
            while (realLength > 0) {
                realLength = dataSource.read(buffer, 0, 2048)
            }

            val dataSource2 = CacheDataSource.CacheDataSourceFactory()
                .create {
                    setCache(simpleCache)
                    setUrl(url)
                }
            dataSource2.open()

            // 重新open，从0读取到EOF
            realLength = dataSource2.read(buffer, 0, 2048)
            while (realLength > 0) {
                realLength = dataSource2.read(buffer, 0, 2048)
            }

            // 这时候的缓存片段信息应该是完整的从0到contentLength，只有一片
            assertEquals(1, simpleCache.exposedRangeList.size)
            assertEquals(0, simpleCache.exposedRangeList[0].start)
            assertEquals(9267615L, simpleCache.exposedRangeList[0].end)

            countDownLatch.countDown()
        }

        countDownLatch.await()
        assertEquals(9267615L, simpleCache.file!!.length())
    }

    /**
     * 频繁seek
     */
    @Test
    fun testFileReadEmptyData() {
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext
        val countDownLatch = CountDownLatch(1)
        val url = "http://cdn.lizhi.fm/audio/2019/06/23/2744633406271451142_hd.mp3"
        val downloadDirectory = appContext.getExternalFilesDir("test")!!
        val simpleCache = SimpleCache(
            downloadDirectory,
            CacheMmkvStorage.getInstance(),
            LruCacheEvictor(
                CacheMmkvStorage.getInstance(),
                downloadDirectory,
                300 * 1024 * 1024,
                300 * 1024 * 1024
            )
        )

        simpleCache.rethrowException = true

        val dataSource = CacheDataSource.CacheDataSourceFactory().create {
            setCache(simpleCache)
            setUrl(url)
        }.apply {
            (this as CacheDataSource).rethrowException = true
        }
        thread {
            dataSource.open()
            val buffer = ByteArray(2048)

            // 从0读取到EOF
            TekiLog.d(TAG, "Should all read from http")
            var bytesRead = 0L
            var realLength = dataSource.read(buffer, 0, 2048)
            bytesRead += realLength
            while (realLength > 0) {
                dataSource.close()
                realLength = dataSource.read(buffer, 0, 2048)
                bytesRead += realLength
                dataSource.open(Range(bytesRead))
            }

            countDownLatch.countDown()
        }

        countDownLatch.await()
        assertEquals(9267615L, simpleCache.file!!.length())

        for (i in 0..100000) {
            dataSource.open(Range(Random.nextLong(9267615L)))
            dataSource.close()
        }
    }
}