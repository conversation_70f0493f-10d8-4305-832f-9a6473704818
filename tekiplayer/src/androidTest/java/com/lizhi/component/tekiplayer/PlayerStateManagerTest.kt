package com.lizhi.component.tekiplayer

import com.lizhi.component.tekiplayer.controller.PlayerState
import com.lizhi.component.tekiplayer.controller.state.PlayerEvent
import com.lizhi.component.tekiplayer.controller.state.PlayerStateMachine
import com.lizhi.component.tekiplayer.controller.state.StateChangeListener
import org.junit.Assert
import org.junit.Before
import org.junit.Test

/**
 * 文件名：PlayerStateManagerTest
 * 作用：状态机单元测试
 * 作者：huangtianhao
 * 创建日期：2021/4/8
 */
class PlayerStateManagerTest {

    private var lastExitState: Int = -1
    private var lastEnterState: Int = -1
    private var lastExitCauseEvent: Int = -1
    private var lastEnterCauseEvent: Int = -1

    private val listener = object : StateChangeListener {
        override fun onStateExit(state: Int, causeEvent: Int) {
            lastExitState = state
            lastExitCauseEvent = causeEvent
        }

        override fun onStateEnter(state: Int, causeEvent: Int) {
            lastEnterState = state
            lastEnterCauseEvent = causeEvent
        }
    }

    private var playerStateMachine = PlayerStateMachine(
        listener
    )

    @Before
    fun resetPlayerState() {
        playerStateMachine = PlayerStateMachine(listener)
        lastExitState = -1
        lastEnterState = -1
        lastEnterCauseEvent = -1
        lastExitCauseEvent = -1
    }

    /**
     * 验证状态机初始状态
     */
    @Test
    fun testInitState() {
        Assert.assertEquals(PlayerState.STATE_IDLE, playerStateMachine.state)
    }

    /**
     * 验证事件回调正常
     */
    @Test
    fun testCallbackInErrorCallback() {
        // 该事件应该不触发状态变动，所有值应还是默认值
        playerStateMachine.sendEvent(PlayerEvent.EVENT_NEED_MORE_DATA)
        Assert.assertEquals(-1, lastExitState)
        Assert.assertEquals(-1, lastEnterState)
        Assert.assertEquals(-1, lastEnterCauseEvent)
        Assert.assertEquals(-1, lastExitCauseEvent)
        // 当前状态也应该是初始状态
        Assert.assertEquals(PlayerState.STATE_IDLE, playerStateMachine.state)
    }

    /**
     * 验证事件回调正常
     */
    @Test
    fun testCallbackInCorrectCallback() {
        // 初始 STATE_IDLE -> STATE_BUFFERING
        val event = PlayerEvent.EVENT_PREPARE
        playerStateMachine.sendEvent(event)
        Assert.assertEquals(PlayerState.STATE_IDLE, lastExitState)
        Assert.assertEquals(PlayerState.STATE_BUFFERING, lastEnterState)
        Assert.assertEquals(event, lastEnterCauseEvent)
        Assert.assertEquals(event, lastExitCauseEvent)
    }

    /**
     * 验证整个播放流程正确
     */
    @Test
    fun testAllProgress() {
        playerStateMachine.sendEvent(PlayerEvent.EVENT_PREPARE)
        Assert.assertEquals(PlayerState.STATE_BUFFERING, playerStateMachine.state)

        playerStateMachine.sendEvent(PlayerEvent.EVENT_BUFFERED_ENOUGH)
        Assert.assertEquals(PlayerState.STATE_READY, playerStateMachine.state)

        playerStateMachine.sendEvent(PlayerEvent.EVENT_NEED_MORE_DATA)
        Assert.assertEquals(PlayerState.STATE_BUFFERING, playerStateMachine.state)

        playerStateMachine.sendEvent(PlayerEvent.EVENT_BUFFERED_ENOUGH)
        playerStateMachine.sendEvent(PlayerEvent.EVENT_PLAY)
        Assert.assertEquals(PlayerState.STATE_PLAYING, playerStateMachine.state)

        playerStateMachine.sendEvent(PlayerEvent.EVENT_PAUSE)
        Assert.assertEquals(PlayerState.STATE_PAUSED, playerStateMachine.state)

        playerStateMachine.sendEvent(PlayerEvent.EVENT_RESUME)
        Assert.assertEquals(PlayerState.STATE_PLAYING, playerStateMachine.state)

        playerStateMachine.sendEvent(PlayerEvent.EVENT_STOP)
        Assert.assertEquals(PlayerState.STATE_ENDED, playerStateMachine.state)

        playerStateMachine.sendEvent(PlayerEvent.EVENT_PLAY)
        Assert.assertEquals(PlayerState.STATE_BUFFERING, playerStateMachine.state)
    }
}