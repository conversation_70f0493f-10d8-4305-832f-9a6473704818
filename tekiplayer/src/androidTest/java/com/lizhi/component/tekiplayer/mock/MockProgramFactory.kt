package com.lizhi.component.tekiplayer.mock

import android.net.Uri
import android.os.Bundle
import com.lizhi.component.tekiplayer.audioprogram.AudioProgram
import com.lizhi.component.tekiplayer.audioprogram.Program

/**
 * 文件名：MockProgramFactory
 * 作用：
 * 作者：huangtianhao
 * 创建日期：2021/4/8
 */
class MockProgramFactory : Program.Factory {
    override fun createProgram(
        uri: Uri,
        seekTimeUs: Long,
        preBuffering: Boolean,
        fixedDuration: Long,
        category: String?,
        extraData: Bundle?
    ): Program {
        return MockProgram(0f, 0L, null, "mock")
    }

    class MockProgram(
        override var volume: Float, override val durationUs: Long,
        override val lastFatalError: Throwable?, override val uuid: String
    ) : Program {
        override fun isReady(): Boolean {
            return false
        }

        override fun isBuffering(): Boolean {
            return false
        }

        override fun isPlaying(): <PERSON><PERSON>an {
            return false
        }

        override fun preload(
            prepareCallback: (AudioProgram) -> Unit,
            error: (AudioProgram, Throwable?) -> Unit
        ) {

        }

        override fun play() {

        }

        override fun resume() {

        }

        override fun seek(positionUs: Long) {

        }

        override fun seek(percent: Float) {

        }

        override fun continueLoading() {

        }

        override fun stop() {

        }

        override fun pause() {

        }

        override fun getBufferedPositionUs(): Long {
            return 0
        }

        override fun setPlayRate(rate: Float) {

        }

        override fun getPositionUs(): Long {
            return 0
        }

        override fun reportError(throwable: Throwable, recoverable: Boolean) {
        }

        override fun reportTransactionEnd() {

        }

        override fun reportBuffering() {
        }

        override fun reportFinish() {
        }

        override fun reportForceStop() {

        }

        override fun reportPlaying() {
        }

        override fun isEnd(): Boolean {
            return false
        }

        override fun isOnError(): Boolean {
            return false
        }

        override fun stopLoading() {

        }

    }
}