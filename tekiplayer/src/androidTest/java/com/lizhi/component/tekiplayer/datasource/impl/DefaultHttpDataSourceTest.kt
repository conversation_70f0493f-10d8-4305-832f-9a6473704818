package com.lizhi.component.tekiplayer.datasource.impl

import android.net.Uri
import androidx.test.InstrumentationRegistry
import androidx.test.runner.AndroidJUnit4
import com.lizhi.component.tekiplayer.datasource.Range
import org.junit.Assert.assertEquals
import org.junit.Test
import org.junit.runner.RunWith
import java.io.File
import java.util.concurrent.CountDownLatch
import kotlin.concurrent.thread

/**
 * 文件名：DefaultHttpDataSourceTest
 * 作用：DefaultHttpDataSource测试
 * 作者：huangtianhao
 * 创建日期：2021/3/18
 */
@RunWith(AndroidJUnit4::class)
class DefaultHttpDataSourceTest {

    @Test
    fun read() {
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext
        val countDownLatch = CountDownLatch(1)

        val file = File(appContext.getExternalFilesDir("test"), "123.mp3")
        thread {
            val dataSource = DefaultHttpDataSource.DefaultHttpDataSourceFactory()
                .create(Uri.parse("http://cdn.lizhi.fm/audio/2019/06/23/2744633406271451142_hd.mp3"))
            dataSource.open(Range(0))
            val buffer = ByteArray(2048)
            if (file.exists()) {
                file.delete()
            }
            file.createNewFile()
            val outputStream = file.outputStream()

            var totalLength = 0L
            var realLength = dataSource.read(buffer, 0, 2048)
            totalLength += realLength
            while (realLength > 0) {
                outputStream.write(buffer, 0, realLength)
                realLength = dataSource.read(buffer, 0, 2048)
                totalLength += realLength
            }

            countDownLatch.countDown()
        }

        countDownLatch.await()
        assertEquals(9267615, file.length())
    }

}