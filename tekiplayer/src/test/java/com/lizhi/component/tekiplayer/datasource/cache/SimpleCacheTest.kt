package com.lizhi.component.tekiplayer.datasource.cache

//import android.support.test.InstrumentationRegistry
import org.junit.Test

/**
 * 文件名：SimpleCacheTest
 * 作用：
 * 作者：huangtianhao
 * 创建日期：2021/3/18
 */
class SimpleCacheTest {

    private lateinit var simpleCache: SimpleCache

    @Test
    fun getExposedRangeList() {
//        val appContext = InstrumentationRegistry.getInstrumentation().targetContext
//        simpleCache = SimpleCache(appContext.getExternalFilesDir("123")!!)
//
//        assertEquals(true, simpleCache.exposedRangeList.isEmpty())
//        simpleCache.updateCachedRange(Range(0, 1000))
//        assertEquals(
//            1,
//            simpleCache.exposedRangeList.size
//        )
//        val range = simpleCache.getNextRangeOfPos(1)
//        assertEquals(true, range != null && range.start == 0L && range.end == 1000L)
//
//        simpleCache.updateCachedRange(Range(700, 1300))
//        val range2 = simpleCache.getNextRangeOfPos(1)
//        assertEquals(true, range2 != null && range2.start == 0L && range2.end == 1300L)
//
//        simpleCache.updateCachedRange(Range(1400, 2000))
//        assertEquals(
//            2,
//            simpleCache.exposedRangeList.size
//        )
//        val range3 = simpleCache.getNextRangeOfPos(1350)
//        assertEquals(true, range3 != null && range3.start == 1400L && range3.end == 2000L)
//
//        simpleCache.updateCachedRange(Range(1300, 1400))
//        val range4 = simpleCache.getNextRangeOfPos(0)
//        assertEquals(true, range4 != null)
//        assertEquals(0L, range4!!.start)
//        assertEquals(2000L, range4.end)
    }
}