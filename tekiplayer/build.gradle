apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
//apply plugin: 'kotlin-android-extensions'
//apply plugin: "org.jetbrains.dokka"
//apply from: 'https://gitlab.lizhi.fm/component_android/Gradle_Config/raw/master/maven-push.gradle'
apply from: 'https://gitlab.lizhi.fm/component_android/Gradle_Config/-/raw/master/7.0_maven.gradle'

android {
    compileSdkVersion rootProject.ext.android["compileSdkVersion"]

    defaultConfig {
        minSdkVersion rootProject.ext.android["minSdkVersion"]
        targetSdkVersion rootProject.ext.android["targetSdkVersion"]
        versionCode getCompVersion()
        versionName VERSION_NAME

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            buildConfigField "String", "VERSION_NAME", "\"${VERSION_NAME}\""
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            buildConfigField "String", "VERSION_NAME", "\"${VERSION_NAME}\""
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

//dokkaHtml.configure {
//    dokkaSourceSets {
//        named("main") {
//            noAndroidSdkLink.set(false)
//        }
//    }
//}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test:runner:1.1.0-alpha4'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.0-alpha4'
    implementation "androidx.annotation:annotation:1.1.0"
    implementation "com.lizhi.component.base:basetool-base:1.4.0"
//    implementation "com.yibasan.lizhifm.audio:lzopus-lib:0.0.7-SNAPSHOT"
    implementation "androidx.collection:collection-ktx:1.1.0"
    implementation "com.lizhi.component.base:cloudconfig-base:1.2.5"
    implementation 'com.lizhi.component.base:lzaudio-opus-base:0.3.8'
//    implementation(project(':lizhiaudiocore'))
}

def getCompVersion() {
    def versionName = VERSION_NAME.split('-')[0]
    println(versionName)
    def versionSegments = versionName.split('\\.').toList()
    if (versionSegments.size() > 3 && !VERSION_NAME.contains("SNAPSHOT")) {
        throw new Exception("版本号不能超过3段")
    }
    while (versionSegments.size() < 3) {
        versionSegments.add('00')
    }
    def versionBuilder = new StringBuilder()
    def seg;
    for (int i = 0; i < versionSegments.size(); i++) {
        seg = versionSegments[i]
        if (seg.length() >= 3) {
        } else if (seg.length() == 1) {
            seg = "0$seg"
        }
        versionBuilder.append(seg)
    }
    def version = Integer.parseInt(versionBuilder.toString())
    println(version)
    return version
}

