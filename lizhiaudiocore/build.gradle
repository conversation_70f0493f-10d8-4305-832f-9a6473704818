plugins {
    id 'com.android.library'
    id 'product.upload'
}

apply from: 'https://gitlab.lizhi.fm/component_android/Gradle_Config/-/raw/master/7.0_maven.gradle'

android {
    namespace 'com.lizhi.audiocore'
    compileSdk 33
//    ndkPath android.sdkDirectory.absolutePath + '/ndk/21.4.7075529'
    ndkVersion '21.4.7075529'

    defaultConfig {
        minSdk 21
        targetSdk 33
        versionName VERSION_NAME

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"

        externalNativeBuild {
            cmake {
                cppFlags ""
                abiFilters "armeabi-v7a", "arm64-v8a"
                version "3.6.4111459"
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    externalNativeBuild {
        cmake {
            path file('../CMakeLists.txt')
        }
    }
}

dependencies {

    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.8.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}