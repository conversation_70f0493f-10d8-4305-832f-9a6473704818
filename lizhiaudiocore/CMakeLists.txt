cmake_minimum_required(VERSION 3.6.0)

project(lizhiaudiocore)

message(STATUS "lizhiaudiocore var path:")
message(STATUS "lizhiaudiocore CMAKE_SOURCE_DIR = ${CMAKE_SOURCE_DIR}")
message(STATUS "lizhiaudiocore CMAKE_CURRENT_SOURCE_DIR = ${CMAKE_CURRENT_SOURCE_DIR}")
message(STATUS "lizhiaudiocore PROJECT_SOURCE_DIR = ${PROJECT_SOURCE_DIR}")
message(STATUS "lizhiaudiocore CMAKE_BINARY_DIR = ${CMAKE_BINARY_DIR}")
message(STATUS "lizhiaudiocore CMAKE_CURRENT_BINARY_DIR = ${CMAKE_CURRENT_BINARY_DIR}")
message(STATUS "lizhiaudiocore PROJECT_BINARY_DIR = ${PROJECT_BINARY_DIR}")

if(${CMAKE_SYSTEM_NAME} STREQUAL "iOS")
    set(PLATFORM "ios")
    set(LIB<PERSON>RY_TYPE "SHARED")
    set(UNIX "true")
    enable_language(C)
    enable_language(CXX)
    enable_language(OBJC)
    enable_language(OBJCXX)
    set(CMAKE_XCODE_ATTRIBUTE_LD_RUNPATH_SEARCH_PATHS "@executable_path/Frameworks")
    set(CMAKE_XCODE_ATTRIBUTE_DYLIB_INSTALL_NAME_BASE "@rpath/dore.framework/dore")
    set(CMAKE_XCODE_ATTRIBUTE_LD_DYLIB_INSTALL_NAME "@rpath")
    add_definitions(-DWEBRTC_POSIX)
    add_definitions(-DWEBRTC_IOS)
    add_definitions(-DWEBRTC_MAC)
    # add_definitions(-D__APPLE__)
    add_definitions(-D__Userspace_os_Darwin)
    add_definitions(-DHAVE_NETINET_IN_H)
    # -fobjc-arc: c automatic reference counting
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -fexceptions -frtti -Wall -x objective-c++")
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -g -fstack-protector-all -Wall -Wextra -Wno-unused-parameter -Wstrict-aliasing -Wstrict-prototypes -fPIE -Wstack-protector -x objective-c")
    if(${PLATFORM} STREQUAL "SIMULATOR64")
        set(IOS_ARCH "x86_64")
    elseif(${PLATFORM} STREQUAL "OS64")
        set(IOS_ARCH "arm64")
    endif()
elseif(${CMAKE_SYSTEM_NAME} STREQUAL "Android")
    set(PLATFORM "android")
    set(UNIX "true")
    add_definitions(-DHAVE_NETINET_IN_H)
    add_definitions(-DHAVE_SYS_TIME_H=1)
    add_definitions(-DWEBRTC_HAS_NEON)
    add_definitions(-DWEBRTC_POSIX)
    add_definitions(-DWEBRTC_LINUX)
    add_definitions(-DWEBRTC_ANDROID)
    set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${PROJECT_SOURCE_DIR}/jniLibs/${ANDROID_ABI})
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -std=c++1z -fexceptions -frtti -Wall")
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -g -fstack-protector-all -Wall -Wextra -Wno-unused-parameter -Wstrict-aliasing -Wstrict-prototypes -fPIE -Wstack-protector")
else()
    set(PLATFORM "unknow")
    set(UNIX "false")
endif()

set(LIZHI_AUDIO_CORE_DIR ${PROJECT_SOURCE_DIR}/src/main/cpp)
message(STATUS "dore LIZHI_AUDIO_CORE_DIR = ${LIZHI_AUDIO_CORE_DIR}")

if(${CMAKE_SYSTEM_NAME} STREQUAL "Android")
    message(STATUS "android = ${CMAKE_SYSTEM_NAME}")
    set(PLATFORM_SRC_LIST
            ${LIZHI_AUDIO_CORE_DIR}/jni/jni_lizhi_audiobuffer_process.cpp
            ${LIZHI_AUDIO_CORE_DIR}/jni/jni_lizhi_audiobuffer_process.h
            )
elseif(${CMAKE_SYSTEM_NAME} STREQUAL "iOS")
    message(STATUS "ios = ${CMAKE_SYSTEM_NAME}")
    set(PLATFORM_SRC_LIST)
endif()

add_library(lizhiaudiocore SHARED
        ${PLATFORM_SRC_LIST}
        ${LIZHI_AUDIO_CORE_DIR}/lizhiaudiocore/audio_codecs/opus/audio_decoder_opus_impl.cc
        ${LIZHI_AUDIO_CORE_DIR}/lizhiaudiocore/audio_codecs/opus/audio_decoder_opus_api.cc
        ${LIZHI_AUDIO_CORE_DIR}/lizhiaudiocore/audio_codecs/opus/opus_interface.c
        ${LIZHI_AUDIO_CORE_DIR}/lizhiaudiocore/audio_buffer/audio_codec_buffer.h
        ${LIZHI_AUDIO_CORE_DIR}/lizhiaudiocore/audio_buffer/audio_codec_buffer.cpp
        ${LIZHI_AUDIO_CORE_DIR}/lizhiaudiocore/audio_buffer/dugon/mutex_posix.cpp
        ${LIZHI_AUDIO_CORE_DIR}/lizhiaudiocore/audio_buffer/dugon/scoped_lock.cpp
        ${LIZHI_AUDIO_CORE_DIR}/lizhiaudiocore/audio_buffer/dugon/ring_buffer.cpp

        # e2ed
        ${LIZHI_AUDIO_CORE_DIR}/lizhiaudiocore/audio_buffer/e2ed/e2ed.h
        ${LIZHI_AUDIO_CORE_DIR}/lizhiaudiocore/audio_buffer/e2ed/e2ed_android.h
        ${LIZHI_AUDIO_CORE_DIR}/lizhiaudiocore/audio_buffer/e2ed/e2ed_android.cc
        ${LIZHI_AUDIO_CORE_DIR}/lizhiaudiocore/audio_buffer/e2ed/e2ed_ios.h
        ${LIZHI_AUDIO_CORE_DIR}/lizhiaudiocore/audio_buffer/e2ed/e2ed_ios.mm

        #common
        ${LIZHI_AUDIO_CORE_DIR}/lizhiaudiocore/common/util/lz_logger.cpp
        ${LIZHI_AUDIO_CORE_DIR}/lizhiaudiocore/common/util/lz_logger.h
        ${LIZHI_AUDIO_CORE_DIR}/lizhiaudiocore/common/util/print_util.hpp
        )


include_directories(
        ${LIZHI_AUDIO_CORE_DIR}/
        ${LIZHI_AUDIO_CORE_DIR}/jni
        ${LIZHI_AUDIO_CORE_DIR}/lizhiaudiocore
        ${LIZHI_AUDIO_CORE_DIR}/third_party/opus/${PLATFORM}
        ${LIZHI_AUDIO_CORE_DIR}/third_party/libspdlog/include
)

# libspdlog
add_library(libspdlog STATIC IMPORTED)
set_target_properties(libspdlog
    PROPERTIES IMPORTED_LOCATION
    ${LIZHI_AUDIO_CORE_DIR}/third_party/libspdlog/lib/${ANDROID_ABI}/libspdlog.a)

if(${CMAKE_SYSTEM_NAME} STREQUAL "Android")
    # opus
    add_library(opus STATIC IMPORTED)
    set_target_properties(opus PROPERTIES IMPORTED_LOCATION
            ${LIZHI_AUDIO_CORE_DIR}/third_party/opus/${PLATFORM}/lib/${ANDROID_ABI}/libopus.a)
elseif(${CMAKE_SYSTEM_NAME} STREQUAL "iOS")
    set(FRAMEWORK_HEADER
            ${LIZHI_AUDIO_CORE_DIR}/lizhiaudiocore/audio_buffer/utils/audio_message_callback.h
            ${LIZHI_AUDIO_CORE_DIR}/lizhiaudiocore/audio_buffer/audio_codec_buffer.h
            )
    set_target_properties(${PROJECT_NAME} PROPERTIES
            FRAMEWORK TRUE
            FRAMEWORK_VERSION A
            MACOSX_FRAMEWORK_IDENTIFIER com.lizhi.audiocore
            VERSION 1.0.0
            SOVERSION 1.0.0
            PUBLIC_HEADER "${FRAMEWORK_HEADER}"
            XCODE_ATTRIBUTE_CODE_SIGN_IDENTITY "iPhone Developer"
            XCODE_ATTRIBUTE_PRODUCT_BUNDLE_IDENTIFIER "AudioCore"
            XCODE_ATTRIBUTE_CLANG_ENABLE_OBJC_ARC "No"
            XCODE_ATTRIBUTE_CLANG_ENABLE_OBJC_WEAK "Yes"
            )
    add_library(opus STATIC IMPORTED)
    set_target_properties(opus PROPERTIES IMPORTED_LOCATION
            ${LIZHI_AUDIO_CORE_DIR}/third_party/opus/${PLATFORM}/lib/libopus.a)
endif()


if(${CMAKE_SYSTEM_NAME} STREQUAL "Android")
    target_link_libraries(lizhiaudiocore -llog -lm -lz opus libspdlog)
elseif(${CMAKE_SYSTEM_NAME} STREQUAL "iOS")
    target_link_libraries(lizhiaudiocore -lm -lz -pthread -ldl -liconv opus libspdlog "-framework CoreVideo"
            "-framework CoreMedia" "-framework VideoToolbox" "-framework AudioToolbox" "-framework AVFoundation"
            "-framework CoreGraphics" "-framework UIKit" "-framework CoreGraphics"
            "-framework GLKit" "-framework QuartzCore")
endif()