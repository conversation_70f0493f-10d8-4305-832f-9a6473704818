package com.lizhi.audiocore;

import java.nio.ByteBuffer;

public class AudioBufferProcess {
    public AudioBufferProcess() {  }

    static {
        System.loadLibrary("lizhiaudiocore");
    }

    public synchronized static OpusFormat probe(byte[] header, int size, boolean isEnd) {
        return nativeProbe1(header, size, isEnd);
    }

    public void init(int channel,  int sampleRate, int frameSize, boolean isRTPRealTimeDecrypt) {
        mNativeHandle = nativeInit(channel, sampleRate, frameSize, isRTPRealTimeDecrypt,
                cipherData -> mCipherCallback.onDecrypt(cipherData));
        mInit = true;
    }

    public void unit() {
        nativeUninit(mNativeHandle);
        mInit = false;
    }

    public boolean sendDecodeBuffer(byte[] buffer, int size, boolean isEnd) {
        if (!mInit) {
            return false;
        }
        return nativeSendDecodeBuffer(mNativeHandle, buffer, size, isEnd);
    }

    public int getUnWriteSize() {
        if (!mInit) {
            return 0;
        }
        return nativeGetUnWriteSize(mNativeHandle);
    }

    public void stopDecoderThread() {
        if (!mInit){
            return;
        }
        nativeStopDecoderThread(mNativeHandle);
    }

    public void setFullByteBufferCallBack(FillByteBufferCallback callback) {
        mCallBack = callback;
    }

    public void setCipherCallback(CipherCallback callback){
        mCipherCallback = callback;
    }

   public void cacheDirectBufferAddress(ByteBuffer bytebuffer) {
        nativeCacheDirectBufferAddress(mNativeHandle, bytebuffer);
   }

    public void lastFrameFinished() {
         mCallBack.lastFrameFinished();
    }

    public void inPutErrorData() {
         mCallBack.inPutErrorData();
    }


    public void getPerPcmData() {
        mCallBack.fullPcmDataSuccess();
    }

    public void quitDecoderThread() { mCallBack.quicDecodeThreadSuccess();}

    private native long nativeInit(int channel,  int sampleRate, int frame_size, boolean isRTPRealTimeDecrypt, CipherCallback cipherCallback);

    private native void nativeUninit(long handler);

    private static native OpusFormat nativeProbe1(byte[] buffer, int size, boolean isEnd);

    private native boolean nativeSendDecodeBuffer(long handler, byte[] buffer, int size, boolean isEnd);

    private native void nativeCacheDirectBufferAddress(long handler, ByteBuffer buffer);

    private native void nativeStopDecoderThread(long handler);

    private native int nativeGetUnWriteSize(long handler);

    private long mNativeHandle;

    private boolean mInit;

    private FillByteBufferCallback mCallBack;
    private CipherCallback mCipherCallback;
}
