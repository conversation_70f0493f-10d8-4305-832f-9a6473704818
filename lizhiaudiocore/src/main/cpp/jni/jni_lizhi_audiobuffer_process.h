/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_lizhi_audiocore_AudioBufferProcess */

#ifndef _Included_com_lizhi_audiocore_AudioBufferProcess
#define _Included_com_lizhi_audiocore_AudioBufferProcess
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_lizhi_audiocore_AudioBufferProcess
 * Method:    nativeInit
 * Signature: (II)J
 */
JNIEXPORT jlong JNICALL Java_com_lizhi_audiocore_AudioBufferProcess_nativeInit
  (JNIEnv *, jobject, jint, jint, jint, jboolean, jobject);

/*
 * Class:     com_lizhi_audiocore_AudioBufferProcess
 * Method:    nativeUninit
 * Signature: (J)V
 */
JNIEXPORT void JNICALL Java_com_lizhi_audiocore_AudioBufferProcess_nativeUninit
  (JNIEnv *, jobject, jlong);

/*
 * Class:     com_lizhi_audiocore_AudioBufferProcess
 * Method:    nativeSendDecodeBuffer
 * Signature: (J[BIZ)Z
 */
JNIEXPORT jboolean JNICALL Java_com_lizhi_audiocore_AudioBufferProcess_nativeSendDecodeBuffer
  (JNIEnv *, jobject, jlong, jbyteArray, jint, jboolean);

/*
 * Class:     com_lizhi_audiocore_AudioBufferProcess
 * Method:    nativeCacheDirectBufferAddress
 * Signature: (JLjava/nio/ByteBuffer;)V
 */
JNIEXPORT void JNICALL Java_com_lizhi_audiocore_AudioBufferProcess_nativeCacheDirectBufferAddress
  (JNIEnv *, jobject, jlong, jobject);

/*
 * Class:     com_lizhi_audiocore_AudioBufferProcess
 * Method:    nativeStopDecoderThread
 * Signature: (J)V
 */
JNIEXPORT void JNICALL Java_com_lizhi_audiocore_AudioBufferProcess_nativeStopDecoderThread
  (JNIEnv *, jobject, jlong);

/*
 * Class:     com_lizhi_audiocore_AudioBufferProcess
 * Method:    nativeGetUnWriteSize
 * Signature: (J)I
 */
JNIEXPORT jint JNICALL Java_com_lizhi_audiocore_AudioBufferProcess_nativeGetUnWriteSize
  (JNIEnv *, jobject, jlong);

/*
 * Class:     com_lizhi_audiocore_AudioBufferProcess
 * Method:    nativeProbe
 * Signature: (J[B)I
 */
JNIEXPORT jobject JNICALL
Java_com_lizhi_audiocore_AudioBufferProcess_nativeProbe1(JNIEnv *env, jclass clazz,
                                                         jbyteArray buffer, jint size,
                                                         jboolean is_end);

#ifdef __cplusplus
}
#endif
#endif
