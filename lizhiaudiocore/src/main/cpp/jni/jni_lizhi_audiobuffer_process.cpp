#include <string.h>
#include "jni_lizhi_audiobuffer_process.h"
#include "audio_buffer/audio_codec_buffer.h"
#include "audio_buffer/utils/logging.h"


bool JstringTransformToChar(JNIEnv *env, jstring jstr, char *result) {
  if (env == nullptr || jstr == nullptr) {
    return false;
  }
  jclass class_string = env->FindClass("java/lang/String");
  jstring string_encode = env->NewStringUTF("utf-8");
  jmethodID mid =
    env->GetMethodID(class_string, "getBytes", "(Ljava/lang/String;)[B");
  auto barr = (jbyteArray) env->CallObjectMethod(jstr, mid, string_encode);
  jsize array_len = env->GetArrayLength(barr);
  jbyte *ba = env->GetByteArrayElements(barr, JNI_FALSE);
  if (array_len > 0) {
    memcpy(result, ba, array_len);
    result[array_len] = 0;
  }
  env->DeleteLocalRef(class_string);
  env->DeleteLocalRef(string_encode);
  env->ReleaseByteArrayElements(barr, ba, 0);
  return true;
}

extern "C" jlong JNICALL Java_com_lizhi_audiocore_AudioBufferProcess_nativeInit
  (JNIEnv *env, jobject obj, jint channels, jint sample_rate, jint frame_size,
   jboolean is_rtp_real_time_decrypt, jobject cipher_callback) {
  jclass jcls = env->GetObjectClass(obj);
  jmethodID jmtd_get_pcm = env->GetMethodID(jcls, "getPerPcmData", "()V");
  jmethodID jmtd_quit_decode_thread = env->GetMethodID(jcls, "quitDecoderThread", "()V");
  jmethodID jmtd_last_frame_finished = env->GetMethodID(jcls, "lastFrameFinished", "()V");
  jmethodID jmtd_input_data_error = env->GetMethodID(jcls, "inPutErrorData", "()V");
  webrtc::AudioCodecBuffer *audio_codec_buffer =
    new webrtc::AudioCodecBuffer(channels, sample_rate, frame_size, is_rtp_real_time_decrypt, env,
                                 jmtd_get_pcm,
                                 jmtd_quit_decode_thread, jmtd_last_frame_finished,
                                 jmtd_input_data_error, obj, cipher_callback);
  return (jlong) audio_codec_buffer;
}

extern "C" JNIEXPORT void JNICALL Java_com_lizhi_audiocore_AudioBufferProcess_nativeUninit
  (JNIEnv *env, jobject, jlong handler) {
  auto *audio_codec_buffer = (webrtc::AudioCodecBuffer *) handler;
  delete audio_codec_buffer;
  audio_codec_buffer = nullptr;
}

extern "C" JNIEXPORT jboolean JNICALL
Java_com_lizhi_audiocore_AudioBufferProcess_nativeSendDecodeBuffer
  (JNIEnv *env, jobject, jlong handler, jbyteArray array, jint array_len, jboolean is_end) {
  auto *audio_codec_buffer = (webrtc::AudioCodecBuffer *) handler;
  uint8_t codec_data[512];
  jbyte *byte = env->GetByteArrayElements(array, 0);

  if (array_len > 0) {
    memcpy(codec_data, (uint8_t *) byte, array_len);
  }

  bool res = audio_codec_buffer->SendCodecBuffer(codec_data, array_len, is_end);
  env->ReleaseByteArrayElements(array, byte, 0);
  return res;
}

extern "C" JNIEXPORT void JNICALL
Java_com_lizhi_audiocore_AudioBufferProcess_nativeCacheDirectBufferAddress
  (JNIEnv *env, jobject, jlong handler, jobject byte_buffer) {
  auto *audio_codec_buffer = (webrtc::AudioCodecBuffer *) handler;
  audio_codec_buffer->SetCacheDirectBuffer(env, byte_buffer);
}

extern "C" JNIEXPORT void JNICALL
Java_com_lizhi_audiocore_AudioBufferProcess_nativeStopDecoderThread
  (JNIEnv *env, jobject, jlong handler) {
  auto *audio_device_buffer = (webrtc::AudioCodecBuffer *) handler;
  audio_device_buffer->StopDecoder();
}

extern "C" JNIEXPORT jint JNICALL Java_com_lizhi_audiocore_AudioBufferProcess_nativeGetUnWriteSize
  (JNIEnv *, jobject, jlong handler) {
  auto *audio_device_buffer = (webrtc::AudioCodecBuffer *) handler;
  return audio_device_buffer->GetUnWriteSize();
}

extern "C" JNIEXPORT
jobject
Java_com_lizhi_audiocore_AudioBufferProcess_nativeProbe1(JNIEnv *env, jclass thiz,
                                                         jbyteArray buffer,
                                                         jint size, jboolean is_end) {

  if (size > 0) {
    jbyte *byte = env->GetByteArrayElements(buffer, JNI_FALSE);
    // 探测音频数据格式
    uint32_t channel = 0;
    uint32_t sample_rate = 0;
    uint32_t frame_size = 0;
    webrtc::BufferProbeType result = webrtc::AudioCodecBuffer::ProbeBuffer((uint8_t *) byte, size,
                                                                           &channel, &sample_rate,
                                                                           &frame_size, is_end);

    if (result == webrtc::BufferProbeType::Error) {
      env->ReleaseByteArrayElements(buffer, byte, 0);
      return nullptr;
    }
    // 通过 jni 构造 com.lizhi.audiocore.OpusFormat 类对象并返回结果
    bool format_result = result == webrtc::BufferProbeType::Ok;
    jclass jcls = env->FindClass("com/lizhi/audiocore/OpusFormat");
    jmethodID jmid = env->GetMethodID(jcls, "<init>", "(IIIZ)V");
    jobject jobj = env->NewObject(jcls, jmid, (jint) sample_rate, (jint) channel, (jint) frame_size,
                                  format_result);
    env->DeleteLocalRef(jcls);
    env->ReleaseByteArrayElements(buffer, byte, 0);
    return jobj;
  }
  return nullptr;
}
