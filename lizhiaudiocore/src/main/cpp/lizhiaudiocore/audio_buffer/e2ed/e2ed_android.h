//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/9/19.
//

#ifndef TEKIPLAYER_E2ED_ANDROID_H
#define TEKIPLAYER_E2ED_ANDROID_H

#include <jni.h>
#include <linux/prctl.h>
#include <sys/prctl.h>
#include "e2ed.h"
#include "spdlog/spdlog.h"

namespace webrtc {

class E2edAndroid : public webrtc::Decryptor {
 public:
  E2edAndroid(jobject cipher_callback);

  ~E2edAndroid() {};

  DecryptRet OnDecrypt(uint8_t *encoded, uint16_t length) override;

 private:
  static JNIEnv *AttachCurrentThread(bool &is_attach);

  jmethodID GetMethodID(JNIEnv *env, jobject obj, const char *name,
                        const char *sig);

 private:
  jobject jcipher_callback_;
};

}

#endif //TEKIPLAYER_E2ED_ANDROID_H
