//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/9/19.
//

#include "e2ed_ios.h"

namespace webrtc {

E2edIos::E2edIos(void *pthis, DecryptDataCallback decrypt_callback)
  : pthis_(pthis), decrypt_callback_(decrypt_callback) {}

DecryptRet E2edIos::OnDecrypt(uint8_t *encoded, uint16_t length) {
  if (!decrypt_callback_) {
    return {nullptr, 0};
  }
  return (*decrypt_callback_)(pthis_, encoded, length);
}

} // namespace webrtc
