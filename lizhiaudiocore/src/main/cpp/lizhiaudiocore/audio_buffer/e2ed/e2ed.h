//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/9/19.
// e2ed: end to end decryption
//

#ifndef TEKIPLAYER_E2ED_H
#define TEKIPLAYER_E2ED_H

#include "lizhiaudiocore/common/util/lz_logger.h"

namespace webrtc {

using DecryptRet = std::pair<std::shared_ptr<uint8_t>, uint16_t>;

class Decryptor {
 public:
  virtual ~Decryptor() {};

  virtual DecryptRet OnDecrypt(uint8_t *encoded, uint16_t length) = 0;
};

} // webrtc

#endif //TEKIPLAYER_E2ED_H
