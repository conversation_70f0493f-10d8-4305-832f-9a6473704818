//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/9/19.
//

#include "e2ed_android.h"

static JavaVM *g_jvm_ = NULL;

namespace webrtc {

#define TAG "E2edAndroid"
pthread_t current_thread_id_ = NULL;
jmethodID on_decrypt_mid_ = NULL;;

E2edAndroid::E2edAndroid(jobject cipher_callback) : jcipher_callback_(cipher_callback) {}

DecryptRet E2edAndroid::OnDecrypt(uint8_t *encoded, uint16_t length) {
  // logI(TAG, FUNC);
  if (g_jvm_ == NULL) {
    logE(TAG, FUNC + "g_jvm_ null");
    return {nullptr, 0};
  }
  if (!jcipher_callback_) {
    logE(TAG, FUNC + "jcipher_callback_ null");
    return {nullptr, 0};
  }
  bool is_attach;
  JNIEnv *env = AttachCurrentThread(is_attach);
  if (!env) {
    return {nullptr, 0};
  }
  pthread_t current_thread_id = pthread_self();
  if (!current_thread_id_ || current_thread_id_ != current_thread_id) {
    current_thread_id_ = current_thread_id;
    on_decrypt_mid_ = GetMethodID(env, jcipher_callback_, "onDecrypt",
                                  "([B)Ljava/nio/ByteBuffer;");
  }

  if (!on_decrypt_mid_) {
    logE(TAG, FUNC + "find on_decrypt_mid_ fail");
    if (is_attach) {
      g_jvm_->DetachCurrentThread();
    }
    return {nullptr, 0};
  }

  jbyteArray params = env->NewByteArray((jsize) length);
  env->SetByteArrayRegion(params, 0, (jsize) length, reinterpret_cast<jbyte *>(encoded));
  jobject result_jobj = (jobject) env->CallObjectMethod(jcipher_callback_, on_decrypt_mid_, params);
  env->DeleteLocalRef(params);

  if (result_jobj == nullptr) {
    logE(TAG, FUNC + "onDecrypt return null");
    if (is_attach) {
      g_jvm_->DetachCurrentThread();
    }
    return {nullptr, 0};
  }
  auto direct_buffer = env->GetDirectBufferAddress(result_jobj);
  jlong capacity = env->GetDirectBufferCapacity(result_jobj);
  uint16_t len = static_cast<uint16_t>(capacity);
  auto plain_data = std::shared_ptr<uint8_t>(new uint8_t[len], std::default_delete<uint8_t[]>());
  memcpy(plain_data.get(), reinterpret_cast<uint8_t *>(direct_buffer), len);
  jmethodID clear_mid = GetMethodID(env, result_jobj, "clear", "()Ljava/nio/Buffer;");
  if (clear_mid) {
    env->CallObjectMethod(result_jobj, clear_mid);
  }
  env->DeleteLocalRef(result_jobj);

  if (is_attach) {
    g_jvm_->DetachCurrentThread();
  }

  return {plain_data, len};
}

JNIEnv *E2edAndroid::AttachCurrentThread(bool &is_attach) {
  is_attach = false;
  JNIEnv *env = nullptr;
  if (g_jvm_ == NULL) {
    logE(TAG, FUNC + "g_jvm_ null");
    return nullptr;
  }
  jint ret = g_jvm_->GetEnv(reinterpret_cast<void **>(&env),
                            JNI_VERSION_1_6);
  if (ret == JNI_EDETACHED || !env) {
    JavaVMAttachArgs args;
    args.version = JNI_VERSION_1_6;
    args.group = nullptr;
    // 16 is the maximum size for thread names on Android.
    char thread_name[16];
    int err = prctl(PR_GET_NAME, thread_name);
    if (err < 0) {
      args.name = nullptr;
    } else {
      args.name = thread_name;
    }
    ret = g_jvm_->AttachCurrentThread(&env, &args);
    if (ret != 0) {
      logE(TAG, FUNC + "AttachCurrentThread fail");
      return nullptr;
    }
    is_attach = true;
  }
  return env;
}

jmethodID E2edAndroid::GetMethodID(JNIEnv *env, jobject obj, const char *name, const char *sig) {
  if (obj == nullptr) return nullptr;
  jclass clazz = env->GetObjectClass(obj);
  if (clazz == nullptr) {
    logE(TAG, FUNC + "clazz is nullptr");
    return nullptr;
  }
  jmethodID id = env->GetMethodID(clazz, name, sig);
  env->DeleteLocalRef(clazz);
  return id;
}

extern "C" JNIEXPORT jint JNICALL JNI_OnLoad(JavaVM *jvm, void *reserved) {
  g_jvm_ = jvm;
  return JNI_VERSION_1_6;
}

}