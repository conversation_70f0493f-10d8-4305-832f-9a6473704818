//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/9/19.
//

#ifndef TEKIPLAYER_E2ED_IOS_H
#define TEKIPLAYER_E2ED_IOS_H

#include "e2ed.h"

#include <cstdint>

#include "lizhiaudiocore/audio_buffer/utils/audio_message_callback.h"

namespace webrtc {

class E2edIos : public webrtc::Decryptor {
 public:
  E2edIos(void *pthis, DecryptDataCallback decrypt_callback);

  ~E2edIos() {};

  DecryptRet OnDecrypt(uint8_t *encoded, uint16_t length) override;

 private:
  void *pthis_ = nullptr;
  DecryptDataCallback decrypt_callback_;
};

} // namespace webrtc

#endif // TEKIPLAYER_E2ED_IOS_H
