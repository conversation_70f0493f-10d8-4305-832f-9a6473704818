#include <pthread.h>

#include "mutex.h"
#include "mutex_impl_posix.h"

namespace DUGON {

Mutex::Mutex() {
  // 将mutex调整为同一个线程可以重复获得, hjx 2017年7月18日
  pthread_mutexattr_t attr;
  pthread_mutexattr_init(&attr);
  pthread_mutexattr_settype(&attr, PTHREAD_MUTEX_RECURSIVE);
  pthread_mutex_init(&mutex_, &attr);
}

Mutex::~Mutex() { pthread_mutex_destroy(&mutex_); }

bool Mutex::Lock() {
  int ret = pthread_mutex_lock(&mutex_);
  return ret == 0;
}

bool Mutex::Unlock() {
  int ret = pthread_mutex_unlock(&mutex_);
  return ret == 0;
}

}  // namespace DUGON
