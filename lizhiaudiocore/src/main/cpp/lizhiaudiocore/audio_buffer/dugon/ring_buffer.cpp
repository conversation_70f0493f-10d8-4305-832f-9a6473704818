#include "ring_buffer.h"

#include <cstring>

#include "scoped_lock.h"
#include "lizhiaudiocore/audio_buffer/utils/logging.h"

namespace DUGON {
RingBuffer::RingBuffer(unsigned int capacity, std::string name)
  : buffer_(NULL),
    size_(capacity),
    name_(name),
    read_pos_(NULL),
    write_pos_(NULL),
    un_read_size_(0),
    is_end_(false),
    left_buf_size_(capacity) {
  buffer_ = new uint8_t[capacity];

  if (buffer_) {
    Reset();
  }
}

RingBuffer::~RingBuffer() {
  if (buffer_) {
    delete[] buffer_;
  }

  buffer_ = NULL;
  read_pos_ = NULL;
  write_pos_ = NULL;
}

// write data "data" with length "lenght"
RingBufferErrorType RingBuffer::Write(const uint8_t *data, unsigned int length) {
  ScopedLock autoLock(buffer_lock_);

  // check the left size
  if (left_buf_size_ < length) {
    return kNoEnoughData;
  }

  // write: copy data
  if (write_pos_ >= read_pos_) {
    unsigned int first_part_size = (unsigned int) (buffer_ + size_ - write_pos_);

    if (length <= first_part_size) {
      memcpy(write_pos_, data, length);
      write_pos_ += length;
    } else {
      memcpy(write_pos_, data, first_part_size);
      const uint8_t *data_pos = data + first_part_size;
      write_pos_ = buffer_;
      memcpy(write_pos_, data_pos, length - first_part_size);
      write_pos_ = buffer_ + length - first_part_size;
    }
  } else {
    memcpy(write_pos_, data, length);
    write_pos_ += length;
  }

  un_read_size_ += length;
  left_buf_size_ -= length;

  return kNoError;
}

void
RingBuffer::ParseOpusHeader(uint8_t *opus_header, uint16_t *codec_length, uint32_t *sync_header) {
  *sync_header = (((uint32_t) opus_header[0]) << 24) + ((((uint32_t) opus_header[1])) << 16) +
                 (((uint32_t) opus_header[2]) << 8) + ((uint32_t) opus_header[3]);
  *codec_length = (((uint16_t) opus_header[5]) << 8) + ((uint16_t) opus_header[6]);
}

RingBufferErrorType RingBuffer::CheckSyncHeader() {
  if (un_read_size_ < OPUS_HEADER_LENGTH) {
    return kNoEnoughData;
  }

  uint8_t codec_data[OPUS_HEADER_LENGTH];
  if (write_pos_ > read_pos_) {
    memcpy(codec_data, read_pos_, OPUS_HEADER_LENGTH);
  } else {
    unsigned int first_part_size = (unsigned int) (buffer_ + size_ - read_pos_);
    if (first_part_size >= OPUS_HEADER_LENGTH) {
      memcpy(codec_data, read_pos_, OPUS_HEADER_LENGTH);
    } else {
      memcpy(codec_data, read_pos_, first_part_size);
      memcpy(codec_data + first_part_size, buffer_, OPUS_HEADER_LENGTH - first_part_size);
    }
  }

  unsigned short codec_length;
  unsigned int sync_header;
  ParseOpusHeader(codec_data, &codec_length, &sync_header);

  if (SYNC_HEADER != sync_header) {
    return kError;
  } else if (un_read_size_ < (OPUS_HEADER_LENGTH + codec_length)) {
    return kNoEnoughData;
  } else if (un_read_size_ >= (OPUS_HEADER_LENGTH + codec_length) &&
             un_read_size_ < (OPUS_HEADER_LENGTH * 2 + codec_length)) {
    if (is_end_) {
      return kNoError;
    } else {
      return kNoEnoughData;
    }
  } else {
    unsigned int total_size = OPUS_HEADER_LENGTH * 2 + codec_length;
    uint8_t total_data[total_size];
    if (write_pos_ > read_pos_) {
      memcpy(total_data, read_pos_, total_size);
    } else {
      unsigned int first_part_size = (unsigned int) (buffer_ + size_ - read_pos_);
      if (first_part_size >= total_size) {
        memcpy(total_data, read_pos_, total_size);
      } else {
        memcpy(total_data, read_pos_, first_part_size);
        memcpy(total_data + first_part_size, buffer_, total_size - first_part_size);
      }
    }

    ParseOpusHeader(total_data + OPUS_HEADER_LENGTH + codec_length, &codec_length, &sync_header);
    if (sync_header != SYNC_HEADER) {
      return kError;
    }
  }

  return kNoError;
}

bool RingBuffer::FindSyncHeader(uint32_t *correct_error_count) {
  ScopedLock autoLock(buffer_lock_);
  *correct_error_count = 0;
  while (true) {
    RingBufferErrorType type = CheckSyncHeader();
    if (type == kNoError) {
      if (*correct_error_count > correct_error_count_threshold_) {
        return false;
      }
      return true;
    } else if (type == kNoEnoughData) {
      return false;
    }

    if (type == kError) {
      if ((buffer_ + size_ - read_pos_) == 0) {
        read_pos_ = buffer_;
      }
      read_pos_++;
      un_read_size_--;
      left_buf_size_++;
      (*correct_error_count)++;
    }
  }
}

// read data to "data" with length "length"
RingBufferErrorType RingBuffer::Read(uint8_t *data, unsigned int length) {
  ScopedLock autoLock(buffer_lock_);

  // check the unread data length
  if (length > un_read_size_) {
    return kNoEnoughData;
  }

  // read: copy data
  if (write_pos_ > read_pos_) {
    memcpy(data, read_pos_, length);
    read_pos_ += length;
  } else {
    unsigned int first_part_size = (unsigned int) (buffer_ + size_ - read_pos_);

    if (length <= first_part_size) {
      memcpy(data, read_pos_, length);
      read_pos_ += length;
    } else {
      memcpy(data, read_pos_, first_part_size);
      uint8_t *data_pos = data + first_part_size;
      read_pos_ = buffer_;
      memcpy(data_pos, read_pos_, length - first_part_size);
      read_pos_ = buffer_ + length - first_part_size;
    }
  }

  un_read_size_ -= length;
  left_buf_size_ += length;

  return kNoError;
}

unsigned int RingBuffer::GetUnwriteSize() {
  return left_buf_size_;
}

void RingBuffer::Reset() {
  memset(buffer_, 0, size_ / sizeof(char));
  read_pos_ = buffer_;
  write_pos_ = buffer_;
  left_buf_size_ = size_;
  un_read_size_ = 0;
}

unsigned int RingBuffer::GetUnreadSize() { return un_read_size_; }

void RingBuffer::MarkEnd() {
  is_end_ = true;
}

}  // namespace DUGON
