#ifndef SCOPED_LOCK_H
#define SCOPED_LOCK_H

namespace DUGON {

class Mutex;

/**
 * ScopedLock is the class to lock and unlock mutex object automatically.
 * Mutex object will be locked with ScopedLock object is constructed, and
 * will be unlocked with it is destructed.
 */
class ScopedLock {
 public:
  /**
   * Constructor.
   * @param lock Mutex object that will be locked.
   */
  ScopedLock(Mutex &lock);

  /**
   * Destructor.
   * Mutex object will be unlocked in destructor.
   */
  virtual ~ScopedLock();

 private:
  Mutex &lock_;
};

}  // namespace DUGON
#endif
