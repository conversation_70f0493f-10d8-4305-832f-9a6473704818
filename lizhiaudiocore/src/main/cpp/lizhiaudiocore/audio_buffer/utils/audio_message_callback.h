#ifndef AUDIO_MESSAGE_CALLBACK_H_
#define AUDIO_MESSAGE_CALLBACK_H_

#include <stddef.h>
#include <stdint.h>

#include <memory>
#include <vector>

#ifdef __cplusplus
extern "C" {
#endif

typedef webrtc::DecryptRet (*DecryptDataCallback)(void *, const uint8_t *data, uint16_t size);

typedef void (*RawDataCallback)(void *, const uint8_t *data, uint32_t size, uint32_t packet_size);

typedef void (*LastFrameFinished)(void *);

typedef void (*QuitDecodeThreadEvent)(void *);

typedef void (*ErrorDataInput)(void *, const char *info);

#ifdef __cplusplus
}
#endif
#endif
