#include "audio_codec_buffer.h"

#include <string.h>

#include <cmath>
#include <cstddef>
#include <cstdint>
#include <iosfwd>

#include "lizhiaudiocore/audio_buffer/utils/logging.h"
#include "lizhiaudiocore/audio_codecs/opus/audio_decoder_opus.h"
#include "lizhiaudiocore/common/util/print_util.hpp"

namespace webrtc {

#define TAG "AudioCodecBuffer"
#define AUDIO_OPUS_MIN_FRAMES (2)
#define AUDIO_OPUS_MAX_PROBE_BYTE (512)
#ifdef WEBRTC_ANDROID
static JavaVM *jvm_;

AudioCodecBuffer::AudioCodecBuffer(uint32_t channels, uint32_t sample_rate, uint32_t frame_size,
                                   bool is_rtp_real_time_decrypt, JNIEnv *env,
                                   jmethodID pcm_callback, jmethodID quit_decode_thread,
                                   jmethodID last_frame_finished, jmethodID input_data_error,
                                   jobject jaudio_buffer_obj, jobject cipher_callback)
  : sample_rates_(sample_rate),
    pre_frame_time_stamp_(0),
    frame_size_(frame_size),
    channels_(channels),
    is_rtp_real_time_decrypt_(is_rtp_real_time_decrypt) {
  normal_decode_size_ = sample_rates_ * frame_size_ / 1000;
  decoded_buffer_data_ = new int16_t[normal_decode_size_];
  pcm_callback_ = pcm_callback;
  last_frame_finished_ = last_frame_finished;
  input_data_error_ = input_data_error;
  quit_decode_thread_ = quit_decode_thread;
  env->GetJavaVM(&jvm_);
  jaudio_buffer_obj_ = env->NewGlobalRef(jaudio_buffer_obj);
  e2ed_ = std::make_shared<E2edAndroid>(env->NewGlobalRef(cipher_callback));
  Init();
}

#endif

#ifdef WEBRTC_IOS
AudioCodecBuffer::AudioCodecBuffer(uint32_t channels, uint32_t sample_rate, uint32_t frame_size, bool is_rtp_real_time_decrypt,
                                   RawDataCallback raw_data_callback, LastFrameFinished last_frame_finished,
                                   QuitDecodeThreadEvent quit_decode_thread_event, ErrorDataInput error_data_input,
                                   DecryptDataCallback decrypt_callback)
        : sample_rates_(sample_rate),
          frame_size_(frame_size),
          pre_frame_time_stamp_(0),
          channels_(channels),
          is_rtp_real_time_decrypt_(is_rtp_real_time_decrypt),
          raw_data_callback_(raw_data_callback),
          last_frame_finished_(last_frame_finished),
          quit_decode_thread_event_(quit_decode_thread_event),
          error_data_input_(error_data_input),
          decrypt_callback_(decrypt_callback) {
    normal_decode_size_ = sample_rates_ * frame_size_ / 1000;
    decoded_buffer_data_ = new int16_t[normal_decode_size_];
    pcm_raw_data_size_ = channels * sample_rate / 1000 * frame_size * sizeof(int16_t);
    pcm_raw_data_ = new uint8_t[pcm_raw_data_size_];
    e2ed_ = std::make_shared<E2edIos>(this, decrypt_callback_);
    Init();
}
#endif

void AudioCodecBuffer::Init() {
#if (WEBRTC_ANDROID)
  LzLogger::setFileLog("", "file_logger");
#elif (WEBRTC_WIN)
  LzLogger::setFileLog("", "file_logger");
#endif

  decoder_ = webrtc::AudioDecoderOpus::MakeAudioDecoder(channels_, sample_rates_);
  sync_decoding_ = true;
  input_ring_buffer_ = new DUGON::RingBuffer(1024 * 4, "ringbuffer");
  is_of_end_ = false;
  correct_error_count_ = 0;

  pthread_t thread_process;
  pthread_attr_t attr;
  pthread_attr_init(&attr);
  pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
  pthread_create(&thread_process, &attr, &DecodeThreadWrapper, this);
}

AudioCodecBuffer::~AudioCodecBuffer() {
#ifdef WEBRTC_ANDROID
  JNIEnv *env = nullptr;
  if (jvm_->GetEnv((void **) &env, JNI_VERSION_1_6) == JNI_OK) {
    env->DeleteGlobalRef(jaudio_buffer_obj_);
    jaudio_buffer_obj_ = nullptr;
  }
#endif

#ifdef WEBRTC_IOS
  delete pcm_raw_data_;
  pcm_raw_data_ = nullptr;
#endif

  if (input_ring_buffer_) {
    delete input_ring_buffer_;
    input_ring_buffer_ = nullptr;
  }

  if (decoder_) {
    delete decoder_;
    decoder_ = nullptr;
  }

#ifdef WEBRTC_ANDROID
  direct_buffer_address_ = nullptr;
#endif

  if (decoded_buffer_data_) {
    delete decoded_buffer_data_;
    decoded_buffer_data_ = nullptr;
  }
}

bool AudioCodecBuffer::SendCodecBuffer(const uint8_t *data, int length, bool is_end) {
  if (!sync_decoding_) {
    return false;
  }

  std::unique_lock<std::mutex> lk(mutex_);
  DUGON::RingBufferErrorType type = input_ring_buffer_->Write(data, length);
  is_of_end_ = is_end;
  if (is_end) {
    input_ring_buffer_->MarkEnd();
  }
  bool res = (type == DUGON::kNoError);
  if (res || is_end) {
    cv_.notify_one();
  }
  return res;
}

void *AudioCodecBuffer::DecodeThreadWrapper(void *handle) {
  AudioCodecBuffer *handler = (AudioCodecBuffer *) handle;
  handler->DecodeThread();
  return nullptr;
}

#ifdef WEBRTC_ANDROID

void AudioCodecBuffer::SetCacheDirectBuffer(JNIEnv *env, jobject byte_buffer) {
  direct_buffer_address_ = env->GetDirectBufferAddress(byte_buffer);
}

#endif

void AudioCodecBuffer::StopDecoder() {
  if (sync_decoding_) {
    std::unique_lock<std::mutex> lk(mutex_);
    sync_decoding_ = false;
    cv_.notify_one();
  }
}

void AudioCodecBuffer::ReadFrameHeader(uint16_t *encoder_length, uint32_t *frame_size,
                                       uint32_t *sync_header) {
  std::unique_lock<std::mutex> lk(mutex_);
  uint8_t opus_header[OPUS_HEADER_LENGTH];
  input_ring_buffer_->Read(opus_header, OPUS_HEADER_LENGTH);
  ParseOpusHeader(opus_header, encoder_length, frame_size, sync_header);
}

void AudioCodecBuffer::ReadFramePayload(uint8_t *encoder_data, uint16_t encoder_length) {
  std::unique_lock<std::mutex> lk(mutex_);
  input_ring_buffer_->Read(encoder_data, encoder_length);
}

int AudioCodecBuffer::DecoderPerFrame(uint32_t &packet_size, uint32_t &empty_packet_number) {
  uint16_t encoder_length;
  uint32_t sync_header;
  uint32_t frame_timestamp;
  ReadFrameHeader(&encoder_length, &frame_timestamp, &sync_header);
  packet_size = encoder_length;

  if (encoder_length == 0) {
    empty_packet_number = 0;
    return 0;
  }

  if ((frame_timestamp > pre_frame_time_stamp_) &&
      (frame_timestamp - pre_frame_time_stamp_) >= frame_size_ * 2) {
    empty_packet_number = ((frame_timestamp - pre_frame_time_stamp_) / frame_size_) - 1;
  } else {
    empty_packet_number = 0;
  }
  pre_frame_time_stamp_ = frame_timestamp;

  uint8_t encoder_data[encoder_length];
  ReadFramePayload(encoder_data, encoder_length);
  auto [final_data, final_length] = Decrypt(e2ed_, is_rtp_real_time_decrypt_, encoder_data,
                                            encoder_length);
  if (final_data == nullptr) {
    packet_size = -1;
    logE(TAG, "e2edResult is null");
    return -1;
  }
  uint32_t duration = decoder_->PacketDuration(final_data.get(), final_length);
  uint32_t decoded_buffer_size = duration * channels_;
  if (decoded_buffer_size == normal_decode_size_) {
    return decoder_->DecodeInternal(final_data.get(), final_length, sample_rates_,
                                    decoded_buffer_data_);
  }
  packet_size = -1;
  return -1;
}

DecryptRet AudioCodecBuffer::Decrypt(std::shared_ptr<Decryptor> e2ed, bool is_rtp_real_time_decrypt,
                                     uint8_t *encoded, uint16_t length) {
  // logE(TAG, "Decrypt encoded:" + PrintUtil::PrintArray(encoded, length));
  if (is_rtp_real_time_decrypt) {
    return e2ed->OnDecrypt(encoded, length);
  }
  std::shared_ptr<uint8_t> data(encoded, [](auto p) {});
  return {data, length};
}

#ifdef WEBRTC_ANDROID

JNIEnv *AudioCodecBuffer::AttachCurrentThread(bool &is_attach) {
  is_attach = false;
  JNIEnv *env = nullptr;
  if (jvm_ == NULL) {
    logE(TAG, FUNC + "gJavaVm null");
    return nullptr;
  }
  jint ret = jvm_->GetEnv(reinterpret_cast<void **>(&env),
                          JNI_VERSION_1_6);
  if (ret == JNI_EDETACHED || !env) {
    JavaVMAttachArgs args;
    args.version = JNI_VERSION_1_6;
    args.group = nullptr;
    // 16 is the maximum size for thread names on Android.
    char thread_name[16];
    int err = prctl(PR_GET_NAME, thread_name);
    if (err < 0) {
      args.name = nullptr;
    } else {
      args.name = thread_name;
    }
    ret = jvm_->AttachCurrentThread(&env, &args);
    if (ret != 0) {
      logE(TAG, FUNC + "AttachCurrentThread fail");
      return nullptr;
    }
    is_attach = true;
  }
  return env;
}

#endif

void AudioCodecBuffer::DecodeThread() {
#ifdef WEBRTC_ANDROID
  bool is_attach;
  JNIEnv *env = AttachCurrentThread(is_attach);
#endif
  while (sync_decoding_) {
    uint32_t correct_error_count = 0;
    while (!input_ring_buffer_->FindSyncHeader(&correct_error_count) &&
           sync_decoding_) {
      std::unique_lock<std::mutex> lk(mutex_);
      correct_error_count_ += correct_error_count;
      if (correct_error_count_ > correct_error_count_threshold_) {
#ifdef WEBRTC_ANDROID
        env->CallVoidMethod(jaudio_buffer_obj_, input_data_error_);
#endif

#ifdef WEBRTC_IOS
        (*error_data_input_)(this, nullptr);
#endif
        sync_decoding_ = false;
        break;
      }

      if (!is_of_end_) {
        cv_.wait(lk);
      } else {
#ifdef WEBRTC_ANDROID
        env->CallVoidMethod(jaudio_buffer_obj_, last_frame_finished_);
#endif

#ifdef WEBRTC_IOS
        (*last_frame_finished_)(this);
#endif
        sync_decoding_ = false;
        break;
      }
    }


    if (!sync_decoding_) {
      break;
    }

    if (correct_error_count > 0) {
      correct_error_count_ += correct_error_count;
      if (correct_error_count_ > correct_error_count_threshold_) {
#ifdef WEBRTC_ANDROID
        env->CallVoidMethod(jaudio_buffer_obj_, input_data_error_);
#endif

#ifdef WEBRTC_IOS
        (*error_data_input_)(this, nullptr);
#endif
        sync_decoding_ = false;
        break;
      }
    }

    correct_error_count_ = 0;
    uint32_t packet_size;
    uint32_t empty_packet_count;
    if (DecoderPerFrame(packet_size, empty_packet_count) > 0) {
      if (empty_packet_count > 0) {
#ifdef WEBRTC_ANDROID
        memset((uint8_t *) direct_buffer_address_, 0, normal_decode_size_ * sizeof(int16_t));
#endif

#ifdef WEBRTC_IOS
        memset((uint8_t *)pcm_raw_data_, 0, normal_decode_size_ * sizeof(int16_t));
#endif

        while (empty_packet_count > 0) {
#ifdef  WEBRTC_ANDROID
          env->CallVoidMethod(jaudio_buffer_obj_, pcm_callback_);
#endif

#ifdef WEBRTC_IOS
          (*raw_data_callback_)(this, pcm_raw_data_, pcm_raw_data_size_, packet_size + OPUS_HEADER_LENGTH);
#endif
          empty_packet_count--;
        }
      }

      if (packet_size > 0) {
#ifdef WEBRTC_ANDROID
        memcpy((uint8_t *) direct_buffer_address_, (uint8_t *) decoded_buffer_data_,
               normal_decode_size_ * sizeof(int16_t));
        env->CallVoidMethod(jaudio_buffer_obj_, pcm_callback_);
#endif

#ifdef WEBRTC_IOS
        memcpy((uint8_t *)pcm_raw_data_, (uint8_t *) decoded_buffer_data_,
           normal_decode_size_ * sizeof(int16_t));
        (*raw_data_callback_)(this, pcm_raw_data_, pcm_raw_data_size_, packet_size + OPUS_HEADER_LENGTH);
#endif

      }
    } else if (packet_size < 0) {
#ifdef WEBRTC_ANDROID
      env->CallVoidMethod(jaudio_buffer_obj_, input_data_error_);
#endif

#ifdef WEBRTC_IOS
      (*error_data_input_)(this, nullptr);
#endif

      sync_decoding_ = false;
      break;
    }
  }

#ifdef WEBRTC_ANDROID
  env->CallVoidMethod(jaudio_buffer_obj_, quit_decode_thread_);
  if (is_attach) {
    jvm_->DetachCurrentThread();
  }
#endif

#ifdef WEBRTC_IOS
  (*quit_decode_thread_event_)(this);
#endif
}

void AudioCodecBuffer::ParseOpusHeader(uint8_t *opus_header, uint16_t *codec_length,
                                       uint32_t *frame_size, uint32_t *sync_header) {
  *sync_header = (((uint32_t) opus_header[0]) << 24) + ((((uint32_t) opus_header[1])) << 16) +
                 (((uint32_t) opus_header[2]) << 8) + ((uint32_t) opus_header[3]);
  *codec_length = (((uint16_t) opus_header[5]) << 8) + ((uint16_t) opus_header[6]);
  *frame_size = (((uint32_t) opus_header[8]) << 24) + ((((uint32_t) opus_header[9])) << 16) +
                (((uint32_t) opus_header[10]) << 8) + ((uint32_t) opus_header[11]);

}

unsigned int AudioCodecBuffer::GetUnWriteSize() {
  if (input_ring_buffer_) {
    return input_ring_buffer_->GetUnwriteSize();
  }
  return 0;
}

int AudioCodecBuffer::IsLatestElementsEqual(const std::vector<FrameInfo> &frame_infos) {
  if (frame_infos.size() >= AUDIO_OPUS_MIN_FRAMES) {
    // 比较最新的两个元素是否相同
    const FrameInfo &current_element = frame_infos[frame_infos.size() - 2];
    const FrameInfo &next_element = frame_infos.back();
    uint32_t est_next_frame_pos =
      current_element.start_pos + OPUS_HEADER_LENGTH + current_element.payload_length;
    if (current_element.channel == next_element.channel &&
        current_element.sample_rate == next_element.sample_rate &&
        current_element.sync_header == next_element.sync_header &&
        est_next_frame_pos == next_element.start_pos) {
      logW(TAG, FUNC + "verify pass");
      return 1;
    }
  }
  return 0;
}

BufferProbeType AudioCodecBuffer::ProbeBuffer(const uint8_t *probe_data, uint32_t data_size,
                                              uint32_t *channel, uint32_t *sample_rate,
                                              uint32_t *frame_size, bool is_end) {
#if (WEBRTC_ANDROID)
  LzLogger::setFileLog("", "file_logger");
#elif (WEBRTC_WIN)
  LzLogger::setFileLog("", "file_logger");
#endif
  uint16_t payload_length = 0;
  uint32_t sync_header = 0;
  uint32_t time_stamp = 0;
  std::vector<FrameInfo> frame_infos;
  for (int frame_pos = 0; frame_pos < data_size; frame_pos++) {
    if (!probe_data || frame_pos + OPUS_HEADER_LENGTH > data_size) { // 保障有足够的数据进行头解析
      if (is_end || data_size > AUDIO_OPUS_MAX_PROBE_BYTE) { // 文件已读完 / 已达限定探测字节数
        logE(TAG, "end but no match");
        return Error;
      }
      return NoEnoughData;
    }
    ParseOpusHeader(const_cast<uint8_t *>(&probe_data[frame_pos]), &payload_length, channel,
                    sample_rate, &time_stamp, &sync_header);
    if (sync_header != SYNC_HEADER) {
      // logE(TAG, "header no match");
      continue;
    }
    if (data_size < (frame_pos + OPUS_HEADER_LENGTH + payload_length)) { // 保障足够当前帧的数据大小
      // logE(TAG, "ProbeBuffer NoEnoughData:" + PrintUtil::PrintArray(probe_data, data_size));
      if (is_end || data_size > AUDIO_OPUS_MAX_PROBE_BYTE) { // 文件已读完 / 已达限定探测字节数
        logE(TAG, "end but noEnoughData");
        return Error;
      }
      return NoEnoughData;
    }

    FrameInfo info;
    info.channel = *channel;
    info.payload_length = payload_length;
    info.sample_rate = *sample_rate;
    info.sync_header = sync_header;
    info.start_pos = frame_pos;
    info.time_stamp = time_stamp;
    frame_infos.push_back(info);
    if (IsLatestElementsEqual(frame_infos)) {
      uint32_t duration = (frame_infos[frame_infos.size() - 1].time_stamp -
                           frame_infos[frame_infos.size() - 2].time_stamp) * (*sample_rate / 1000);
      *frame_size = duration * *channel * 1000 / *sample_rate;
      return Ok;
    }

    // 找到头后跳帧，一方面提高效率，另一方面避免伪头影响. 此处减1是因为1帧长度已包含当前frame_pos的位置，应去重
    frame_pos += OPUS_HEADER_LENGTH + payload_length - 1;
  }
  return Error;
}

void AudioCodecBuffer::ParseOpusHeader(uint8_t *opus_header, uint16_t *codec_length,
                                       uint32_t *channel, uint32_t *sample_rate,
                                       uint32_t *frame_size, uint32_t *sync_header) {
  *sync_header = (((uint32_t) opus_header[0]) << 24) + ((((uint32_t) opus_header[1])) << 16) +
                 (((uint32_t) opus_header[2]) << 8) + ((uint32_t) opus_header[3]);
  *codec_length = (((uint16_t) opus_header[5]) << 8) + ((uint16_t) opus_header[6]);
  *channel = (((uint32_t) opus_header[4]) >> 4) & 0x3;
  uint32_t sample_index = ((uint32_t) opus_header[4]) & 0xf;
  *sample_rate = GetSampleRate(sample_index);
  *frame_size = (((uint32_t) opus_header[8]) << 24) + ((((uint32_t) opus_header[9])) << 16) +
                (((uint32_t) opus_header[10]) << 8) + ((uint32_t) opus_header[11]);
}

uint32_t AudioCodecBuffer::GetSampleRate(uint32_t sample_rate_index) {
  uint32_t sample_rate = 0;
  switch (sample_rate_index) {
    case 1:
      sample_rate = 8000;
      break;
    case 2:
      sample_rate = 16000;
      break;
    case 3:
      sample_rate = 32000;
      break;
    case 4:
      sample_rate = 48000;
      break;
    case 5:
      sample_rate = 64000;
      break;
    case 6:
      sample_rate = 96000;
      break;
    case 7:
      sample_rate = 128000;
      break;
    case 8:
      sample_rate = 256000;
      break;
    default:
      break;
  }
  return sample_rate;
}

}
