/*
 *  Copyright (c) 2012 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#ifndef API_AUDIO_CODECS_AUDIO_DECODER_H_
#define API_AUDIO_CODECS_AUDIO_DECODER_H_

#include <stddef.h>
#include <stdint.h>

#include <memory>
#include <vector>

namespace webrtc {

class AudioDecoder {
 public:

  AudioDecoder() {};

  virtual ~AudioDecoder() {};

  virtual int PacketDuration(const uint8_t *encoded, size_t encoded_len) const = 0;

  virtual int SampleRateHz() const = 0;

  virtual size_t Channels() const = 0;

  virtual int DecodeInternal(const uint8_t *encoded, size_t encoded_len,
                             int sample_rate_hz, int16_t *decoded) = 0;

};

}  // namespace webrtc
#endif  // API_AUDIO_CODECS_AUDIO_DECODER_H_
