/*
 *  Copyright (c) 2017 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#include <memory>
#include <utility>
#include <vector>

#include "lizhiaudiocore/audio_codecs/opus/audio_decoder_opus.h"
#include "lizhiaudiocore/audio_codecs/opus/audio_decoder_opus_impl.h"

namespace webrtc {
AudioDecoder *AudioDecoderOpus::MakeAudioDecoder(
  int32_t channels, int32_t sample_rate) {
  return new AudioDecoderOpusImpl(channels, sample_rate);
}

}  // namespace webrtc
