/*
 *  Copyright (c) 2015 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#include "lizhiaudiocore/audio_codecs/opus/audio_decoder_opus_impl.h"
#include <memory>
#include <utility>

namespace webrtc {
AudioDecoderOpusImpl::AudioDecoderOpusImpl(size_t num_channels, int sample_rate)
  : channels_(num_channels),
    sample_rate_(sample_rate) {
  WebRtcOpus_DecoderCreate(&dec_state_, channels_);
  WebRtcOpus_DecoderInit(dec_state_);
}

AudioDecoderOpusImpl::~AudioDecoderOpusImpl() {
  WebRtcOpus_DecoderFree(dec_state_);
}

int AudioDecoderOpusImpl::DecodeInternal(const uint8_t *encoded,
                                         size_t encoded_len, int sample_rate_hz,
                                         int16_t *decoded) {
  int16_t temp_type = 1;  // Default is speech.
  int ret =
    WebRtcOpus_Decode(dec_state_, encoded, encoded_len, decoded, &temp_type);
  if (ret > 0)
    ret *= static_cast<int>(channels_);  // Return total number of samples.
  return ret;
}

int AudioDecoderOpusImpl::PacketDuration(const uint8_t *encoded,
                                         size_t encoded_len) const {
  return WebRtcOpus_DurationEst(dec_state_, encoded, encoded_len);
}

int AudioDecoderOpusImpl::SampleRateHz() const { return sample_rate_; }

size_t AudioDecoderOpusImpl::Channels() const { return channels_; }

}  // namespace webrtc
