//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/10/7.
//
#pragma once
#ifndef TEKIPLAYER_PRINT_UTIL_H
#define TEKIPLAYER_PRINT_UTIL_H

#include <sstream>
#include <iomanip>
#include <string.h>
#include <stdint.h>

class PrintUtil {
 public:
  PrintUtil() {}

  ~PrintUtil() {}

  static std::string PrintArray(const uint8_t *data, int length) {
    // return "";
    std::ostringstream oss;
    oss << "len=" << length << " data ";
    for (int i = 0; i < length; i++) {
      oss << std::hex << std::setw(2) << std::setfill('0')
          << static_cast<unsigned int>(data[i]) << " ";
    }
    return oss.str();
  }
};

#endif //TEKIPLAYER_PRINT_UTIL_H
