#include "lizhiaudiocore/common/util/lz_logger.h"

#if (WEBRTC_ANDROID)

#include "spdlog/sinks/android_sink.h"

#endif

#include "spdlog/spdlog.h"
#include "spdlog/sinks/rotating_file_sink.h"
#include <chrono>

namespace LzLogger {

#ifdef WEBRTC_ANDROID
std::shared_ptr<spdlog::logger> android_logger = nullptr;
#endif

void setLogLevel(LogLevel level) {
  switch (level) {
    case LOG_LEVEL_OFF:
      spdlog::set_level(spdlog::level::off);
      break;
    case LOG_LEVEL_VERBOSE:
      spdlog::set_level(spdlog::level::trace);
    case LOG_LEVEL_DEBUG:
      spdlog::set_level(spdlog::level::debug);
      break;
    case LOG_LEVEL_INFO:
      spdlog::set_level(spdlog::level::info);
      break;
    case LOG_LEVEL_WARNING:
      spdlog::set_level(spdlog::level::warn);
      break;
    case LOG_LEVEL_ERROR:
      spdlog::set_level(spdlog::level::err);
      break;
    case LOG_LEVEL_FATAL:
      spdlog::set_level(spdlog::level::critical);
      break;
    default:
      break;
  }
}

#ifdef WIN32
void setFileLog(std::wstring filePath, std::string fileLogName, bool truncate /*= true*/)
{
  std::shared_ptr<spdlog::logger> file_logger = spdlog::get(fileLogName);
  if (file_logger != nullptr) {
    spdlog::drop(fileLogName);
  }
  // TODO ivantsui
//	file_logger = spdlog::rotating_logger_st(fileLogName, filePath, 1048576 * 50, 2);
  spdlog::set_default_logger(file_logger);
  spdlog::flush_every(std::chrono::seconds(3));
}
#else

void setFileLog(std::string filePath, std::string fileLogName, bool truncate /*= true*/) {
#ifdef WEBRTC_ANDROID
  if (!android_logger) {
    android_logger = spdlog::android_logger_mt("android");
    android_logger->set_pattern("%v");
  }
#endif
  std::shared_ptr<spdlog::logger> file_logger = spdlog::get(fileLogName);
  if (file_logger != nullptr) {
    spdlog::drop(fileLogName);
  }
  // TODO ivantsui
//	file_logger = spdlog::rotating_logger_st(fileLogName, filePath, 1048576 * 50, 2);
  spdlog::set_default_logger(file_logger);
  spdlog::flush_every(std::chrono::seconds(5));
}

#endif

void log(int level, const char *tag, std::string msg) {
  if (!android_logger) {
    return;
  }
  std::string content = " [" + std::string(tag) + "]: " + msg;
  switch (level) {
    case LOG_LEVEL_DEBUG:
#if (WEBRTC_ANDROID)
      android_logger->debug(content);
#elif (WEBRTC_IOS)
      printf(content)
#elif (WEBRTC_WIN)
      spdlog::debug(content);
#endif
      break;
    case LOG_LEVEL_INFO:
#if (WEBRTC_ANDROID)
      android_logger->info(content);
#elif (WEBRTC_IOS)
      printf(content)
#elif (WEBRTC_WIN)
      spdlog::info(content);
#endif
      break;
    case LOG_LEVEL_WARNING:
#if (WEBRTC_ANDROID)
      android_logger->warn(content);
#elif (WEBRTC_IOS)
      printf(content)
#elif (WEBRTC_WIN)
      spdlog::warn(content);
#endif
      break;
    case LOG_LEVEL_ERROR:
#if (WEBRTC_ANDROID)
      android_logger->error(content);
#elif (WEBRTC_IOS)
      printf(content)
#elif (WEBRTC_WIN)
      spdlog::error(content);
#endif
      break;
    case LOG_LEVEL_FATAL:
#if (WEBRTC_ANDROID)
      android_logger->critical(content);
#elif (WEBRTC_IOS)
      printf(content)
#elif (WEBRTC_WIN)
      spdlog::critical(content);
#endif
      break;
    default:
      break;
  }
}

} // namespace LzLogger
