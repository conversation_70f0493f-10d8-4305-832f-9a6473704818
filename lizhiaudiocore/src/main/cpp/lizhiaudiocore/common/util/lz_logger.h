#ifndef LZ_LOGGER_HPP
#define LZ_LOGGER_HPP

#include <iostream>
#include <string>

namespace LzLogger {
#define FUNC (std::string(__FUNCTION__) + " ")
#ifdef ANDROID
enum LogLevel {
  LOG_LEVEL_VERBOSE,
  LOG_LEVEL_DEBUG,
  LOG_LEVEL_INFO,
  LOG_LEVEL_WARNING,
  LOG_LEVEL_ERROR,
  LOG_LEVEL_FATAL,
  LOG_LEVEL_OFF,
};

void setLogLevel(LogLevel level);

void setFileLog(std::string filePath, std::string fileLogName, bool truncate = true);

void log(int level, const char *tag, std::string msg);

#define logI(tag, msg) LzLogger::log(LzLogger::LOG_LEVEL_INFO, tag, msg)
#define logE(tag, msg) LzLogger::log(LzLogger::LOG_LEVEL_ERROR, tag, msg)
#define logD(tag, msg) LzLogger::log(LzLogger::LOG_LEVEL_DEBUG, tag, msg)
#define logW(tag, msg) LzLogger::log(LzLogger::LOG_LEVEL_WARNING, tag, msg)
// #define logI(tag, msg)
// #define logE(tag, msg)
// #define logD(tag, msg)
// #define logW(tag, msg)

#define logE_rds(tag, errId, msg) { logE(tag, msg); rds_->event_error(errId, msg);}
#endif

#ifdef __APPLE__
#define LOG(level, tag, msg) fprintf(stderr,"[%s] [%s:%d] %s: %s\n", level, __FILE__, __LINE__, tag, msg)
static void log(const char* level, const std::string& tag, const std::string& msg) {
  LOG(level, tag.c_str(), msg.c_str());
}
#define logI(tag, msg) LzLogger::log("INFO", tag, msg)
#define logE(tag, msg) LzLogger::log("ERROR", tag, msg)
#define logD(tag, msg) LzLogger::log("DEBUG", tag, msg)
#define logW(tag, msg) LzLogger::log("WARNING", tag, msg)
#endif

}
#endif
