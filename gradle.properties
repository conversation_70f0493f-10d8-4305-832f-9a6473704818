# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
#org.gradle.jvmargs=-Xmx2048m
# When configured, <PERSON><PERSON><PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
org.gradle.parallel=true
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
android.useAndroidX=true
android.enableJetifier=true
POM_DESCRIPTION=TekiPlayeræ­æ¾å¨
POM_GROUP=com.lizhi.component.base
VERSION_NAME=0.3.8
NEXUS_USERNAME=hubujun
NEXUS_PASSWORD=5af2b93ce6374653b6a396aac8de7cfc
android.injected.testOnly=false
#org.gradle.unsafe.configuration-cache=true
org.gradle.jvmargs=-Dkotlin.daemon.jvm.options=--illegal-access=permit