@file:Suppress("unused")

package com.lizhi.component.tekiplayer.okhttp

import android.content.Context
import android.net.Uri
import android.os.Bundle
import com.lizhi.component.tekiplayer.datasource.*
import com.lizhi.component.tekiplayer.datasource.DataReader.Companion.READ_END_OF_INPUT
import com.lizhi.component.tekiplayer.datasource.DataReader.Companion.READ_ERROR
import com.lizhi.component.tekiplayer.datasource.exception.HttpDataSourceException
import com.lizhi.component.tekiplayer.datasource.exception.IllegalContentTypeException
import com.lizhi.component.tekiplayer.datasource.exception.InvalidResponseCodeException
import com.lizhi.component.tekiplayer.datasource.exception.OutOfRangeException
import com.lizhi.component.tekiplayer.okhttp.prebuild.BuildConnectionProcessor
import com.lizhi.component.tekiplayer.util.TekiLog
import okhttp3.Request
import okhttp3.Response
import okhttp3.internal.closeQuietly
import okhttp3.internal.headersContentLength
import java.io.EOFException
import java.io.IOException
import java.io.InputStream

/**
 * 文件名：OkHttpDataSource
 * 作用：OkHTTP请求数据源
 * 作者：huangtianhao
 * 创建日期：2021/2/22
 */
class OkHttpDataSource(
    context: Context,
    url: String,
    override var dataSourceCallback: DataSourceCallback?,
    override val strategy: DataSourceStrategy
) : BaseDataSource(context, url, dataSourceCallback, strategy) {

    class OkHttpDataSourceFactory : BaseFactory() {
        override fun create(uri: Uri, extraData: Bundle?): DataSource {
            return OkHttpDataSource(
                checkNotNull(context),
                this.url ?: uri.toString(),
                this.dataSourceCallback,
                this.strategy
            )
        }
    }

    companion object {
        private const val TAG = "OkHttpDataSource"
    }

    private var bytesRemaining = -1L
    private var response: Response? = null
    private var inputStream: InputStream? = null

    override var contentLength: Long? = null

    override var responseHeaders: Map<String, List<String>>? = null
        private set

    private val okHttpClient = BuildConnectionProcessor.getOkHttpClientInstance(strategy)

    override fun open(range: Range): Boolean {
        // 参数校验
        if (!checkArgs(range)) {
            return false
        }

        close()

        TekiLog.i(TAG, "open $range")

        return try {
            internalOpen(range) >= 0
        } catch (e: Exception) {
            TekiLog.e(TAG, e.message ?: "", e)
            dataSourceCallback?.onErrorOccurred(e)
            false
        }
    }

    private fun internalOpen(range: Range): Long {
        val response = try {
            val request: Request = Request.Builder()
                .header("Range", range.getRangeRequestStr())
                .header("Connection", "Keep-Alive")
                .header("User-Agent", strategy.userAgent)
                .apply {
                    strategy.extraRequestProperties.forEach {
                        header(it.key, it.value)
                    }
                }
                .get()
                .url(originUrl)
                .build()

            okHttpClient.newCall(request).execute().also {
                this.response = it
            }
        } catch (e: Exception) {
            throw HttpDataSourceException(cause = e)
        }


        response.run {
            val responseCode = response.code

            val responseMessage = try {
                "header: ${response.headers} msg: ${response.message}"
            } catch (e: Exception) {
                throw HttpDataSourceException(cause = e)
            }

            responseHeaders = this.headers.toMultimap()

            if (responseCode < 200 || responseCode > 299) {
                if (responseCode == 416) {
                    throw OutOfRangeException(originUrl, range)
                }
                throw InvalidResponseCodeException(
                    originUrl,
                    responseCode,
                    responseMessage
                )
            }

            val contentTypeString = response.body?.contentType()?.toString() ?: ""
            if (strategy.rejectedContentType.find { contentTypeString.contains(it) } != null) {
                throw IllegalContentTypeException(originUrl, contentTypeString)
            }

            val contentLength = response.headersContentLength()
            <EMAIL> = contentLength + range.start
            val end = range.end

            val length = if (end == null) {
                if (contentLength == -1L) {
                    -1L
                } else {
                    contentLength
                }
            } else {
                end - range.start
            }.also {
                bytesRemaining = it
            }

            TekiLog.i(TAG, "bytesRemaining = $length")

            try {
                if (hasAesInfo() && !isRealTimeDecrypt()) {
                    <EMAIL> = aesComponent!!.convertDecryptInputStream(aesKey!!, aesIV!!, response.body?.byteStream()!!, localEncryptCache, plainCacheLength, saveEncryptDataCallback)
                    bytesRemaining += aesComponent!!.getUnUseCacheCount()
                    TekiLog.i(TAG, "aes bytesRemaining = $bytesRemaining")
                } else {
                    <EMAIL> = response.body?.byteStream()
                }
            } catch (e: Exception) {
                throw HttpDataSourceException(cause = e)
            }

            dataSourceCallback?.onReadyRead()

            return length
        }

    }


    override fun read(
        buffer: ByteArray,
        offset: Int,
        readLength: Int
    ): Int {
        return try {
            readInternal(buffer, offset, readLength)
        } catch (e: Exception) {
            TekiLog.e(TAG, e.message ?: "", e)
            dataSourceCallback?.onErrorOccurred(e)
            READ_ERROR
        }
    }

    @Throws(IOException::class)
    private fun readInternal(buffer: ByteArray, offset: Int, readLength: Int): Int {
        if (readLength == 0) {
            return 0
        }
        // TekiLog.i(TAG, "readInternal bytesRemaining $bytesRemaining")
        if (bytesRemaining == -1L || inputStream == null) {
            throw HttpDataSourceException(message = "open connection failed")
        }

        if (bytesRemaining == 0L) {
            dataSourceCallback?.onEndEncountered()
            return READ_END_OF_INPUT
        }

        val minOf = minOf(bytesRemaining.toInt(), readLength)
        val read = inputStream?.read(buffer, offset, minOf)
            ?: throw HttpDataSourceException(message = "null input stream, offset=$offset, len=$minOf")

        if (read == -1) {
            if (hasAesInfo()) {
                bytesRemaining = 0
            } else {
                // 正常不该出现的EOF，可能是bytesRemaining计算错误
                dataSourceCallback?.onEndEncountered()
                throw EOFException()
            }
        } else {
            bytesRemaining -= read.toLong()
        }

//        TekiLog.i(TAG, "after read bytes remaining = $bytesRemaining")
        return read
    }

    private fun checkArgs(range: Range): Boolean {
        if (range.start < 0) {
            this.dataSourceCallback?.onErrorOccurred(
                HttpDataSourceException(message = "illegal range ${range.start} < 0")
            )
            return false
        }
        if (originUrl.isBlank()) {
            this.dataSourceCallback?.onErrorOccurred(
                HttpDataSourceException(message = "url is blank")
            )
            return false
        }
        return true
    }


    override fun close() {
        try {
            inputStream?.close()
        } catch (ignore: Exception) {
        } finally {
            inputStream = null
            bytesRemaining = -1L
            try {
                response?.closeQuietly()
            } catch (e: Exception) {
                TekiLog.e(TAG, e.message ?: "", e)
            }
            response = null
        }
    }

    override fun getUrl(): String {
        return response?.request?.url?.toString() ?: originUrl
    }

    override fun updateDataSourceCallback(dataSourceCallback: DataSourceCallback) {
        this.dataSourceCallback = dataSourceCallback
    }
}