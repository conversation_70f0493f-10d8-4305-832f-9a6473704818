package com.lizhi.component.tekiplayer.okhttp.prebuild

import com.lizhi.component.tekiplayer.controller.cdn.CdnListHolder
import com.lizhi.component.tekiplayer.controller.cdn.CdnListListener
import com.lizhi.component.tekiplayer.datasource.DataSourceStrategy
import com.lizhi.component.tekiplayer.okhttp.prebuild.callback.PreConnectCallback
import com.lizhi.component.tekiplayer.util.TekiLog
import okhttp3.*
import okio.Timeout
import java.io.IOException
import java.net.InetAddress
import java.util.concurrent.TimeUnit

/**
 * 预建连的实现类
 */
object BuildConnectionProcessor {

    private const val TAG = "BuildConnectionProcessor"

    private var okHttpClient: OkHttpClient? = null

    private const val PRE_CONNECT_LIMIT = 5

    fun getOkHttpClientInstance(strategy: DataSourceStrategy): OkHttpClient {
        return okHttpClient
            ?: synchronized(this) {
                okHttpClient.let { it ->
                    return it ?: OkHttpClient.Builder().apply {
                        connectTimeout(strategy.connectTimeout.toLong(), TimeUnit.MILLISECONDS)
                        readTimeout(strategy.readTimeout.toLong(), TimeUnit.MILLISECONDS)
                        val dnsResolver = strategy.dnsResolver
                        if (dnsResolver != null && dnsResolver.isEnableCustomDns()) {
                            dns(object : Dns {
                                override fun lookup(hostname: String): List<InetAddress> {
                                    return try {
                                        dnsResolver.lookup(hostname).also {
                                            TekiLog.i(TAG, "lookup [hostname] $hostname result $it")
                                        }
                                    } catch (e: Throwable) {
                                        TekiLog.i(
                                            TAG,
                                            "failed to use custom dnsResolver, switch back to Dns.SYSTEM"
                                        )
                                        Dns.SYSTEM.lookup(hostname)
                                    }
                                }
                            })
                        }
                        eventListener(DnsEventListener(dnsResolver))
                    }.build().also { client ->
                        okHttpClient = client
                        CdnListHolder.addListener(object : CdnListListener {
                            override fun onCdnListChanged(cdnList: List<String>) {
                                TekiLog.i(TAG, "onCdnListChanged $cdnList")
                                try {
                                    for (cdn in cdnList) {
                                        val idleConnectionCount =
                                            client.connectionPool.idleConnectionCount()
                                        if (idleConnectionCount >= PRE_CONNECT_LIMIT) {
                                            TekiLog.w(
                                                TAG,
                                                "skip [cdn] $cdn preconnect, because [the idle number] $idleConnectionCount is exceed the limit $PRE_CONNECT_LIMIT"
                                            )
                                            return
                                        }
                                        buildConnection(client, cdn, object : PreConnectCallback {
                                            override fun connectCompleted(url: String) {
                                                TekiLog.i(TAG, "preConnect completed $url")
                                            }

                                            override fun connectFailed(t: Throwable) {
                                                TekiLog.e(TAG, "preConnect failed", t)
                                            }
                                        })
                                    }
                                } catch (e: Exception) {
                                    TekiLog.e(TAG, "preConnect forEach error", e)
                                }
                            }
                        })
                    }
                }
            }
    }

    @JvmField
    val NONE_CALL: Call = object : Call {
        override fun request(): Request {
            return null!!
        }

        @Throws(IOException::class)
        override fun execute(): Response {
            return null!!
        }

        override fun enqueue(responseCallback: Callback) {}

        override fun cancel() {}

        override fun isExecuted(): Boolean {
            return false
        }

        override fun isCanceled(): Boolean {
            return false
        }

        override fun timeout(): Timeout {
            return null!!
        }

        override fun clone(): Call {
            return null!!
        }
    }

    /**
     * 预建连
     */
    fun buildConnection(client: OkHttpClient, url: String, callback: PreConnectCallback?) {
        if (url.isBlank()) {
            callback?.connectFailed(IllegalArgumentException("Url is null."))
            return
        }

        if (client.connectionPool.idleConnectionCount() >= PRE_CONNECT_LIMIT) {
            TekiLog.d(TAG, "buildConnection: 空闲连接数达到5个")
            // 空闲连接数达到5个
            callback?.connectFailed(IllegalStateException("The idle connections reached the upper limit<5>."))
            return
        }

        val runnable = PreConnectRunnable(client, url, callback)
        client.dispatcher.executorService.execute(runnable)
    }
}