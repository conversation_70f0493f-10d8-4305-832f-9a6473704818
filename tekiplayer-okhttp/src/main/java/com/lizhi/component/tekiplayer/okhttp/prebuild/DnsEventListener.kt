package com.lizhi.component.tekiplayer.okhttp.prebuild

import com.lizhi.component.tekiplayer.controller.dns.DnsResolver
import com.lizhi.component.tekiplayer.util.TekiLog
import okhttp3.*
import java.io.IOException
import java.net.InetAddress
import java.net.InetSocketAddress
import java.net.Proxy

/**
 * 文件名：DnsEventListener
 * 作用：监听Http请求结果并向DNS组件反馈；
 * 参考大红袍接入DNS组件的实现，该逻辑其实更应该包装在DNS组件内部
 *  https://gitlab.lizhi.fm/Dahongpao/dahongpao_android/-/blob/54ff99d6bd702a0b88abde52f9eb1395c09da82b/base/src/main/java/com/lizhi/podcast/network/HttpListenerMonitor.kt
 * 作者：huangtianhao
 * 创建日期：2021/5/10
 */
class DnsEventListener(private val dnsResolver: DnsResolver?) : EventListener() {

    companion object {
        private const val TAG = "DnsEventListener"
    }

    private var callOnStartTime = 0L

    private var dnsStartTime = 0L
    private var dnsCostTime = 0L

    private var connStartTime = 0L
    private var connCostTime = 0L

    private var secureConnStartTime = 0L
    private var secureConnCostTime = 0L

    private var secureConnSuccess = false  //ssl连接是否成功

    private var connectionAcquireStartTime = 0L
    private var connectionAcquireCostTime = 0L

    private var requestHeadersStartTime = 0L
    private var requestHeadersCostTime = 0L

    private var requestBodyStartTime = 0L
    private var requestBodyCostTime = 0L

    private var responseHeadersStartTime = 0L
    private var responseHeadersCostTime = 0L

    private var responseBodyStartTime = 0L
    private var responseBodyCostTime = 0L

    private var callOnCostTime = 0L

    private var hostAddress: String? = null  //ip地址
    private var hostName: String? = null  //域名
    private var isConnSuccess = false

    override fun callStart(call: Call) {
        callOnStartTime = System.currentTimeMillis()
        super.callStart(call)
    }

    override fun dnsStart(
        call: Call,
        domainName: String
    ) {
        dnsStartTime = System.currentTimeMillis()
        hostName = domainName
        super.dnsStart(call, domainName)
    }

    override fun dnsEnd(
        call: Call,
        domainName: String,
        inetAddressList: List<InetAddress>
    ) {
        super.dnsEnd(call, domainName, inetAddressList)
        dnsCostTime = System.currentTimeMillis() - dnsStartTime
    }

    override fun connectStart(
        call: Call,
        inetSocketAddress: InetSocketAddress,
        proxy: Proxy
    ) {
        connStartTime = System.currentTimeMillis()
        super.connectStart(call, inetSocketAddress, proxy)
        //hostAddress 是ip地址
        hostAddress = inetSocketAddress.address.hostAddress
        TekiLog.d(
            TAG,
            "connectStart ip is ${inetSocketAddress.address.hostAddress}",
        )
    }

    override fun secureConnectStart(call: Call) {
        secureConnStartTime = System.currentTimeMillis()
        super.secureConnectStart(call)
    }

    override fun secureConnectEnd(
        call: Call,
        handshake: Handshake?
    ) {
        super.secureConnectEnd(call, handshake)
        secureConnSuccess = handshake != null //为null的话说明ssl失败，不为null说明成功
        secureConnCostTime =
            System.currentTimeMillis() - secureConnStartTime
        TekiLog.d(
            TAG,
            "secureConnectEnd secureConnSuccess is $secureConnSuccess,cost $secureConnCostTime"
        )
    }

    override fun connectEnd(
        call: Call,
        inetSocketAddress: InetSocketAddress,
        proxy: Proxy,
        protocol: Protocol?
    ) {
        super.connectEnd(call, inetSocketAddress, proxy, protocol)
        connCostTime = System.currentTimeMillis() - connStartTime

        isConnSuccess = true

        TekiLog.d(
            TAG,
            "connectSuccess ,cost is ${(connCostTime - secureConnCostTime).toInt()}"
        )
    }

    override fun connectFailed(
        call: Call,
        inetSocketAddress: InetSocketAddress,
        proxy: Proxy,
        protocol: Protocol?,
        ioe: IOException
    ) {
        super.connectFailed(call, inetSocketAddress, proxy, protocol, ioe)
        connCostTime = System.currentTimeMillis() - connStartTime

        //短链接的连接结束，上报短链接的打点
        isConnSuccess = false

        TekiLog.i(
            TAG,
            "connectFailed ,cost is ${(connCostTime - secureConnCostTime).toInt()}"
        )
    }

    override fun connectionAcquired(
        call: Call,
        connection: Connection
    ) {
        connectionAcquireStartTime = System.currentTimeMillis()
        super.connectionAcquired(call, connection)
    }

    override fun connectionReleased(
        call: Call,
        connection: Connection
    ) {
        super.connectionReleased(call, connection)
        connectionAcquireCostTime =
            System.currentTimeMillis() - connectionAcquireStartTime
    }

    override fun requestHeadersStart(call: Call) {
        requestHeadersStartTime = System.currentTimeMillis()
        super.requestHeadersStart(call)
    }

    override fun requestHeadersEnd(
        call: Call,
        request: Request
    ) {
        super.requestHeadersEnd(call, request)
        requestHeadersCostTime =
            System.currentTimeMillis() - requestHeadersStartTime
    }

    override fun requestBodyStart(call: Call) {
        requestBodyStartTime = System.currentTimeMillis()
        super.requestBodyStart(call)
    }

    override fun requestBodyEnd(
        call: Call,
        byteCount: Long
    ) {
        super.requestBodyEnd(call, byteCount)
        requestBodyCostTime =
            System.currentTimeMillis() - requestBodyStartTime
    }

    override fun responseHeadersStart(call: Call) {
        responseHeadersStartTime = System.currentTimeMillis()
        super.responseHeadersStart(call)
    }

    override fun responseHeadersEnd(
        call: Call,
        response: Response
    ) {
        super.responseHeadersEnd(call, response)
        responseHeadersCostTime =
            System.currentTimeMillis() - responseHeadersStartTime
    }

    override fun responseBodyStart(call: Call) {
        responseBodyStartTime = System.currentTimeMillis()
        super.responseBodyStart(call)
    }

    override fun responseBodyEnd(
        call: Call,
        byteCount: Long
    ) {
        super.responseBodyEnd(call, byteCount)
        responseBodyCostTime =
            System.currentTimeMillis() - responseBodyStartTime
    }

    override fun callEnd(call: Call) {
        super.callEnd(call)
        callOnCostTime = System.currentTimeMillis() - callOnStartTime

        dnsResolver?.mark(
            call.request().url.host,
            hostAddress ?: "",
            isConnSuccess,
            true,
            call.request().url.encodedPath,
            connCostTime,
            callOnCostTime
        )

        TekiLog.i(
            TAG,
            "callEnd, total cost time $callOnCostTime,hostName is $hostName,hostAddress is $hostAddress,dnsCostTime $dnsCostTime,secureConnSuccess is $secureConnSuccess,secureConnCostTime $secureConnCostTime, requestHeadersCostTime $requestHeadersCostTime,requestBodyCostTime $requestBodyCostTime,responseHeadersCostTime $responseHeadersCostTime,responseBodyCostTime $responseBodyCostTime"
        )
    }

    override fun callFailed(
        call: Call,
        ioe: IOException
    ) {
        super.callFailed(call, ioe)
        callOnCostTime = System.currentTimeMillis() - callOnStartTime
        dnsResolver?.mark(
            call.request().url.host,
            hostAddress ?: "",
            isConnSuccess,
            true,
            call.request().url.encodedPath,
            connCostTime,
            callOnCostTime
        )
    }

}