Pod::Spec.new do |s|
  s.name             = 'opus_decode'
  s.version          = '1.0.1'
  s.summary          = 'opus_decode'
  s.description      = <<-DESC
  Opus格式解码器
                       DESC

  s.homepage         = 'https://gitlab.lizhi.fm/audioTeam/opus_decode.git'
  s.license          = { :type => 'MIT', :file => 'LICENSE' }
  s.author           = { 'infra' => '<EMAIL>' }
  s.source           = { :git => '*******************:audioTeam/opus_decode.git', :tag => s.version.to_s }
 
  s.swift_version = '5.0'
  s.ios.deployment_target = '8.0'
  s.watchos.deployment_target = '7.0'
  s.swift_version = '5.0'

  # s.header_dir = 'audiobuffer'
  s.header_mappings_dir = 'lizhiaudiocore/src/main/cpp'

  s.libraries = 'c++'

  s.source_files = [
    'lizhiaudiocore/src/main/cpp/lizhiaudiocore/audio_buffer/audio_codec_buffer.h',
    'lizhiaudiocore/src/main/cpp/lizhiaudiocore/audio_buffer/audio_codec_buffer.cpp',
    'lizhiaudiocore/src/main/cpp/lizhiaudiocore/audio_buffer/e2ed/e2ed_ios.mm',
    'lizhiaudiocore/src/main/cpp/lizhiaudiocore/audio_buffer/e2ed/e2ed_ios.h',
    'lizhiaudiocore/src/main/cpp/lizhiaudiocore/audio_buffer/e2ed/e2ed.h',
    'lizhiaudiocore/src/main/cpp/lizhiaudiocore/audio_buffer/dugon/*.{h,cpp,cc,c}',
    'lizhiaudiocore/src/main/cpp/lizhiaudiocore/audio_buffer/utils/*.{h,cpp,cc,c}',
    'lizhiaudiocore/src/main/cpp/lizhiaudiocore/audio_codecs/**/*.{h,cpp,cc,c}',
    'lizhiaudiocore/src/main/cpp/lizhiaudiocore/common/util/lz_logger.h',
    'lizhiaudiocore/src/main/cpp/lizhiaudiocore/common/util/print_util.hpp',
    'lizhiaudiocore/src/main/cpp/third_party/opus/ios/include/*.{h}'
  ]
  s.vendored_libraries = 'lizhiaudiocore/src/main/cpp/third_party/opus/ios/lib/*.a'

  s.pod_target_xcconfig = {
    'DEFINES_MODULE' => 'YES',
    'CLANG_ENABLE_MODULES' => 'YES',
    'CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES' => 'YES',
    'OTHER_CFLAGS' => '-g -fstack-protector-all -Wall -Wextra -Wno-unused-parameter -Wstrict-aliasing -Wstrict-prototypes -fPIE -Wstack-protector -x objective-c',
    'OTHER_CPLUSPLUSFLAGS' => '-g -fexceptions -frtti -Wall -x objective-c++',
    'GCC_PREPROCESSOR_DEFINITIONS' => 'WEBRTC_IOS',
    'HEADER_SEARCH_PATHS' => '"${PODS_ROOT}/Headers/Public/opus_decode/third_party/opus/ios"',
  }
end
