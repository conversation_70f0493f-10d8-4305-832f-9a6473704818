//
//  iOSAppTests.m
//  iOSAppTests
//
//  Created by lidawen on 2023/3/24.
//

#import <XCTest/XCTest.h>
#import <lizhiaudiocore/audio_buffer/audio_codec_buffer.h>
#import <memory>
using namespace webrtc;

extern "C"{
    
static void OpusDecoderCallbackRawDataCallback(void *pThis, const uint8_t *data, uint32_t size, uint32_t encoderLength)
{
}

static void OpusDecoderCallbackLastFrameDecoded(void *pThis)
{
}

static void OpusDecoderCallbackQuitDecodeThreadEvent(void *pThis)
{
}

static void OpusDecoderCallbackErrorDataInput(void *pThis, const char *info)
{
}

static DecryptRet OpusDecoderCallbackDecryptDataCallback(void*, const uint8_t* data, uint16_t size)
{
}
    
}

@interface iOSAppTests : XCTestCase
{
    std::shared_ptr<AudioCodecBuffer> _audioCodecBuffer;
}

@end

@implementation iOSAppTests

- (void)setUp {
    _audioCodecBuffer = std::make_shared<AudioCodecBuffer>(1, 48000, 60, false,
                                                           OpusDecoderCallbackRawDataCallback,
                                                           OpusDecoderCallbackLastFrameDecoded,
                                                           OpusDecoderCallbackQuitDecodeThreadEvent,
                                                           OpusDecoderCallbackErrorDataInput,
                                                           OpusDecoderCallbackDecryptDataCallback);
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
}

- (void)testExample {
    // 这里写测试用例
}

- (void)testPerformanceExample {
    // This is an example of a performance test case.
    [self measureBlock:^{
        // Put the code you want to measure the time of here.
    }];
}

@end
