// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		2C8AF35729CD7ED90028A4B3 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 2C8AF35629CD7ED90028A4B3 /* AppDelegate.m */; };
		2C8AF35A29CD7ED90028A4B3 /* SceneDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 2C8AF35929CD7ED90028A4B3 /* SceneDelegate.m */; };
		2C8AF35D29CD7ED90028A4B3 /* ViewController.mm in Sources */ = {isa = PBXBuildFile; fileRef = 2C8AF35C29CD7ED90028A4B3 /* ViewController.mm */; };
		2C8AF36029CD7ED90028A4B3 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 2C8AF35E29CD7ED90028A4B3 /* Main.storyboard */; };
		2C8AF36229CD7EDA0028A4B3 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 2C8AF36129CD7EDA0028A4B3 /* Assets.xcassets */; };
		2C8AF36529CD7EDA0028A4B3 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 2C8AF36329CD7EDA0028A4B3 /* LaunchScreen.storyboard */; };
		2C8AF36829CD7EDA0028A4B3 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 2C8AF36729CD7EDA0028A4B3 /* main.m */; };
		2C8AF37529CD7F500028A4B3 /* iOSAppTests.mm in Sources */ = {isa = PBXBuildFile; fileRef = 2C8AF37429CD7F500028A4B3 /* iOSAppTests.mm */; };
		2C8AF37C29CD81DB0028A4B3 /* TestData in Resources */ = {isa = PBXBuildFile; fileRef = 2C8AF37B29CD81DB0028A4B3 /* TestData */; };
		2C8AF37D29CD81DB0028A4B3 /* TestData in Resources */ = {isa = PBXBuildFile; fileRef = 2C8AF37B29CD81DB0028A4B3 /* TestData */; };
		5402D2B3D86005525923F820 /* libPods-iOSAppTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 6026C965C45F6E371DFD6156 /* libPods-iOSAppTests.a */; };
		97408B69D3763DC0FF422777 /* libPods-iOSApp.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 8E6B8D15585171F29921D44E /* libPods-iOSApp.a */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		2C8AF37629CD7F500028A4B3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2C8AF34A29CD7ED90028A4B3 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2C8AF35129CD7ED90028A4B3;
			remoteInfo = iOSApp;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		074DAC0C78F7C18D6DB93361 /* Pods-iOSAppTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-iOSAppTests.debug.xcconfig"; path = "Target Support Files/Pods-iOSAppTests/Pods-iOSAppTests.debug.xcconfig"; sourceTree = "<group>"; };
		2C8AF35229CD7ED90028A4B3 /* iOSApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = iOSApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
		2C8AF35529CD7ED90028A4B3 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		2C8AF35629CD7ED90028A4B3 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		2C8AF35829CD7ED90028A4B3 /* SceneDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SceneDelegate.h; sourceTree = "<group>"; };
		2C8AF35929CD7ED90028A4B3 /* SceneDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SceneDelegate.m; sourceTree = "<group>"; };
		2C8AF35B29CD7ED90028A4B3 /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		2C8AF35C29CD7ED90028A4B3 /* ViewController.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ViewController.mm; sourceTree = "<group>"; };
		2C8AF35F29CD7ED90028A4B3 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		2C8AF36129CD7EDA0028A4B3 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		2C8AF36429CD7EDA0028A4B3 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		2C8AF36629CD7EDA0028A4B3 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		2C8AF36729CD7EDA0028A4B3 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		2C8AF37229CD7F500028A4B3 /* iOSAppTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = iOSAppTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		2C8AF37429CD7F500028A4B3 /* iOSAppTests.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = iOSAppTests.mm; sourceTree = "<group>"; };
		2C8AF37B29CD81DB0028A4B3 /* TestData */ = {isa = PBXFileReference; lastKnownFileType = folder; path = TestData; sourceTree = "<group>"; };
		6026C965C45F6E371DFD6156 /* libPods-iOSAppTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-iOSAppTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		7E180F607B2F2809BF43FA1B /* Pods-iOSAppTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-iOSAppTests.release.xcconfig"; path = "Target Support Files/Pods-iOSAppTests/Pods-iOSAppTests.release.xcconfig"; sourceTree = "<group>"; };
		8E6B8D15585171F29921D44E /* libPods-iOSApp.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-iOSApp.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		9E37553E41343BF279527834 /* Pods-iOSApp.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-iOSApp.release.xcconfig"; path = "Target Support Files/Pods-iOSApp/Pods-iOSApp.release.xcconfig"; sourceTree = "<group>"; };
		CA82B9E89D62F04CFBC9D88E /* Pods-iOSApp.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-iOSApp.debug.xcconfig"; path = "Target Support Files/Pods-iOSApp/Pods-iOSApp.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		2C8AF34F29CD7ED90028A4B3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				97408B69D3763DC0FF422777 /* libPods-iOSApp.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2C8AF36F29CD7F500028A4B3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5402D2B3D86005525923F820 /* libPods-iOSAppTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2C8AF34929CD7ED90028A4B3 = {
			isa = PBXGroup;
			children = (
				2C8AF35429CD7ED90028A4B3 /* iOSApp */,
				2C8AF37329CD7F500028A4B3 /* iOSAppTests */,
				2C8AF35329CD7ED90028A4B3 /* Products */,
				5823E0A7BB36787271382B09 /* Pods */,
				FB86A1A11653076DBBADB8C1 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		2C8AF35329CD7ED90028A4B3 /* Products */ = {
			isa = PBXGroup;
			children = (
				2C8AF35229CD7ED90028A4B3 /* iOSApp.app */,
				2C8AF37229CD7F500028A4B3 /* iOSAppTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		2C8AF35429CD7ED90028A4B3 /* iOSApp */ = {
			isa = PBXGroup;
			children = (
				2C8AF37B29CD81DB0028A4B3 /* TestData */,
				2C8AF35529CD7ED90028A4B3 /* AppDelegate.h */,
				2C8AF35629CD7ED90028A4B3 /* AppDelegate.m */,
				2C8AF35829CD7ED90028A4B3 /* SceneDelegate.h */,
				2C8AF35929CD7ED90028A4B3 /* SceneDelegate.m */,
				2C8AF35B29CD7ED90028A4B3 /* ViewController.h */,
				2C8AF35C29CD7ED90028A4B3 /* ViewController.mm */,
				2C8AF35E29CD7ED90028A4B3 /* Main.storyboard */,
				2C8AF36129CD7EDA0028A4B3 /* Assets.xcassets */,
				2C8AF36329CD7EDA0028A4B3 /* LaunchScreen.storyboard */,
				2C8AF36629CD7EDA0028A4B3 /* Info.plist */,
				2C8AF36729CD7EDA0028A4B3 /* main.m */,
			);
			path = iOSApp;
			sourceTree = "<group>";
		};
		2C8AF37329CD7F500028A4B3 /* iOSAppTests */ = {
			isa = PBXGroup;
			children = (
				2C8AF37429CD7F500028A4B3 /* iOSAppTests.mm */,
			);
			path = iOSAppTests;
			sourceTree = "<group>";
		};
		5823E0A7BB36787271382B09 /* Pods */ = {
			isa = PBXGroup;
			children = (
				CA82B9E89D62F04CFBC9D88E /* Pods-iOSApp.debug.xcconfig */,
				9E37553E41343BF279527834 /* Pods-iOSApp.release.xcconfig */,
				074DAC0C78F7C18D6DB93361 /* Pods-iOSAppTests.debug.xcconfig */,
				7E180F607B2F2809BF43FA1B /* Pods-iOSAppTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		FB86A1A11653076DBBADB8C1 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				8E6B8D15585171F29921D44E /* libPods-iOSApp.a */,
				6026C965C45F6E371DFD6156 /* libPods-iOSAppTests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		2C8AF35129CD7ED90028A4B3 /* iOSApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2C8AF36B29CD7EDA0028A4B3 /* Build configuration list for PBXNativeTarget "iOSApp" */;
			buildPhases = (
				85D9C7343028935EFA5B397C /* [CP] Check Pods Manifest.lock */,
				2C8AF34E29CD7ED90028A4B3 /* Sources */,
				2C8AF34F29CD7ED90028A4B3 /* Frameworks */,
				2C8AF35029CD7ED90028A4B3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = iOSApp;
			productName = iOSApp;
			productReference = 2C8AF35229CD7ED90028A4B3 /* iOSApp.app */;
			productType = "com.apple.product-type.application";
		};
		2C8AF37129CD7F500028A4B3 /* iOSAppTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2C8AF37A29CD7F500028A4B3 /* Build configuration list for PBXNativeTarget "iOSAppTests" */;
			buildPhases = (
				F472F8F4A401FFDEE7AA5A98 /* [CP] Check Pods Manifest.lock */,
				2C8AF36E29CD7F500028A4B3 /* Sources */,
				2C8AF36F29CD7F500028A4B3 /* Frameworks */,
				2C8AF37029CD7F500028A4B3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2C8AF37729CD7F500028A4B3 /* PBXTargetDependency */,
			);
			name = iOSAppTests;
			productName = iOSAppTests;
			productReference = 2C8AF37229CD7F500028A4B3 /* iOSAppTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2C8AF34A29CD7ED90028A4B3 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1420;
				TargetAttributes = {
					2C8AF35129CD7ED90028A4B3 = {
						CreatedOnToolsVersion = 14.2;
					};
					2C8AF37129CD7F500028A4B3 = {
						CreatedOnToolsVersion = 14.2;
						TestTargetID = 2C8AF35129CD7ED90028A4B3;
					};
				};
			};
			buildConfigurationList = 2C8AF34D29CD7ED90028A4B3 /* Build configuration list for PBXProject "iOSApp" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 2C8AF34929CD7ED90028A4B3;
			productRefGroup = 2C8AF35329CD7ED90028A4B3 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2C8AF35129CD7ED90028A4B3 /* iOSApp */,
				2C8AF37129CD7F500028A4B3 /* iOSAppTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		2C8AF35029CD7ED90028A4B3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2C8AF36529CD7EDA0028A4B3 /* LaunchScreen.storyboard in Resources */,
				2C8AF36229CD7EDA0028A4B3 /* Assets.xcassets in Resources */,
				2C8AF37C29CD81DB0028A4B3 /* TestData in Resources */,
				2C8AF36029CD7ED90028A4B3 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2C8AF37029CD7F500028A4B3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2C8AF37D29CD81DB0028A4B3 /* TestData in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		85D9C7343028935EFA5B397C /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-iOSApp-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		F472F8F4A401FFDEE7AA5A98 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-iOSAppTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		2C8AF34E29CD7ED90028A4B3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2C8AF35D29CD7ED90028A4B3 /* ViewController.mm in Sources */,
				2C8AF35729CD7ED90028A4B3 /* AppDelegate.m in Sources */,
				2C8AF36829CD7EDA0028A4B3 /* main.m in Sources */,
				2C8AF35A29CD7ED90028A4B3 /* SceneDelegate.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2C8AF36E29CD7F500028A4B3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2C8AF37529CD7F500028A4B3 /* iOSAppTests.mm in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		2C8AF37729CD7F500028A4B3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2C8AF35129CD7ED90028A4B3 /* iOSApp */;
			targetProxy = 2C8AF37629CD7F500028A4B3 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		2C8AF35E29CD7ED90028A4B3 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				2C8AF35F29CD7ED90028A4B3 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		2C8AF36329CD7EDA0028A4B3 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				2C8AF36429CD7EDA0028A4B3 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		2C8AF36929CD7EDA0028A4B3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		2C8AF36A29CD7EDA0028A4B3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		2C8AF36C29CD7EDA0028A4B3 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CA82B9E89D62F04CFBC9D88E /* Pods-iOSApp.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2A5Y5ZXK84;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = iOSApp/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = top.dawenhing.applepushtesting.iOSApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		2C8AF36D29CD7EDA0028A4B3 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9E37553E41343BF279527834 /* Pods-iOSApp.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2A5Y5ZXK84;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = iOSApp/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = top.dawenhing.applepushtesting.iOSApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		2C8AF37829CD7F500028A4B3 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 074DAC0C78F7C18D6DB93361 /* Pods-iOSAppTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2A5Y5ZXK84;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = top.dawenhing.applepushtesting.iOSAppTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/iOSApp.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/iOSApp";
			};
			name = Debug;
		};
		2C8AF37929CD7F500028A4B3 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7E180F607B2F2809BF43FA1B /* Pods-iOSAppTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2A5Y5ZXK84;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = top.dawenhing.applepushtesting.iOSAppTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/iOSApp.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/iOSApp";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2C8AF34D29CD7ED90028A4B3 /* Build configuration list for PBXProject "iOSApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2C8AF36929CD7EDA0028A4B3 /* Debug */,
				2C8AF36A29CD7EDA0028A4B3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2C8AF36B29CD7EDA0028A4B3 /* Build configuration list for PBXNativeTarget "iOSApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2C8AF36C29CD7EDA0028A4B3 /* Debug */,
				2C8AF36D29CD7EDA0028A4B3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2C8AF37A29CD7F500028A4B3 /* Build configuration list for PBXNativeTarget "iOSAppTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2C8AF37829CD7F500028A4B3 /* Debug */,
				2C8AF37929CD7F500028A4B3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 2C8AF34A29CD7ED90028A4B3 /* Project object */;
}
