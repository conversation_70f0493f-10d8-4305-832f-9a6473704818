source 'https://gitlab.lizhi.fm/iOSPods/LizhiSpecs.git'
#  use_frameworks!
platform :ios, '9.0'

target 'iOSApp' do  
  pod 'audiobuffer', :path => "../"

  target 'iOSAppTests' do
    inherit! :search_paths
  end  
end

def fix_arm64_for_sim(installer)
  installer.pods_project.build_configurations.each do |config|
    config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"
  end
end

def fix_xcode14_pods_sign(installer)
  installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
          config.build_settings['CODE_SIGN_IDENTITY'] = ''
      end
  end
end

post_install do |installer|
  fix_arm64_for_sim(installer)
  fix_xcode14_pods_sign(installer)
end
