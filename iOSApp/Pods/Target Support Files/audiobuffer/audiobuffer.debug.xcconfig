CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES
CLANG_ENABLE_MODULES = YES
CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
CONFIGURATION_BUILD_DIR = ${PODS_CONFIGURATION_BUILD_DIR}/audiobuffer
DEFINES_MODULE = YES
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1 WEBRTC_IOS
HEADER_SEARCH_PATHS = $(inherited) "${PODS_ROOT}/Headers/Private" "${PODS_ROOT}/Headers/Private/audiobuffer" "${PODS_ROOT}/Headers/Public" "${PODS_ROOT}/Headers/Public/audiobuffer" "${PODS_ROOT}/Headers/Public/audiobuffer/third_party/opus/ios"
OTHER_CFLAGS = $(inherited) -g -fstack-protector-all -Wall -Wextra -Wno-unused-parameter -Wstrict-aliasing -Wstrict-prototypes -fPIE -Wstack-protector -x objective-c
OTHER_CPLUSPLUSFLAGS = -g -fexceptions -frtti -Wall -x objective-c++
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_ROOT = ${SRCROOT}
PODS_TARGET_SRCROOT = ${PODS_ROOT}/../..
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
PRODUCT_BUNDLE_IDENTIFIER = org.cocoapods.${PRODUCT_NAME:rfc1034identifier}
SKIP_INSTALL = YES
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
