/*
 *  Copyright (c) 2017 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#ifndef AUDIO_DECODER_OPUS_H_
#define AUDIO_DECODER_OPUS_H_

#include <memory>
#include <vector>


#include "lizhiaudiocore/audio_codecs/include/audio_decoder.h"

namespace webrtc {

struct AudioDecoderOpus {
  static AudioDecoder* MakeAudioDecoder(
      int32_t channels, int32_t sample_rate);
};

}  // namespace webrtc

#endif
