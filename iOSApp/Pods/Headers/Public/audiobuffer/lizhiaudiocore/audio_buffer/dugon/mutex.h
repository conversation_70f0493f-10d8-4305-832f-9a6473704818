#ifndef MUTEX_H_
#define MUTEX_H_

#ifdef WIN32
#include "./private/windows/mutex_impl.h"
#else
#include "mutex_impl_posix.h"
#endif

namespace DUGON {
/**
 * Mutex object for lock and unlock.
 */
class Mutex : public MutexImpl {
 public:
  /**
   * Lock object.
   * @return true if successfully.
   */
  bool Lock();

  /**
   * Unlock object.
   * @return true if successfully.
   */
  bool Unlock();

  /**
   * Default constructor.
   */
  Mutex();

  /**
   * Destructor.
   */
  virtual ~Mutex();
};

}  // namespace DUGON
#endif
