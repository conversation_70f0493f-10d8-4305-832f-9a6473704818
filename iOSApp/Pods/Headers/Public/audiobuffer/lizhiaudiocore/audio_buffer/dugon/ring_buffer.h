#ifndef pcm_ring_buffer_H_
#define pcm_ring_buffer_H_
#include <string>

#include "mutex.h"

#define OPUS_HEADER_LENGTH 12

#define SYNC_HEADER 0x73d192b8

namespace DUGON {
typedef enum {
  kNoError = 0,
  kNoEnoughData,
  kError,
} RingBufferErrorType;

class RingBuffer {
 public:
  /**
   * Constructor.
   * @parm capacity The buffer size
   * @parm name Buffer name, used for debug, default name is RingBuffer
   */
  RingBuffer(unsigned int capacity, std::string name = "RingBuffer");

  /**
   * Destructor.
   */
  ~RingBuffer();

  /**
   * Write Data
   * @parm data Pointer for data storage
   * @parm length Data length
   * @return write result
   */
  RingBufferErrorType Write(const uint8_t *data, unsigned int length);

  /**
   * Read Data
   * @parm data Pointer for storage
   * @parm length Data length
   * @return read result
   */
  RingBufferErrorType Read(uint8_t *data, unsigned int length);

  /**
   * Get Unread Data
   * @return unread buffer size
   */
  unsigned int GetUnreadSize();

  /**
     * Get Unwrite Data
     * @return unwrite buffer size
    */
  unsigned int GetUnwriteSize();

  /**
   * Reset bffer
   */
  void Reset();

  /**
     * Find Sync Header
  */
  bool FindSyncHeader(uint32_t* correct_error_count);

  /**
     * Check Sync Header
  */
  RingBufferErrorType CheckSyncHeader();

  /**
       * Check Sync Header
  */
  void ParseOpusHeader(uint8_t* opus_header, uint16_t* codec_length, uint32_t* sync_header);

  /**
      * End flag
 */
  void MarkEnd();
 private:
  Mutex buffer_lock_;
  uint8_t *buffer_;
  unsigned int size_;
  std::string name_;

  uint8_t *read_pos_;
  uint8_t *write_pos_;
  unsigned int un_read_size_;
  unsigned int left_buf_size_;
  bool is_end_ = false;
  const uint32_t correct_error_count_threshold_ = 1000;
};
}  // namespace DUGON
#endif
