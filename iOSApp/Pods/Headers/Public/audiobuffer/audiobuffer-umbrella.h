#ifdef __OBJC__
#import <UIKit/UIKit.h>
#else
#ifndef FOUNDATION_EXPORT
#if defined(__cplusplus)
#define FOUNDATION_EXPORT extern "C"
#else
#define FOUNDATION_EXPORT extern
#endif
#endif
#endif

#import "lizhiaudiocore/audio_buffer/audio_codec_buffer.h"
#import "lizhiaudiocore/audio_buffer/dugon/mutex.h"
#import "lizhiaudiocore/audio_buffer/dugon/mutex_impl_posix.h"
#import "lizhiaudiocore/audio_buffer/dugon/ring_buffer.h"
#import "lizhiaudiocore/audio_buffer/dugon/scoped_lock.h"
#import "lizhiaudiocore/audio_buffer/utils/atomic_ops.h"
#import "lizhiaudiocore/audio_buffer/utils/audio_message_callback.h"
#import "lizhiaudiocore/audio_buffer/utils/ignore_wundef.h"
#import "lizhiaudiocore/audio_buffer/utils/logging.h"
#import "lizhiaudiocore/audio_codecs/include/audio_decoder.h"
#import "lizhiaudiocore/audio_codecs/opus/audio_decoder_opus.h"
#import "lizhiaudiocore/audio_codecs/opus/audio_decoder_opus_impl.h"
#import "lizhiaudiocore/audio_codecs/opus/opus_inst.h"
#import "lizhiaudiocore/audio_codecs/opus/opus_interface.h"
#import "third_party/opus/ios/include/opus.h"
#import "third_party/opus/ios/include/opus_defines.h"
#import "third_party/opus/ios/include/opus_multistream.h"
#import "third_party/opus/ios/include/opus_projection.h"
#import "third_party/opus/ios/include/opus_types.h"

FOUNDATION_EXPORT double audiobufferVersionNumber;
FOUNDATION_EXPORT const unsigned char audiobufferVersionString[];

