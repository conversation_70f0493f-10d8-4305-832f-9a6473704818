/*
 *  Copyright (c) 2015 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#ifndef AUDIO_DECODER_OPUS_IMPL_H_
#define AUDIO_DECODER_OPUS_IMPL_H_

#include <stddef.h>
#include <stdint.h>

#include <vector>

#include "lizhiaudiocore/audio_codecs/include/audio_decoder.h"
#include "lizhiaudiocore/audio_codecs/opus/opus_interface.h"

namespace webrtc {

class AudioDecoderOpusImpl final : public AudioDecoder {
 public:
  explicit AudioDecoderOpusImpl(size_t num_channels, int sample_rate);
  ~AudioDecoderOpusImpl() override;

  int PacketDuration(const uint8_t* encoded, size_t encoded_len) const override;
  int SampleRateHz() const override;
  size_t Channels() const override;
  int DecodeInternal(const uint8_t* encoded, size_t encoded_len,
                     int sample_rate_hz, int16_t* decoded) override;

 private:
  OpusDecInst* dec_state_;
  const size_t channels_;
  int sample_rate_;
};

}  // namespace webrtc

#endif  // AUDIO_DECODER_OPUS_IMPL_H_
