/*
 *  Copyright (c) 2012 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#ifndef MODULES_AUDIO_CODEC_BUFFER_H_
#define MODULES_AUDIO_CODEC_BUFFER_H_
#include <string.h>
#include <stddef.h>
#include <stdint.h>
#include <pthread.h>
#include <atomic>
#include <mutex>
#ifdef WEBRTC_ANDROID
#include <jni.h>
#endif


#include "lizhiaudiocore/audio_buffer/dugon/ring_buffer.h"
#include "lizhiaudiocore/audio_codecs/opus/audio_decoder_opus.h"
#include "lizhiaudiocore/audio_buffer/utils/logging.h"
#include "lizhiaudiocore/audio_buffer/utils/audio_message_callback.h"

namespace webrtc {
    typedef enum {
        Ok = 0,
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON>,
    } <PERSON><PERSON>erProbeType;

    class AudioCodecBuffer {
    public:
#ifdef WEBRTC_IOS
        AudioCodecBuffer(uint32_t channels, uint32_t sample_rate, uint32_t frame_size,
                         RawDataCallback raw_data_callback, LastFrameFinished last_frame_finished,
                         QuitDecodeThreadEvent quit_decode_thread_event, ErrorDataInput error_data_input);
#endif

#ifdef WEBRTC_ANDROID
        AudioCodecBuffer(uint32_t channels, uint32_t sample_rate, uint32_t frame_size, JNIEnv *env,
                         jmethodID pcm_callback, jmethodID quit_decode_thread,
                         jmethodID last_frame_finished, jmethodID input_data_error,
                         jobject jaudio_buffer_obj);
#endif
        virtual ~AudioCodecBuffer();

        void Init();

        void DecodeThread();

        bool SendCodecBuffer(const uint8_t *data, int length, bool is_end);

#ifdef WEBRTC_ANDROID
        void SetCacheDirectBuffer(JNIEnv* env, jobject byte_buffer);
#endif
        void StopDecoder();

        void ReadFrameHeader(uint16_t* encoder_length, uint32_t* frame_size, uint32_t* sync_header);

        void ReadFramePayload(uint8_t* encoder_data, uint16_t encoder_length);

        int DecoderPerFrame(uint32_t &packet_size, uint32_t &empty_packet_number);

        void ParseOpusHeader(uint8_t *opus_header, uint16_t *codec_length, uint32_t* frame_size, uint32_t *sync_header);

        unsigned int GetUnWriteSize();

        static BufferProbeType ProbeBuffer(const uint8_t *probe_data, uint32_t data_size, uint32_t *channel, uint32_t* sample_rate, uint32_t* frame_size, bool is_end);

    private:
        static void *DecodeThreadWrapper(void *);
        static void ParseOpusHeader(uint8_t* opus_header, uint16_t* codec_length, uint32_t* channel, uint32_t * sample_rate, uint32_t* frame_size, uint32_t* sync_header);
        static uint32_t GetSampleRate(uint32_t sample_rate_index);
        const uint32_t correct_error_count_threshold_ = 1000;
        DUGON::RingBuffer *input_ring_buffer_;
        webrtc::AudioDecoder *decoder_ = nullptr;
        uint32_t channels_;
        uint32_t sample_rates_;
        std::mutex mutex_;
        std::condition_variable cv_;
        bool sync_decoding_;
        bool is_of_end_;
        uint32_t correct_error_count_;
        uint32_t frame_size_;
        uint32_t normal_decode_size_;
        uint32_t pre_frame_time_stamp_;
        int16_t* decoded_buffer_data_;
#ifdef WEBRTC_IOS
        uint8_t* pcm_raw_data_;
        uint32_t pcm_raw_data_size_;
        RawDataCallback raw_data_callback_;
        LastFrameFinished last_frame_finished_;
        QuitDecodeThreadEvent quit_decode_thread_event_;
        ErrorDataInput error_data_input_;
#endif

#ifdef WEBRTC_ANDROID
        void *direct_buffer_address_;
        jmethodID pcm_callback_;
        jmethodID quit_decode_thread_;
        jmethodID last_frame_finished_;
        jmethodID input_data_error_;
        jobject jaudio_buffer_obj_;
        JavaVM *jvm_;
#endif
    };
}
#endif
