{"name": "audiobuffer", "version": "1.0.0", "summary": "audiobuffer", "description": "Opus格式解码器", "homepage": "https://gitlab.lizhi.fm/xindingfeng/audiobuffer.git", "license": {"type": "MIT", "file": "LICENSE"}, "authors": {"infra": "<EMAIL>"}, "source": {"git": "https://gitlab.lizhi.fm/xindingfeng/audiobuffer.git", "tag": "1.0.0"}, "swift_versions": "5.0", "platforms": {"ios": "8.0"}, "header_mappings_dir": "lizhiaudiocore/src/main/cpp", "libraries": "c++", "source_files": ["lizhiaudiocore/src/main/cpp/lizhiaudiocore/audio_buffer/**/*.{h,cpp,cc,c}", "lizhiaudiocore/src/main/cpp/lizhiaudiocore/audio_codecs/**/*.{h,cpp,cc,c}", "lizhiaudiocore/src/main/cpp/third_party/opus/ios/include/*.{h}"], "vendored_libraries": "lizhiaudiocore/src/main/cpp/third_party/opus/ios/lib/*.a", "pod_target_xcconfig": {"DEFINES_MODULE": "YES", "CLANG_ENABLE_MODULES": "YES", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "OTHER_CFLAGS": "-g -fstack-protector-all -Wall -Wextra -Wno-unused-parameter -Wstrict-aliasing -Wstrict-prototypes -fPIE -Wstack-protector -x objective-c", "OTHER_CPLUSPLUSFLAGS": "-g -fexceptions -frtti -Wall -x objective-c++", "GCC_PREPROCESSOR_DEFINITIONS": "WEBRTC_IOS", "HEADER_SEARCH_PATHS": "\"${PODS_ROOT}/Headers/Public/audiobuffer/third_party/opus/ios\""}, "swift_version": "5.0"}