// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		07982E8399EE08AF31926F9075A52191 /* opus_multistream.h in Headers */ = {isa = PBXBuildFile; fileRef = D0331B984817111C4516D692E29447B5 /* opus_multistream.h */; settings = {ATTRIBUTES = (Project, ); }; };
		16B3EAE8369BE51E5DCE285B68797FA6 /* opus.h in Headers */ = {isa = PBXBuildFile; fileRef = 002D5371A307059FEF7F5A306443C710 /* opus.h */; settings = {ATTRIBUTES = (Project, ); }; };
		3001E3B7A11DCBC672C4B3A50E4320AF /* audio_decoder_opus_impl.cc in Sources */ = {isa = PBXBuildFile; fileRef = 9A899FA5AFC428392B919154D2BC84DE /* audio_decoder_opus_impl.cc */; };
		31030CB4E3AA6277A355AF1C3A423D54 /* mutex_impl_posix.h in Headers */ = {isa = PBXBuildFile; fileRef = 8BF910D781B430B4A8EEB1CB489F7341 /* mutex_impl_posix.h */; settings = {ATTRIBUTES = (Project, ); }; };
		3405963516203F1F46A95E7D8D868A51 /* scoped_lock.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AC0D137908DB0A49B7656CBFB0D064 /* scoped_lock.h */; settings = {ATTRIBUTES = (Project, ); }; };
		3466B48EE8F0B3F37AE66B7FFA9D055F /* logging.h in Headers */ = {isa = PBXBuildFile; fileRef = B85F0E14EB7A0C1F0AB9A50471FAB000 /* logging.h */; settings = {ATTRIBUTES = (Project, ); }; };
		3913721F3BAE278CE08381643ED3594E /* opus_types.h in Headers */ = {isa = PBXBuildFile; fileRef = 2998B00D6311A96A10DA6059564E8499 /* opus_types.h */; settings = {ATTRIBUTES = (Project, ); }; };
		40CE66EC9B0942836CE30C28D1548C73 /* audiobuffer-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 69878C47D35D9DAEC583995798BCFC45 /* audiobuffer-umbrella.h */; settings = {ATTRIBUTES = (Project, ); }; };
		68007828C43CCAB03FBEBEBD8692136B /* opus_projection.h in Headers */ = {isa = PBXBuildFile; fileRef = 0349D14C50E35B66B6FAE486A0BFD1A1 /* opus_projection.h */; settings = {ATTRIBUTES = (Project, ); }; };
		6AF4AE4604966BDF25D47BAF3845F3C6 /* audio_decoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 7C6EBA3482EF602B11E56A2F530F3221 /* audio_decoder.h */; settings = {ATTRIBUTES = (Project, ); }; };
		7300A0A69081355393084106B8D3734D /* opus_interface.h in Headers */ = {isa = PBXBuildFile; fileRef = 7AA2CA5DDC212BEB73E00B1300207BA2 /* opus_interface.h */; settings = {ATTRIBUTES = (Project, ); }; };
		88414D4B5C598A5848A3E727ADEE66FD /* audio_decoder_opus.h in Headers */ = {isa = PBXBuildFile; fileRef = 3D0986B8193AAA3DD6D192C6D5528168 /* audio_decoder_opus.h */; settings = {ATTRIBUTES = (Project, ); }; };
		8B44B6445D34D3D1A44421ADF247D16E /* opus_defines.h in Headers */ = {isa = PBXBuildFile; fileRef = 21343F9CB1765F99D278E8262FCB70E1 /* opus_defines.h */; settings = {ATTRIBUTES = (Project, ); }; };
		9A9613C9EAF9871237CD5CD5AE8AFBE1 /* ignore_wundef.h in Headers */ = {isa = PBXBuildFile; fileRef = 4E4B0CC6B3BF447BA694AD03FF1B6F7B /* ignore_wundef.h */; settings = {ATTRIBUTES = (Project, ); }; };
		9DC7A94339781F41F46A280728D93C0D /* audio_codec_buffer.cpp in Sources */ = {isa = PBXBuildFile; fileRef = A258580EDFA33F539558E391EE3E087E /* audio_codec_buffer.cpp */; };
		A0F97354F05290504B80AD804320A08C /* ring_buffer.cpp in Sources */ = {isa = PBXBuildFile; fileRef = A8F528A21093411F9441FA7F5AA94DF0 /* ring_buffer.cpp */; };
		A440C6B1FBEFF0C80C34C59D48DED338 /* audio_decoder_opus_api.cc in Sources */ = {isa = PBXBuildFile; fileRef = 840D9412D8597BD59555425C93384AEF /* audio_decoder_opus_api.cc */; };
		A9FF01BAC74430E8C7333E3C99E9422C /* Pods-iOSApp-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 8282F351EB8DAE336D42FFF74E9952B7 /* Pods-iOSApp-dummy.m */; };
		AD203604EE1A2F1368EC541B6E68CE84 /* audio_message_callback.h in Headers */ = {isa = PBXBuildFile; fileRef = F4826BBDDF90F64974C3806336A733EE /* audio_message_callback.h */; settings = {ATTRIBUTES = (Project, ); }; };
		B04CDE3AE4804EF553E48D7468EECD63 /* ring_buffer.h in Headers */ = {isa = PBXBuildFile; fileRef = 2349D92A51863566DF96227E6764C42D /* ring_buffer.h */; settings = {ATTRIBUTES = (Project, ); }; };
		B50B4DF0DA475DF05B504967629C0B07 /* audiobuffer-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A1F574B5FCFEC89A7A5F1D8B6B9A23D /* audiobuffer-dummy.m */; };
		D2877AA8A8DD2D1DD040552E6E8195E7 /* audio_decoder_opus_impl.h in Headers */ = {isa = PBXBuildFile; fileRef = 7D00B31F11DB9607E64C6BA1FF83CB20 /* audio_decoder_opus_impl.h */; settings = {ATTRIBUTES = (Project, ); }; };
		DB04C1AF76568612B29D3F64C70D5CD6 /* atomic_ops.h in Headers */ = {isa = PBXBuildFile; fileRef = BFDDB5372287D1710F4EA3C2448D1C9B /* atomic_ops.h */; settings = {ATTRIBUTES = (Project, ); }; };
		DC5BE728AA2E39F1D81298AF77EDB5DB /* audio_codec_buffer.h in Headers */ = {isa = PBXBuildFile; fileRef = E620CD6B77A6BF1831F75C4D9F0E4038 /* audio_codec_buffer.h */; settings = {ATTRIBUTES = (Project, ); }; };
		DF08BE3DD80B19D8B664EEDE9390F687 /* Pods-iOSAppTests-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = F4480DE40806F92E04F5479D41A2CC8D /* Pods-iOSAppTests-dummy.m */; };
		E01DB3358102CCFF26746B55EFAF22A3 /* mutex.h in Headers */ = {isa = PBXBuildFile; fileRef = 06735034C1FD37B2BC8150F215F86191 /* mutex.h */; settings = {ATTRIBUTES = (Project, ); }; };
		E5032554E91F948740B94FFC41927DE1 /* opus_interface.c in Sources */ = {isa = PBXBuildFile; fileRef = 7E28D90E41DCB1A10FB60EF60AA5344E /* opus_interface.c */; };
		E7268603B1DC2599052A310738DDB240 /* mutex_posix.cpp in Sources */ = {isa = PBXBuildFile; fileRef = A4EFF23A84C4E71CF0A01749A55B4D48 /* mutex_posix.cpp */; };
		EA733A472CEE7CC9AB9D082038764024 /* scoped_lock.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 4885560EAB43B8479BF6126E7FAC64AB /* scoped_lock.cpp */; };
		F0C2B71C8E137E995E7B29CAC9895AE2 /* opus_inst.h in Headers */ = {isa = PBXBuildFile; fileRef = F4860D7EA4057299D791E307935632F9 /* opus_inst.h */; settings = {ATTRIBUTES = (Project, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		539AF23054C05D903A912EB2B38C5BCA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = C09D413D0B1805DC5CBA5C2478F61B6D;
			remoteInfo = "Pods-iOSApp";
		};
		FB71336357EB4D857DF82243B000CF7F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 63653F3358AC3C0C817B61CDF5420600;
			remoteInfo = audiobuffer;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		002D5371A307059FEF7F5A306443C710 /* opus.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = opus.h; sourceTree = "<group>"; };
		0349D14C50E35B66B6FAE486A0BFD1A1 /* opus_projection.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = opus_projection.h; sourceTree = "<group>"; };
		05C59FF0FE2B33D28494C48E92C10B1F /* audiobuffer.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = audiobuffer.debug.xcconfig; sourceTree = "<group>"; };
		06735034C1FD37B2BC8150F215F86191 /* mutex.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = mutex.h; sourceTree = "<group>"; };
		0A1F574B5FCFEC89A7A5F1D8B6B9A23D /* audiobuffer-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "audiobuffer-dummy.m"; sourceTree = "<group>"; };
		21343F9CB1765F99D278E8262FCB70E1 /* opus_defines.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = opus_defines.h; sourceTree = "<group>"; };
		2349D92A51863566DF96227E6764C42D /* ring_buffer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = ring_buffer.h; sourceTree = "<group>"; };
		2998B00D6311A96A10DA6059564E8499 /* opus_types.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = opus_types.h; sourceTree = "<group>"; };
		31920FDC6AC66325AEDBE40F62DFF16F /* audiobuffer.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = audiobuffer.release.xcconfig; sourceTree = "<group>"; };
		34C9BB79F5D88CA1376E75696A69472D /* audiobuffer-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "audiobuffer-prefix.pch"; sourceTree = "<group>"; };
		3D0986B8193AAA3DD6D192C6D5528168 /* audio_decoder_opus.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = audio_decoder_opus.h; sourceTree = "<group>"; };
		3E4767929A1F423198A01D07AD42A666 /* Pods-iOSApp-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-iOSApp-acknowledgements.markdown"; sourceTree = "<group>"; };
		45C4430EFF8489707400047327BEC7EF /* Pods-iOSAppTests-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-iOSAppTests-acknowledgements.plist"; sourceTree = "<group>"; };
		4885560EAB43B8479BF6126E7FAC64AB /* scoped_lock.cpp */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.cpp.cpp; path = scoped_lock.cpp; sourceTree = "<group>"; };
		4E4B0CC6B3BF447BA694AD03FF1B6F7B /* ignore_wundef.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = ignore_wundef.h; sourceTree = "<group>"; };
		5BD0470619184D5EF060359E51D83F45 /* Pods-iOSAppTests-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-iOSAppTests-acknowledgements.markdown"; sourceTree = "<group>"; };
		6814CC764AC4A49CD4C7C2D60738F2E8 /* libPods-iOSApp.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-iOSApp.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		690D53710AE7DB2A423178BD17A98022 /* Pods-iOSApp.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-iOSApp.release.xcconfig"; sourceTree = "<group>"; };
		69878C47D35D9DAEC583995798BCFC45 /* audiobuffer-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "audiobuffer-umbrella.h"; sourceTree = "<group>"; };
		71AC0D137908DB0A49B7656CBFB0D064 /* scoped_lock.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = scoped_lock.h; sourceTree = "<group>"; };
		7AA2CA5DDC212BEB73E00B1300207BA2 /* opus_interface.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = opus_interface.h; sourceTree = "<group>"; };
		7C6EBA3482EF602B11E56A2F530F3221 /* audio_decoder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = audio_decoder.h; sourceTree = "<group>"; };
		7D00B31F11DB9607E64C6BA1FF83CB20 /* audio_decoder_opus_impl.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = audio_decoder_opus_impl.h; sourceTree = "<group>"; };
		7E28D90E41DCB1A10FB60EF60AA5344E /* opus_interface.c */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.c; path = opus_interface.c; sourceTree = "<group>"; };
		80704819527063B233070CFFB66CFD42 /* Pods-iOSApp.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-iOSApp.debug.xcconfig"; sourceTree = "<group>"; };
		8282F351EB8DAE336D42FFF74E9952B7 /* Pods-iOSApp-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-iOSApp-dummy.m"; sourceTree = "<group>"; };
		840D9412D8597BD59555425C93384AEF /* audio_decoder_opus_api.cc */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.cpp.cpp; path = audio_decoder_opus_api.cc; sourceTree = "<group>"; };
		8BF910D781B430B4A8EEB1CB489F7341 /* mutex_impl_posix.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = mutex_impl_posix.h; sourceTree = "<group>"; };
		946DF953522AF9D2F780D80D86A51620 /* Pods-iOSAppTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-iOSAppTests.release.xcconfig"; sourceTree = "<group>"; };
		97D4A1E65919B569E6025A72090072BC /* Pods-iOSApp-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-iOSApp-acknowledgements.plist"; sourceTree = "<group>"; };
		9A899FA5AFC428392B919154D2BC84DE /* audio_decoder_opus_impl.cc */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.cpp.cpp; path = audio_decoder_opus_impl.cc; sourceTree = "<group>"; };
		9C010B05DD12616663BD4D1A54ED0B62 /* README.md */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		A258580EDFA33F539558E391EE3E087E /* audio_codec_buffer.cpp */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.cpp.cpp; path = audio_codec_buffer.cpp; sourceTree = "<group>"; };
		A4EFF23A84C4E71CF0A01749A55B4D48 /* mutex_posix.cpp */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.cpp.cpp; path = mutex_posix.cpp; sourceTree = "<group>"; };
		A8F528A21093411F9441FA7F5AA94DF0 /* ring_buffer.cpp */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.cpp.cpp; path = ring_buffer.cpp; sourceTree = "<group>"; };
		B3FAD98F9FC944395FD965468374B645 /* audiobuffer.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; path = audiobuffer.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		B85F0E14EB7A0C1F0AB9A50471FAB000 /* logging.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = logging.h; sourceTree = "<group>"; };
		BB160628974BF28238710640A6AE26F6 /* Pods-iOSAppTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-iOSAppTests.debug.xcconfig"; sourceTree = "<group>"; };
		BFDDB5372287D1710F4EA3C2448D1C9B /* atomic_ops.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = atomic_ops.h; sourceTree = "<group>"; };
		D0331B984817111C4516D692E29447B5 /* opus_multistream.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = opus_multistream.h; sourceTree = "<group>"; };
		DF771FBC26155CF0F996795D64453535 /* audiobuffer */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; name = audiobuffer; path = libaudiobuffer.a; sourceTree = BUILT_PRODUCTS_DIR; };
		E620CD6B77A6BF1831F75C4D9F0E4038 /* audio_codec_buffer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = audio_codec_buffer.h; sourceTree = "<group>"; };
		E97C6B7DB8E3B78C5041B901D25BF2D1 /* Pods-iOSAppTests */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; name = "Pods-iOSAppTests"; path = "libPods-iOSAppTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		EF8EA630F9FEF1C14B08C42A6DD7A4C2 /* audiobuffer.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = audiobuffer.modulemap; sourceTree = "<group>"; };
		F4480DE40806F92E04F5479D41A2CC8D /* Pods-iOSAppTests-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-iOSAppTests-dummy.m"; sourceTree = "<group>"; };
		F4826BBDDF90F64974C3806336A733EE /* audio_message_callback.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = audio_message_callback.h; sourceTree = "<group>"; };
		F4860D7EA4057299D791E307935632F9 /* opus_inst.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = opus_inst.h; sourceTree = "<group>"; };
		F754549D857EDFF1EF2107E5C6B09B2C /* libopus.a */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = archive.ar; name = libopus.a; path = lizhiaudiocore/src/main/cpp/third_party/opus/ios/lib/libopus.a; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		74241BF3D4D4209A8F623735931B15C4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5B7D2E7D4BC521E2969FC5F7AD8EBE3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EBF99CDC40AFB5D1AED50DA3E62A602A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1ECB338BBDA9AF878E59A8717B117F11 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				8C53240B6CBE811AD1DFA12872382291 /* Pods-iOSApp */,
				28186A779075C2AC052AB5FD70CEDF04 /* Pods-iOSAppTests */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		202A5C26C85F7B82D0C4CD684842C025 /* opus */ = {
			isa = PBXGroup;
			children = (
				A01170CFB5EA8A1E5D18510CF5E639B1 /* ios */,
			);
			path = opus;
			sourceTree = "<group>";
		};
		28186A779075C2AC052AB5FD70CEDF04 /* Pods-iOSAppTests */ = {
			isa = PBXGroup;
			children = (
				5BD0470619184D5EF060359E51D83F45 /* Pods-iOSAppTests-acknowledgements.markdown */,
				45C4430EFF8489707400047327BEC7EF /* Pods-iOSAppTests-acknowledgements.plist */,
				F4480DE40806F92E04F5479D41A2CC8D /* Pods-iOSAppTests-dummy.m */,
				BB160628974BF28238710640A6AE26F6 /* Pods-iOSAppTests.debug.xcconfig */,
				946DF953522AF9D2F780D80D86A51620 /* Pods-iOSAppTests.release.xcconfig */,
			);
			name = "Pods-iOSAppTests";
			path = "Target Support Files/Pods-iOSAppTests";
			sourceTree = "<group>";
		};
		34EA0B4917266F9CF1DA51D19B600559 /* third_party */ = {
			isa = PBXGroup;
			children = (
				202A5C26C85F7B82D0C4CD684842C025 /* opus */,
			);
			name = third_party;
			path = lizhiaudiocore/src/main/cpp/third_party;
			sourceTree = "<group>";
		};
		564C997641F6C4795A79DB4CB04CFB46 /* Pod */ = {
			isa = PBXGroup;
			children = (
				B3FAD98F9FC944395FD965468374B645 /* audiobuffer.podspec */,
				9C010B05DD12616663BD4D1A54ED0B62 /* README.md */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		57595E7B55F735BB1A615DF795B65596 /* Products */ = {
			isa = PBXGroup;
			children = (
				DF771FBC26155CF0F996795D64453535 /* audiobuffer */,
				6814CC764AC4A49CD4C7C2D60738F2E8 /* libPods-iOSApp.a */,
				E97C6B7DB8E3B78C5041B901D25BF2D1 /* Pods-iOSAppTests */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		75C14512C5A869FF5410E148CDCCF340 /* lizhiaudiocore */ = {
			isa = PBXGroup;
			children = (
				AE29A6D2ACE21C3430CF20C234CEF8CB /* audio_buffer */,
				B79BF066A25E19028016B5A13DB05538 /* audio_codecs */,
			);
			name = lizhiaudiocore;
			path = lizhiaudiocore/src/main/cpp/lizhiaudiocore;
			sourceTree = "<group>";
		};
		84350EBB5E2EC60E183CDC35D8F203A5 /* audiobuffer */ = {
			isa = PBXGroup;
			children = (
				E38169F820932D57F6AC20ED2A11D74B /* Frameworks */,
				75C14512C5A869FF5410E148CDCCF340 /* lizhiaudiocore */,
				564C997641F6C4795A79DB4CB04CFB46 /* Pod */,
				C7219DF2EAAD048C02E04CEF0435A06D /* Support Files */,
				34EA0B4917266F9CF1DA51D19B600559 /* third_party */,
			);
			name = audiobuffer;
			path = ../..;
			sourceTree = "<group>";
		};
		8C53240B6CBE811AD1DFA12872382291 /* Pods-iOSApp */ = {
			isa = PBXGroup;
			children = (
				3E4767929A1F423198A01D07AD42A666 /* Pods-iOSApp-acknowledgements.markdown */,
				97D4A1E65919B569E6025A72090072BC /* Pods-iOSApp-acknowledgements.plist */,
				8282F351EB8DAE336D42FFF74E9952B7 /* Pods-iOSApp-dummy.m */,
				80704819527063B233070CFFB66CFD42 /* Pods-iOSApp.debug.xcconfig */,
				690D53710AE7DB2A423178BD17A98022 /* Pods-iOSApp.release.xcconfig */,
			);
			name = "Pods-iOSApp";
			path = "Target Support Files/Pods-iOSApp";
			sourceTree = "<group>";
		};
		911682023C86D4766397162F2968F67D /* dugon */ = {
			isa = PBXGroup;
			children = (
				06735034C1FD37B2BC8150F215F86191 /* mutex.h */,
				8BF910D781B430B4A8EEB1CB489F7341 /* mutex_impl_posix.h */,
				A4EFF23A84C4E71CF0A01749A55B4D48 /* mutex_posix.cpp */,
				A8F528A21093411F9441FA7F5AA94DF0 /* ring_buffer.cpp */,
				2349D92A51863566DF96227E6764C42D /* ring_buffer.h */,
				4885560EAB43B8479BF6126E7FAC64AB /* scoped_lock.cpp */,
				71AC0D137908DB0A49B7656CBFB0D064 /* scoped_lock.h */,
			);
			path = dugon;
			sourceTree = "<group>";
		};
		92D9A44516684E13FEF398E179BC7BB1 /* utils */ = {
			isa = PBXGroup;
			children = (
				BFDDB5372287D1710F4EA3C2448D1C9B /* atomic_ops.h */,
				F4826BBDDF90F64974C3806336A733EE /* audio_message_callback.h */,
				4E4B0CC6B3BF447BA694AD03FF1B6F7B /* ignore_wundef.h */,
				B85F0E14EB7A0C1F0AB9A50471FAB000 /* logging.h */,
			);
			path = utils;
			sourceTree = "<group>";
		};
		9492E7B168A0A5EB5ABD481372DAF864 /* include */ = {
			isa = PBXGroup;
			children = (
				002D5371A307059FEF7F5A306443C710 /* opus.h */,
				21343F9CB1765F99D278E8262FCB70E1 /* opus_defines.h */,
				D0331B984817111C4516D692E29447B5 /* opus_multistream.h */,
				0349D14C50E35B66B6FAE486A0BFD1A1 /* opus_projection.h */,
				2998B00D6311A96A10DA6059564E8499 /* opus_types.h */,
			);
			path = include;
			sourceTree = "<group>";
		};
		A01170CFB5EA8A1E5D18510CF5E639B1 /* ios */ = {
			isa = PBXGroup;
			children = (
				9492E7B168A0A5EB5ABD481372DAF864 /* include */,
			);
			path = ios;
			sourceTree = "<group>";
		};
		A2FAC1F13C4E47031621037512202561 /* Development Pods */ = {
			isa = PBXGroup;
			children = (
				84350EBB5E2EC60E183CDC35D8F203A5 /* audiobuffer */,
			);
			name = "Development Pods";
			sourceTree = "<group>";
		};
		AE29A6D2ACE21C3430CF20C234CEF8CB /* audio_buffer */ = {
			isa = PBXGroup;
			children = (
				A258580EDFA33F539558E391EE3E087E /* audio_codec_buffer.cpp */,
				E620CD6B77A6BF1831F75C4D9F0E4038 /* audio_codec_buffer.h */,
				911682023C86D4766397162F2968F67D /* dugon */,
				92D9A44516684E13FEF398E179BC7BB1 /* utils */,
			);
			path = audio_buffer;
			sourceTree = "<group>";
		};
		B79BF066A25E19028016B5A13DB05538 /* audio_codecs */ = {
			isa = PBXGroup;
			children = (
				D617416B3AED50C735EC2F6C0D286A96 /* include */,
				D10E2A6425F6484B034D8EA8559BD695 /* opus */,
			);
			path = audio_codecs;
			sourceTree = "<group>";
		};
		C7219DF2EAAD048C02E04CEF0435A06D /* Support Files */ = {
			isa = PBXGroup;
			children = (
				EF8EA630F9FEF1C14B08C42A6DD7A4C2 /* audiobuffer.modulemap */,
				0A1F574B5FCFEC89A7A5F1D8B6B9A23D /* audiobuffer-dummy.m */,
				34C9BB79F5D88CA1376E75696A69472D /* audiobuffer-prefix.pch */,
				69878C47D35D9DAEC583995798BCFC45 /* audiobuffer-umbrella.h */,
				05C59FF0FE2B33D28494C48E92C10B1F /* audiobuffer.debug.xcconfig */,
				31920FDC6AC66325AEDBE40F62DFF16F /* audiobuffer.release.xcconfig */,
			);
			name = "Support Files";
			path = "iOSApp/Pods/Target Support Files/audiobuffer";
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				A2FAC1F13C4E47031621037512202561 /* Development Pods */,
				D89477F20FB1DE18A04690586D7808C4 /* Frameworks */,
				57595E7B55F735BB1A615DF795B65596 /* Products */,
				1ECB338BBDA9AF878E59A8717B117F11 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		D10E2A6425F6484B034D8EA8559BD695 /* opus */ = {
			isa = PBXGroup;
			children = (
				3D0986B8193AAA3DD6D192C6D5528168 /* audio_decoder_opus.h */,
				840D9412D8597BD59555425C93384AEF /* audio_decoder_opus_api.cc */,
				9A899FA5AFC428392B919154D2BC84DE /* audio_decoder_opus_impl.cc */,
				7D00B31F11DB9607E64C6BA1FF83CB20 /* audio_decoder_opus_impl.h */,
				F4860D7EA4057299D791E307935632F9 /* opus_inst.h */,
				7E28D90E41DCB1A10FB60EF60AA5344E /* opus_interface.c */,
				7AA2CA5DDC212BEB73E00B1300207BA2 /* opus_interface.h */,
			);
			path = opus;
			sourceTree = "<group>";
		};
		D617416B3AED50C735EC2F6C0D286A96 /* include */ = {
			isa = PBXGroup;
			children = (
				7C6EBA3482EF602B11E56A2F530F3221 /* audio_decoder.h */,
			);
			path = include;
			sourceTree = "<group>";
		};
		D89477F20FB1DE18A04690586D7808C4 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		E38169F820932D57F6AC20ED2A11D74B /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				F754549D857EDFF1EF2107E5C6B09B2C /* libopus.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		BC9356C9C623C82C4DF85DD6C22497F3 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D22B51AA0D7CDCB382DD1C4B6DC46D1B /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB04C1AF76568612B29D3F64C70D5CD6 /* atomic_ops.h in Headers */,
				DC5BE728AA2E39F1D81298AF77EDB5DB /* audio_codec_buffer.h in Headers */,
				6AF4AE4604966BDF25D47BAF3845F3C6 /* audio_decoder.h in Headers */,
				88414D4B5C598A5848A3E727ADEE66FD /* audio_decoder_opus.h in Headers */,
				D2877AA8A8DD2D1DD040552E6E8195E7 /* audio_decoder_opus_impl.h in Headers */,
				AD203604EE1A2F1368EC541B6E68CE84 /* audio_message_callback.h in Headers */,
				40CE66EC9B0942836CE30C28D1548C73 /* audiobuffer-umbrella.h in Headers */,
				9A9613C9EAF9871237CD5CD5AE8AFBE1 /* ignore_wundef.h in Headers */,
				3466B48EE8F0B3F37AE66B7FFA9D055F /* logging.h in Headers */,
				E01DB3358102CCFF26746B55EFAF22A3 /* mutex.h in Headers */,
				31030CB4E3AA6277A355AF1C3A423D54 /* mutex_impl_posix.h in Headers */,
				16B3EAE8369BE51E5DCE285B68797FA6 /* opus.h in Headers */,
				8B44B6445D34D3D1A44421ADF247D16E /* opus_defines.h in Headers */,
				F0C2B71C8E137E995E7B29CAC9895AE2 /* opus_inst.h in Headers */,
				7300A0A69081355393084106B8D3734D /* opus_interface.h in Headers */,
				07982E8399EE08AF31926F9075A52191 /* opus_multistream.h in Headers */,
				68007828C43CCAB03FBEBEBD8692136B /* opus_projection.h in Headers */,
				3913721F3BAE278CE08381643ED3594E /* opus_types.h in Headers */,
				B04CDE3AE4804EF553E48D7468EECD63 /* ring_buffer.h in Headers */,
				3405963516203F1F46A95E7D8D868A51 /* scoped_lock.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FF455CEA4D604418AD4FB0A376B7DEB1 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		57606D9E36D1DF621AAFD0CC051613C0 /* Pods-iOSAppTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 64020AFC1E16CD1EC945C201F461CC4B /* Build configuration list for PBXNativeTarget "Pods-iOSAppTests" */;
			buildPhases = (
				FF455CEA4D604418AD4FB0A376B7DEB1 /* Headers */,
				8EBB361A359F980395D4B6E7EB3F9BC2 /* Sources */,
				74241BF3D4D4209A8F623735931B15C4 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				C78205C82FAAEC4DD11796C23AD05A63 /* PBXTargetDependency */,
			);
			name = "Pods-iOSAppTests";
			productName = "Pods-iOSAppTests";
			productReference = E97C6B7DB8E3B78C5041B901D25BF2D1 /* Pods-iOSAppTests */;
			productType = "com.apple.product-type.library.static";
		};
		63653F3358AC3C0C817B61CDF5420600 /* audiobuffer */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 37DAC2ECEF275E4E637E8908B401C982 /* Build configuration list for PBXNativeTarget "audiobuffer" */;
			buildPhases = (
				D22B51AA0D7CDCB382DD1C4B6DC46D1B /* Headers */,
				A7088E30D1BCE142817E4D955BD78578 /* Sources */,
				EBF99CDC40AFB5D1AED50DA3E62A602A /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = audiobuffer;
			productName = audiobuffer;
			productReference = DF771FBC26155CF0F996795D64453535 /* audiobuffer */;
			productType = "com.apple.product-type.library.static";
		};
		C09D413D0B1805DC5CBA5C2478F61B6D /* Pods-iOSApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = EDFF55B7C944E69A511A75ACF57455A2 /* Build configuration list for PBXNativeTarget "Pods-iOSApp" */;
			buildPhases = (
				BC9356C9C623C82C4DF85DD6C22497F3 /* Headers */,
				5FC9D48263F9DC06B6F33A0DA71DD2D8 /* Sources */,
				B5B7D2E7D4BC521E2969FC5F7AD8EBE3 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				0C6EE3EF4E3E58D49BAF0F6C6AA4113F /* PBXTargetDependency */,
			);
			name = "Pods-iOSApp";
			productName = "Pods-iOSApp";
			productReference = 6814CC764AC4A49CD4C7C2D60738F2E8 /* libPods-iOSApp.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1300;
				LastUpgradeCheck = 1300;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			productRefGroup = 57595E7B55F735BB1A615DF795B65596 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				63653F3358AC3C0C817B61CDF5420600 /* audiobuffer */,
				C09D413D0B1805DC5CBA5C2478F61B6D /* Pods-iOSApp */,
				57606D9E36D1DF621AAFD0CC051613C0 /* Pods-iOSAppTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		5FC9D48263F9DC06B6F33A0DA71DD2D8 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A9FF01BAC74430E8C7333E3C99E9422C /* Pods-iOSApp-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8EBB361A359F980395D4B6E7EB3F9BC2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF08BE3DD80B19D8B664EEDE9390F687 /* Pods-iOSAppTests-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A7088E30D1BCE142817E4D955BD78578 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9DC7A94339781F41F46A280728D93C0D /* audio_codec_buffer.cpp in Sources */,
				A440C6B1FBEFF0C80C34C59D48DED338 /* audio_decoder_opus_api.cc in Sources */,
				3001E3B7A11DCBC672C4B3A50E4320AF /* audio_decoder_opus_impl.cc in Sources */,
				B50B4DF0DA475DF05B504967629C0B07 /* audiobuffer-dummy.m in Sources */,
				E7268603B1DC2599052A310738DDB240 /* mutex_posix.cpp in Sources */,
				E5032554E91F948740B94FFC41927DE1 /* opus_interface.c in Sources */,
				A0F97354F05290504B80AD804320A08C /* ring_buffer.cpp in Sources */,
				EA733A472CEE7CC9AB9D082038764024 /* scoped_lock.cpp in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		0C6EE3EF4E3E58D49BAF0F6C6AA4113F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = audiobuffer;
			target = 63653F3358AC3C0C817B61CDF5420600 /* audiobuffer */;
			targetProxy = FB71336357EB4D857DF82243B000CF7F /* PBXContainerItemProxy */;
		};
		C78205C82FAAEC4DD11796C23AD05A63 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "Pods-iOSApp";
			target = C09D413D0B1805DC5CBA5C2478F61B6D /* Pods-iOSApp */;
			targetProxy = 539AF23054C05D903A912EB2B38C5BCA /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		1BEDC13E697A0CF0A9C9F3D42C656B3B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 31920FDC6AC66325AEDBE40F62DFF16F /* audiobuffer.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				GCC_PREFIX_HEADER = "Target Support Files/audiobuffer/audiobuffer-prefix.pch";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MODULEMAP_FILE = Headers/Public/audiobuffer/audiobuffer.modulemap;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PRIVATE_HEADERS_FOLDER_PATH = "";
				PRODUCT_MODULE_NAME = audiobuffer;
				PRODUCT_NAME = audiobuffer;
				PUBLIC_HEADERS_FOLDER_PATH = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		25AD9454612BF454A1E3DC4CD4FA8C6D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		32B96E6515422D8092FE34D543291345 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BB160628974BF28238710640A6AE26F6 /* Pods-iOSAppTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MACH_O_TYPE = staticlib;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		46F185E691A335549D7F5BBA8CE14581 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 05C59FF0FE2B33D28494C48E92C10B1F /* audiobuffer.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				GCC_PREFIX_HEADER = "Target Support Files/audiobuffer/audiobuffer-prefix.pch";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MODULEMAP_FILE = Headers/Public/audiobuffer/audiobuffer.modulemap;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PRIVATE_HEADERS_FOLDER_PATH = "";
				PRODUCT_MODULE_NAME = audiobuffer;
				PRODUCT_NAME = audiobuffer;
				PUBLIC_HEADERS_FOLDER_PATH = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		5EBC726671708F125BC811B3FF4965DC /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 946DF953522AF9D2F780D80D86A51620 /* Pods-iOSAppTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MACH_O_TYPE = staticlib;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		B14C11A1CC3C63D843432148287F7EF1 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 690D53710AE7DB2A423178BD17A98022 /* Pods-iOSApp.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MACH_O_TYPE = staticlib;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		CA547D2C7E9A8A153DC2B27FBE00B112 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		E1CEC9F264E7CBDCA73DD74D748B831F /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 80704819527063B233070CFFB66CFD42 /* Pods-iOSApp.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MACH_O_TYPE = staticlib;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		37DAC2ECEF275E4E637E8908B401C982 /* Build configuration list for PBXNativeTarget "audiobuffer" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				46F185E691A335549D7F5BBA8CE14581 /* Debug */,
				1BEDC13E697A0CF0A9C9F3D42C656B3B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				25AD9454612BF454A1E3DC4CD4FA8C6D /* Debug */,
				CA547D2C7E9A8A153DC2B27FBE00B112 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		64020AFC1E16CD1EC945C201F461CC4B /* Build configuration list for PBXNativeTarget "Pods-iOSAppTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				32B96E6515422D8092FE34D543291345 /* Debug */,
				5EBC726671708F125BC811B3FF4965DC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		EDFF55B7C944E69A511A75ACF57455A2 /* Build configuration list for PBXNativeTarget "Pods-iOSApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E1CEC9F264E7CBDCA73DD74D748B831F /* Debug */,
				B14C11A1CC3C63D843432148287F7EF1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
