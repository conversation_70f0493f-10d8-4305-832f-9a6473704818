<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".MainActivity">

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:context=".MainActivity">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:visibility="gone"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="插入： " />

            <EditText
                android:id="@+id/index"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:hint="第几位" />

            <EditText
                android:id="@+id/url"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint=" 音频地址"
                android:imeOptions="actionDone"
                android:maxLines="1"
                android:singleLine="true" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="倍速:  " />

            <Button
                android:id="@+id/speedHalf"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0.5" />

            <Button
                android:id="@+id/speed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="1.0" />

            <Button
                android:id="@+id/speed1Half"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="1.5" />

            <Button
                android:id="@+id/speed2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="2" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/position"
                android:layout_width="65dp"
                android:layout_height="wrap_content"
                android:lines="1"
                android:text="00:00.000" />

            <SeekBar
                android:id="@+id/seekBar"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"/>

            <TextView
                android:id="@+id/duration"
                android:layout_width="65dp"
                android:layout_height="wrap_content"
                android:text="00:00.000" />

        </LinearLayout>


        <TextView
            android:id="@+id/tv_state"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="STATE_IDLE" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="horizontal">

            <Button
                android:id="@+id/previous"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="↑" />

            <Button
                android:id="@+id/seek15"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="←15" />

            <Button
                android:id="@+id/play"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="▶" />

            <Button
                android:id="@+id/seek"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="15→" />

            <Button
                android:id="@+id/next"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="↓" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="音量： " />

            <Button
                android:id="@+id/plus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="+" />

            <Button
                android:id="@+id/minus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="-" />

            <CheckBox
                android:id="@+id/cb_delete"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:checked="false"
                android:text="启动内置删除策略"
                />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="删缓存： " />

            <Button
                android:id="@+id/deleteCache"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="所有" />

            <Button
                android:id="@+id/deleteHighCache"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="高优" />

            <Button
                android:id="@+id/deleteNormal"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="普通" />

            <Button
                android:id="@+id/deleteSelected"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="已选" />

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="查缓存： " />

            <Button
                android:id="@+id/queryCache"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="所有" />

            <Button
                android:id="@+id/querySelected"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="已选" />

        </LinearLayout>

<!-- 清缓存 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:visibility="gone"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="清缓存： " />

            <Button
                android:id="@+id/clearCache"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="清除所有缓存" />

            <Button
                android:id="@+id/clearHighCache"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="清除高优缓存" />

            <Button
                android:id="@+id/clearList"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="清空列表" />

        </LinearLayout>

        <!-- 定时 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:visibility="gone"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="定时： " />

            <Button
                android:id="@+id/timer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="10秒" />

            <Button
                android:id="@+id/timer1Item"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="1个节目" />

            <Button
                android:id="@+id/timer5Item"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="5个节目" />

            <Button
                android:id="@+id/indexBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="index" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="顺序： " />

            <RadioGroup
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/repeat"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    android:text="顺序" />

                <RadioButton
                    android:id="@+id/repeatAll"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="循环" />

                <RadioButton
                    android:id="@+id/repeatSingle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="单曲" />

                <RadioButton
                    android:id="@+id/repeatRandom"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="随机" />
            </RadioGroup>
        </LinearLayout>

        <!-- 音质 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:visibility="gone"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/getQuality"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="音质： " />

            <RadioGroup
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/lowQuality"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="低清" />

                <RadioButton
                    android:id="@+id/highQuality"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    android:text="高清" />

                <RadioButton
                    android:id="@+id/superQuality"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="超清" />

                <RadioButton
                    android:id="@+id/losslessQuality"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="无损" />
            </RadioGroup>
        </LinearLayout>

        <!-- 焦点丢失 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:visibility="gone"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="焦点丢失： " />

            <RadioGroup
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/pauseOnFocusLost"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    android:text="暂停" />

                <RadioButton
                    android:id="@+id/dontPauseOnFocusLost"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="不暂停" />

            </RadioGroup>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:visibility="gone"
            android:orientation="horizontal">

            <Button
                android:id="@+id/test_addItem"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:text="测试添加音频" />

            <Button
                android:id="@+id/print_allThread"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:text="打印全部线程" />

        </LinearLayout>

    </LinearLayout>

    <TextView
        android:id="@+id/countdown"
        android:visibility="gone"
        android:layout_width="150dp"
        android:layout_gravity="center"
        android:textColor="#ffffff"
        android:background="#55000000"
        android:text="倒计时开始"
        android:gravity="center"
        android:textSize="30sp"
        android:layout_height="150dp"/>
</FrameLayout>