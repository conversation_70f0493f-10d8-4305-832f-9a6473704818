package com.lizhi.component.tekiplayer.demo

import com.lizhi.component.tekiplayer.MediaItem
import com.lizhi.component.tekiplayer.process.PlayerService
import com.lizhi.component.tekiplayer.util.TekiLog
import com.yibansan.dns.DnsManager
import java.net.InetAddress

/**
 * 文件名：DemoService
 * 作用：
 * 作者：l<PERSON><PERSON><PERSON><EMAIL>
 * 创建日期：2021/4/6
 */
class DemoService : PlayerService() {

    val TAG = "DemoService"

    override fun onPlaybackStateChange(status: Int) {
        TekiLog.i(TAG, "onPlaybackStateChange $status")
    }

    override fun onError(errCode: Int, message: String) {
        TekiLog.i(TAG, "onError $errCode - $message")
    }

    override fun onPlaybackRemoveOnList() {
        TekiLog.i(TAG, "onPlaybackRemoveOnList")
    }

    override fun onPlayListUpdate() {
        TekiLog.i(TAG, "onPlayListUpdate")
    }

    override fun onPlayListFinished() {
        TekiLog.i(TAG, "onPlayListFinished")
    }

    override fun onPlaybackChange(
        index: Int,
        item: MediaItem?,
        lastPosition: Long,
        reason: Int
    ) {
        TekiLog.i(TAG, "onPlaybackChange index=$index, item=$item")
    }

    override fun onPlayedPositionUpdate(index: Int, item: MediaItem?, position: Long) {
    }

    override fun onPlayZeroItem(item: MediaItem?) {

    }

    override fun onBufferedPositionUpdate(index: Int, item: MediaItem?, bufferPosition: Long) {
    }

    override fun isEnableCustomDns(): Boolean {
        return true
    }

    override fun lookup(hostname: String): List<InetAddress> {
        TekiLog.i(TAG, "looking up $hostname by DnsManager")
        return DnsManager.lookup(hostname)
            ?: emptyList()
    }

    override fun mark(
        domain: String,
        ip: String,
        connResult: Boolean,
        requestResult: Boolean,
        path: String?,
        connCost: Long,
        requestCost: Long
    ) {
        TekiLog.i(
            TAG,
            "mark result $domain $ip connResult $connResult requestCost $requestCost path $path connCost $connCost requestCost $requestCost"
        )
        DnsManager.mark(
            domain,
            ip,
            connResult,
            requestResult,
            path,
            connCost,
            requestCost
        )
    }
}