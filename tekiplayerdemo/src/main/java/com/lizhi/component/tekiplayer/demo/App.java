package com.lizhi.component.tekiplayer.demo;

import android.app.Application;
import android.os.Build;

import com.didichuxing.doraemonkit.DoraemonKit;
import com.lizhi.component.basetool.env.Environments;
import com.yibasan.lizhifm.lzlogan.Logz;
import com.yibasan.lizhifm.rds.RDSAgent;

/**
 * 文件名：App
 * 作用：
 * 作者：<EMAIL>
 * 创建日期：2021/4/15
 */
public class App extends Application {


    @Override
    public void onCreate() {
        super.onCreate();
        DoraemonKit.setDebug(true);
        DoraemonKit.install(this);

        Environments.setFlashDebugMode(true);
        Logz.setDebug(true);
        Logz.init(this, "851126439", Build.DEVICE, "tekiplayer");

        // int lastPosition = 0;
        // int positionInt = 100;

        // ValueAnimator animator = ValueAnimator.ofInt(lastPosition, positionInt);
        // animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
        //     @Override
        //     public void onAnimationUpdate(ValueAnimator animation) {
        //
        //     }
        // });
        // animator.setInterpolator(new LinearInterpolator());
        // animator.setDuration(1000);
        // animator.start();
        // animator.removeAllUpdateListeners();
        // animator.cancel();
        // int now = (int) animator.getAnimatedValue();




//        //根据项目实际需要来配置ITNetConfBuilder，这里为了简单举例创建一个空实现
//        ITNetConfBuilder itnetConfBuilder = new ITNetConfBuilder.Builder().build();
//
//        ITNetConf.init(
//                this,//主工程的上下文
//                itnetConfBuilder,
//                new ITNetModule.Builder().dns().build()
//        );

        RDSAgent.init(this, "851126439", "tekiplayer", Build.DEVICE);
    }
}
