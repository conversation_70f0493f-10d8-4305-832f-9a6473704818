package com.lizhi.component.tekiplayer.demo

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.CheckBox
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.lizhi.component.tekiplayer.MediaItem
import com.lizhi.component.tekiplayer.util.TekiLog

/**
 * 文件名：PlayListAdapter
 * 作用：
 * 作者：huangtianhao
 * 创建日期：2021/3/30
 */
class PlayListAdapter(
    val dataSet: MutableList<MyMediaItem>,
    val onClickAction: (adapter: PlayListAdapter, viewId: Int, position: Int) -> Unit
) : RecyclerView.Adapter<PlayListAdapter.ViewHolder>() {

    val selectedItems:HashMap<Int, MyMediaItem> = HashMap()

    data class MyMediaItem(
        val mediaItem: MediaItem,
        var isSelected: Boolean = false,
        var isChecked: Boolean = false
    )

    inner class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val textView: TextView
        val cb: CheckBox
        val rootView: ConstraintLayout
        val buttonRemove: Button

        init {
            textView = view.findViewById(R.id.textView)
            rootView = view.findViewById(R.id.rootView)
            cb = view.findViewById(R.id.cb)
            buttonRemove = view.findViewById(R.id.btn_remove)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_play_list, parent, false)

        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val data = dataSet[position]
        holder.textView.text = data.mediaItem.uri.toString()

        holder.rootView.setOnClickListener {
            onClickAction(this, it.id, position)
        }
        holder.buttonRemove.setOnClickListener {
            onClickAction(this, it.id, position)
        }
        holder.cb.setOnClickListener {
            onClickAction(this, holder.cb.id, position)
            TekiLog.d("cache_manager","选中位置：：$position")
            val item = dataSet[position]
            item.isChecked = holder.cb.isChecked
            if (holder.cb.isChecked) {
                if (!selectedItems.containsKey(position)) {
                    selectedItems[position] = item
                }
            } else {
                selectedItems.remove(position)
            }
        }

        if (data.isSelected) {
            holder.textView.setTextColor(Color.parseColor("#FF19BCE4"))
        } else {
            holder.textView.setTextColor(Color.parseColor("#99000000"))
        }
        holder.cb.isChecked = data.isChecked
    }

    override fun getItemCount(): Int {
        return dataSet.size
    }
}