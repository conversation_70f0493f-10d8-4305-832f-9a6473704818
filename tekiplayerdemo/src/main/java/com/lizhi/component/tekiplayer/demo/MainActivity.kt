package com.lizhi.component.tekiplayer.demo

import android.animation.ValueAnimator
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.os.SystemClock
import android.util.Base64
import android.util.Log
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.SeekBar
import android.widget.SeekBar.OnSeekBarChangeListener
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.lizhi.component.tekiplayer.MediaItem
import com.lizhi.component.tekiplayer.PlayEventListener
import com.lizhi.component.tekiplayer.Player
import com.lizhi.component.tekiplayer.REPEAT_MODE_ALL
import com.lizhi.component.tekiplayer.REPEAT_MODE_OFF
import com.lizhi.component.tekiplayer.REPEAT_MODE_ONE
import com.lizhi.component.tekiplayer.REPEAT_MODE_SHUFFLE
import com.lizhi.component.tekiplayer.TekiPlayer
import com.lizhi.component.tekiplayer.analyzer.Reporter
import com.lizhi.component.tekiplayer.controller.PlayerState
import com.lizhi.component.tekiplayer.datasource.cache.LruCacheEvictor
import com.lizhi.component.tekiplayer.datasource.entity.DeleteCacheType
import com.lizhi.component.tekiplayer.datasource.entity.MediaInfo
import com.lizhi.component.tekiplayer.datasource.entity.MediaInfoResult
import com.lizhi.component.tekiplayer.demo.PlayListAdapter.MyMediaItem
import com.lizhi.component.tekiplayer.demo.databinding.ActivityMainBinding
import com.lizhi.component.tekiplayer.util.TekiLog
import com.lizhi.component.tekiplayer.util.UNIT
import com.lizhi.component.tekiplayer.util.aes.AESUtil
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileOutputStream
import java.net.URL
import java.util.UUID
import kotlin.concurrent.thread


class MainActivity() : AppCompatActivity(), PlayEventListener {
    var i = 0

    lateinit var player: TekiPlayer
    private val mainHandler = Handler(Looper.getMainLooper())
    private lateinit var binding: ActivityMainBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        MMKV.initialize(applicationContext)

        val url = "http://cdn102.lizhi.fm/audio/2020/10/27/5140979884521277446_hd.mp3"
        TekiLog.i("main", "url = $url")

        val file = File(cacheDir, "5.mp3")

        val file1 = File("${Environment.getExternalStorageDirectory().absolutePath}/183/LizhiFM/Files/upload/")
        val firstOrNull = file1.listFiles()?.firstOrNull {
            it.name.endsWith("aac")
        }
        player = TekiPlayer.Builder(this)
//                .setMaxBufferSizeOnWifi((0.5 * 1024 * 1024).toLong())
                .recordPlaybackPosition(true)
                .setLimitMaxDiskHighPrioritySpaceUsage(15L * 1024 * 1024) // 15M高优缓存
//                .setLimitFreeDiskSpaceUsage(100L * 1024 * 1024 * 1024)
//                .setLimitMaxDiskSpaceUsage(15L * 1024 * 1024)
//                .setAutoPrepareMediaCount(0)
//                .setDnsResolver(object : DnsResolver {
//                    override fun lookup(hostname: String): List<InetAddress> {
//                        TekiLog.i("main", "looking up $hostname by DnsManager")
//                        return DnsManager.lookup(hostname)
//                            ?: emptyList()
//                    }
//
//                    override fun mark(
//                        domain: String,
//                        ip: String,
//                        connResult: Boolean,
//                        requestResult: Boolean,
//                        path: String?,
//                        connCost: Long,
//                        requestCost: Long
//                    ) {
//                        TekiLog.i(
//                            "main",
//                            "mark result $domain $ip connResult $connResult requestCost $requestCost path $path connCost $connCost requestCost $requestCost"
//                        )
////                        DnsManager.mark(
////                            domain,
////                            ip,
////                            connResult,
////                            requestResult,
////                            path,
////                            connCost,
////                            requestCost
////                        )
//                    }
//
//                    override fun isEnableCustomDns(): Boolean {
//                        return true
//                    }
//
//
//                })
                .build()
//                .bindService(DemoService::class.java)
//                .get()

        player.addPlayEventListener(this)
        player.addNetworkQualityWatcher { qualityLevel, speedBps ->
            TekiLog.w("demo", "当前平均下载速度 ${speedBps / 1000}kbps")
            run {
                Toast.makeText(this, "当前平均下载速度 $speedBps bps", Toast.LENGTH_SHORT).show()
            }
        }

        player.setCdnList(
            listOf(
                "http://cdn101.lizhi.fm/",
                "http://cdn.gzlzfm.com",
                "http://cdn102.lizhi.fm",
                "http://cdn101.mlychee.com"
            )
        )

        findViewById<View>(R.id.indexBtn).setOnClickListener {
            println("TekiPlayer, currIndex = ${player.getCurrentIndexOnList()}")
        }

        findViewById<SeekBar>(R.id.seekBar).setOnSeekBarChangeListener(object : OnSeekBarChangeListener {
            override fun onProgressChanged(
                seekBar: SeekBar?,
                progress: Int,
                fromUser: Boolean
            ) {
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {

            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                player.seekTo(seekBar?.progress?.toLong() ?: 0)
            }
        })

        findViewById<View>(R.id.seek).setOnClickListener {
            player.seekBy(15 * 1000)
        }
        binding.seek15.setOnClickListener {
            player.seekBy(-15 * 1000)
        }

        binding.play.setOnClickListener {
            when (player.getStatus()) {
                PlayerState.STATE_IDLE, PlayerState.STATE_ENDED -> {
                    player.prepare()
                    player.play()
                }
                PlayerState.STATE_PLAYING -> {
                    player.pause()
                }
                PlayerState.STATE_READY, PlayerState.STATE_PAUSED -> {
                    player.resume()
                }
            }
        }

        binding.play.setOnLongClickListener {
            player.stop()
            true
        }

        binding.speed.setOnClickListener {
            player.speed = 1.0F
        }

        binding.speedHalf.setOnClickListener {
            player.speed = 0.5F
        }

        binding.speed1Half.setOnClickListener {
            player.speed = 1.5F
        }

        binding.speed2.setOnClickListener {
            player.speed = 2.0F
        }

        binding.repeat.setOnClickListener {
            player.repeatMode = REPEAT_MODE_OFF
        }

        binding.repeatAll.setOnClickListener {
            player.repeatMode = REPEAT_MODE_ALL
        }

        binding.repeatSingle.setOnClickListener {
            player.repeatMode = REPEAT_MODE_ONE
        }

        binding.repeatRandom.setOnClickListener {
            player.repeatMode = REPEAT_MODE_SHUFFLE
        }

        binding.getQuality.setOnClickListener {
            Toast.makeText(this, player.getActualAudioQuality().toString(), Toast.LENGTH_SHORT).show()
        }

        binding.lowQuality.setOnClickListener {
            player.setAudioQuality(Player.Quality.LOW)
        }

        binding.highQuality.setOnClickListener {
            player.setAudioQuality(Player.Quality.HIGH)
        }

        binding.superQuality.setOnClickListener {
            player.setAudioQuality(Player.Quality.SUPER)
        }

        binding.losslessQuality.setOnClickListener {
            player.setAudioQuality(Player.Quality.LOSSLESS)
        }

        binding.timer1Item.setOnClickListener {
            player.stopAfterMediaItemFinish(1)
        }

        binding.timer5Item.setOnClickListener {
            player.stopAfterMediaItemFinish(5)
        }

        binding.pauseOnFocusLost.setOnCheckedChangeListener { buttonView, isChecked ->
            player.pauseWhenAudioFocusLost = isChecked
        }
        TekiPlayer.enableBuiltinPolicy(false)
        binding.cbDelete.setOnCheckedChangeListener{ _, isChecked ->
            TekiPlayer.enableBuiltinPolicy(isChecked)
        }
        binding.deleteCache.setOnClickListener {
            GlobalScope.launch {
                TekiPlayer.deleteCaches(this@MainActivity, DeleteCacheType.ALL)
            }
        }
        binding.deleteNormal.setOnClickListener {
            GlobalScope.launch {
                TekiPlayer.deleteCaches(this@MainActivity, DeleteCacheType.NORMAL)
            }
        }

        binding.deleteHighCache.setOnClickListener {
            GlobalScope.launch {
                TekiPlayer.deleteCaches(this@MainActivity, DeleteCacheType.HIGH_PRIORITY)
            }
        }

        binding.deleteSelected.setOnClickListener {
            GlobalScope.launch {
//                val dataSet: MutableList<MyMediaItem> = (binding.rv.adapter as PlayListAdapter).selectedItems
                val dataSet: HashMap<Int, MyMediaItem> = (binding.rv.adapter as PlayListAdapter).selectedItems
                val datas:MutableList<MediaInfo> = mutableListOf()
                for ((key, item) in dataSet) {
                    TekiLog.d("cache_manager","准备删除 ${item.mediaItem.uri} ")
                    datas.add(MediaInfo(item.mediaItem.uri.toString(), item.mediaItem.extraData?.getBoolean(LruCacheEvictor.CACHE_PRIORITY, false)?:false))
                }
               val results: List<MediaInfoResult> = TekiPlayer.deleteMediaInfos(this@MainActivity, datas)
                for (result in results) {
                    TekiLog.d("cache_manager","删除 url:${result.info.url} 高优:${result.info.isHighPriority}  的结果是: ${result.result.name}")
                }
            }
        }

        binding.queryCache.setOnClickListener {
            GlobalScope.launch {
                val size = TekiPlayer.queryAllCacheSize(this@MainActivity)
                TekiLog.d("cache_manager","查询所有缓存大小：${size.UNIT()}")
                runOnUiThread{
                    Toast.makeText(this@MainActivity, "查询所有缓存大小：${size.UNIT()}", Toast.LENGTH_SHORT).show()
                }
            }
        }

        binding.querySelected.setOnClickListener {
            GlobalScope.launch {
//                val dataSet: MutableList<MyMediaItem> = (binding.rv.adapter as PlayListAdapter).selectedItems
                val dataSet: HashMap<Int, MyMediaItem> = (binding.rv.adapter as PlayListAdapter).selectedItems
                val datas:MutableList<MediaInfo> = mutableListOf()
                for ((key, item) in dataSet) {
                    val isHighPriority = item.mediaItem.extraData?.getBoolean(LruCacheEvictor.CACHE_PRIORITY, false)?:false
                    TekiLog.d("cache_manager","准备查询 URL:${item.mediaItem.uri}  高优:$isHighPriority")
                    datas.add(MediaInfo(item.mediaItem.uri.toString(), isHighPriority))
                }
                val size = TekiPlayer.queryMediaInfosCacheSize(this@MainActivity, datas)
                TekiLog.d("cache_manager","查询指定缓存大小：${size.UNIT()}")
                runOnUiThread {
                    Toast.makeText(this@MainActivity, "查询指定缓存大小：${size.UNIT()}", Toast.LENGTH_SHORT).show()
                }
            }
        }

        initPlayList(binding.rv, file, firstOrNull)
        addLocalFileList(binding.rv)

//        Thread {
//            while (true) {
//
//                TekiLog.d("currPosition", "now:${player?.getPosition()}")
//                SystemClock.sleep(20)
//            }
//        }.start()

    }

    override fun onTimeRemainingUpdate(index: Int, item: MediaItem?, remainingMs: Long) {
        run {
            binding.countdown.visibility = View.VISIBLE
            binding.countdown.text = "倒计时:${remainingMs/1000}"
            if (remainingMs == 0L) {
                binding.countdown.visibility = View.GONE
            }
        }
    }

    override fun onItemRemainingUpdate(index: Int, item: MediaItem?, remainingItem: Int) {
        run {
            Toast.makeText(this, "暂停还有:${remainingItem}个节目", Toast.LENGTH_SHORT).show()
        }
    }

    var lastPosition: Int = 0
    var lastAnimator: ValueAnimator? = null

    override fun onPlayedPositionUpdate(index: Int, item: MediaItem?, position: Long) {
        run {
            TekiLog.d("onPlayedPositionUpdate", "index=$index, item=$item, position=$position")

            binding.seekBar.progress = position.toInt()
            this.binding.position.text = "${player.getPosition() /1000 / 60}:${player.getPosition() / 1000 % 60}.${player.getPosition() % 1000}"
        }
    }

    override fun onPlayZeroItem(item: MediaItem?) {
        Toast.makeText(this, "你在播放空音频哦", Toast.LENGTH_SHORT).show()
    }

    override fun onBufferedPositionUpdate(index: Int, item: MediaItem?, bufferPosition: Long) {
        run {
            binding.seekBar.secondaryProgress = bufferPosition.toInt()
        }
    }

    override fun onAudioQualityChange(to: Player.Quality) {
        Toast.makeText(this, "音质切换成功", Toast.LENGTH_SHORT).show()
    }

    private fun addLocalFileList(rv: RecyclerView) {
        getLocalFile(
            "http://47.115.183.71/No2-441%e9%87%87%e6%a0%b7-%e5%8f%8c-128kbs-%e6%81%92%e5%ae%9a.mp3",
            expectedContentLength = 7264527
        ) {
            mainHandler.post {
                (rv.adapter as? PlayListAdapter)?.run {
                    val mediaItem = createMockMediaItem(it)
                    player.addMediaItem(mediaItem)
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        player.stop()
    }

    private fun getLocalFile(
        url: String,
        expectedContentLength: Long,
        onGetFilePath: (String) -> Unit
    ) {
        val file = File(externalCacheDir!!, url.substringAfterLast("/"))
        if (!file.exists()) {
            file.createNewFile()
        }
        if (file.exists() && file.length() == expectedContentLength) {
            TekiLog.i("main", "getLocalFile expectedContentLength")
            onGetFilePath(file.toURI().toString())
            return
        }
        thread {
            try {
                val inputStream = URL(url).openStream()
                inputStream.copyTo(file.outputStream())
                onGetFilePath(file.toURI().toString())
            } catch (e: Exception) {

            }
        }
    }

    lateinit var list: MutableList<PlayListAdapter.MyMediaItem>

    private fun addData(file: File? = null,
                        file1: File? = null) {
//        list = listOf<MyMediaItem>().toMutableList()
        list = listMediaItem().toMutableList()
        addEncryptMockMediaItems()
//        list.add(MyMediaItem(createHighMockMediaItem("file:///android_asset/123.mp3")))
        file1?.let {
            val mediaItem = MediaItem.Builder().setUri(Uri.parse(it.toURI().toString())).setSpecifiedDuration(19500).build()
            list.add(MyMediaItem(mediaItem))
        }
        player.addMediaItem(list.map { it.mediaItem })
    }

    private fun addEncryptMockMediaItems() {
        // 创建sd卡的加密文件
        val jiamiFile = File(cacheDir, "localfilejiami.opus")
        com.lizhi.component.basetool.io.FileUtils.copy(assets.open("assetsjiami.opus"), FileOutputStream(jiamiFile))
        list.add(MyMediaItem(createEncryptMockMediaItem(Uri.fromFile(jiamiFile).toString())))
        // 导入网络服务器上的加密文件
        list.add(MyMediaItem(createEncryptMockMediaItem("https://cdnus101-buz.183im.com/1/2/06381798dda25e9d7d6655bf18d4bfd8/m/5421eee36fc04d1f8005fd62ef0a5fcf.aac",
        "ZYb6HAFJhcbrsjFEmEttawnG02xfsdwcffbMhTaT+BI=",
        "ba2/G34CcBxN3Su0v4D8/g==")))
        list.add(MyMediaItem(createEncryptMockMediaItem("https://cdnus101-buz.183im.com/2/2/06381798dda25e9d7d6655bf18d4bfd8/m/7cba74d0051a4c7ca28035bea210def8/023518c8-f2be-4a2f-bbfb-db326b05dc52.aac")))
        list.add(MyMediaItem(createEncryptMockMediaItem("https://cdnus101-buz.183im.com/2/2/06381798dda25e9d7d6655bf18d4bfd8/m/af3854aa593d417b850f3709bfbf7b14/17741ff4-8c85-4ca7-aa30-a1153199e61e.opus,76760")))
//        list.add(MyMediaItem(createEncryptMockMediaItem("https://im-chat-us-test.s3.us-east-1.amazonaws.com/2/2/f6aed9f965bd2ecb152ec475a3d35adc/m/42c1c0ca1b9747b899fe6f0234068677/ddeadbd6-c6f5-4e7a-862c-a00d68a6cce3.mp3")))
        // 导入assets文件夹下的加密文件
        list.add(MyMediaItem(createEncryptMockMediaItem("file:///android_asset/assetsjiami.opus")))
        // 导入assets文件夹下的实时加密文件
        list.add(MyMediaItem(createEncryptMockMediaItem("file:///android_asset/rtp_real_time.opus",
            "b2IRv1jZYLGS14ytYluT8R6YXT51CszLBPD13Q0Nr4E=",
            "bPbh8rTTRXJftjjPkGVLNg==",
             true)))
        list.add(MyMediaItem(createEncryptMockMediaItem("file:///android_asset/rtp_real_time2.opus",
            "K8UP+so5ZTA7XKEuy5iFJGEBy0SY2fU2BwNU2Gh/OkA=",
            "pOLv896F3Ys4dNhCz7eoMg==",
             true)))
        list.add(MyMediaItem(createEncryptMockMediaItem("file:///android_asset/rtp_real_time2zhen.opus",
            "v1pY9DZ99ZNyzLWzmLK+beNkln79zGTIvETO/Dblceo=",
            "C3/tdqmcWKqf3k2sCH+zAg==",
            true)))
        list.add(MyMediaItem(createEncryptMockMediaItem("file:///android_asset/rtp_real_time1s.opus",
            "1QYjuRzxF2Ddzt6HM887wba/1wicKy+sCk0EM4UaRlM=",
            "oWoX91BSfZUYepKJlIk6xA==",
            true)))
//        list.add(MyMediaItem(createEncryptMockMediaItem("http://192.168.16.226:8080/jenkins/job/Goldeye/459/execution/node/5/ws/serverText/assetsjiami.opus")))
//        list.add(MyMediaItem(createEncryptMockMediaItem("http://192.168.16.226:8080/jenkins/job/Goldeye/459/execution/node/5/ws/serverText/rtp_real_time2.opus",
//            "K8UP+so5ZTA7XKEuy5iFJGEBy0SY2fU2BwNU2Gh/OkA=",
//            "pOLv896F3Ys4dNhCz7eoMg==",
//            true)))
    }

    private fun createEncryptMockMediaItem(url:String,
                                           key:String = Base64.encodeToString("12345678123456781234567812345678".toByteArray(), Base64.NO_WRAP),
                                           iv:String = Base64.encodeToString("1234567812345678".toByteArray(), Base64.NO_WRAP),
                                           isRealTime:Boolean = false): MediaItem {
        val mediaItem =  createMockMediaItem(url)
        mediaItem.extraData?.let {
            it.putString(AESUtil.AES_KEY,key)
            it.putString(AESUtil.AES_IV,iv)
            if (isRealTime) {
                it.putBoolean(AESUtil.AES_IS_REAL_TIME_DECRYPT, true)
            }
        }
        return mediaItem
    }

    private fun createHighMockMediaItem(url:String): MediaItem {
        val mediaItem =  createMockMediaItem(url)
        mediaItem.extraData?.let {
            it.putBoolean(LruCacheEvictor.CACHE_PRIORITY, true)
        }
        return mediaItem
    }

    private fun initPlayList(
        recyclerView: RecyclerView,
        file: File,
        file1: File?
    ) {

        recyclerView.layoutManager = LinearLayoutManager(this)
        addData(file, file1)
        val adapter = PlayListAdapter(list) { adapter, id, pos ->
            when (id) {
                R.id.rootView -> {
                    player.play(pos)
//                    player.seekTo(pos, Random(1).nextDouble(0.95, 1.0).toFloat())
                    list.forEachIndexed { index, myMediaItem ->
                        myMediaItem.isSelected = index == pos
                    }
                    adapter.notifyDataSetChanged()
                }
                R.id.btn_remove -> {
                    list.removeAt(pos)
                    player.removeItemAt(pos)
                    adapter.notifyDataSetChanged()
                }
            }
        }
        recyclerView.adapter = adapter
        adapter.notifyDataSetChanged()

        binding.timer.setOnClickListener {
            player.stopAfterTime(10 * 1000)
        }

        binding.previous.setOnClickListener {
            player.playPrevious()
//            player.seekTo(0, 0.9999f)
        }

        binding.next.setOnClickListener {
//            player.seekTo(2, 0.97f)
            player.playNext()
        }

        binding.plus.setOnClickListener {
            player.volume = player.volume + 0.1F
        }

        binding.minus.setOnClickListener {
            player.volume = player.volume - 0.1F
        }

        binding.clearCache.setOnClickListener {
            player.clearCache()
            Toast.makeText(this@MainActivity, "Done!", Toast.LENGTH_SHORT).show()
        }

        binding.clearHighCache.setOnClickListener {
            player.clearAllHighPriorityCache()
            Toast.makeText(this@MainActivity, "Done!", Toast.LENGTH_SHORT).show()
        }

        binding.clearList.setOnClickListener {
            if (list.size == 0) {
                addData()
                binding.clearList.text = "清空列表"
            } else {
                player.clearCache()
                player.clear()
                list.clear()
                binding.clearList.text = "填充列表"
            }
            adapter.notifyDataSetChanged()
        }

        binding.url.setOnEditorActionListener { v, actionId, event ->
            if ((actionId == 0 || actionId == EditorInfo.IME_ACTION_DONE)) {
                val mediaItem = MediaItem.Builder().setUri(Uri.parse(binding.url.text.toString())).build()
                try {
                    val indexNum = binding.index.text.toString().toInt()
                    list.add(indexNum, MyMediaItem(mediaItem))
                    player.addMediaItem(indexNum, mediaItem)
                } catch (e: Exception) {
                    player.addMediaItem(mediaItem)
                    list.add(MyMediaItem(mediaItem))
                }
                // rv.adapter?.notifyDataSetChanged()
                true
            }
            false
        }

        binding.testAddItem.setOnClickListener {
            i = 0
            GlobalScope.launch(Dispatchers.IO) {
                while (i < 50) {
                    Log.d("TestDats","i = $i")
                    playVoice("file:///android_asset/input.aac")
                    i++
                    delay(200)
                }
            }
        }

        binding.printAllThread.setOnClickListener {
            printAllThreads()
        }

    }

    private fun playVoice(url: String) {
        // 当作音效播放的话，应该忽略音频焦点
        player.autoHandleAudioFocus = false
        player.pauseWhenAudioFocusLost = false
        player.addMediaItem(
            MediaItem.Builder()
                .setTag("TAG$i")
                .setUri(url.asUri())
                .setExtraData(Bundle().apply {
                    putBoolean(LruCacheEvictor.CACHE_PRIORITY, true)
                })
                .build()
        )
        player.repeatMode = REPEAT_MODE_OFF
        player.prepare()
        player.play()
    }

    fun String.asUri():Uri =
        Uri.parse(this)


    fun printAllThreads() {
        // 获取所有线程的StackTraceElement数组
        val allThreads = Thread.getAllStackTraces()

        // 遍历并打印所有线程的信息
        for ((thread, stackTrace) in allThreads) {
            Log.d("ThreadInfoTest", "Thread: ${thread.name}, State: ${thread.state}")
        }
    }

    private fun listMediaItem(): List<PlayListAdapter.MyMediaItem> {
        return listOf(
            "file:///android_asset/input.aac",
            "file:///android_asset/emptyData.aac",
            "http://cdn.gzlzfm.com/audio/2015/02/17/18042590199901062_hd.mp3",
            "http://cdn.lizhi.fm/audio/2019/06/23/2744633406271451142_hd.mp3",
            "http://cdn.gzlzfm.com/audio/2021/03/06/5165665404497607174_hd.mp3",
////
////            "file:///android_asset/jiemi.aac",
////            "file:///android_asset/jiemi.mp3",
////            "file:///android_asset/123.mp3",
//            "file:///android_asset/10086159_127.opus",
//            "file:///android_asset/41813.aac",
////            "file:///android_asset/test.opus",
//            "https://cdnus101-buz.183im.com/1/2/06381798dda25e9d7d6655bf18d4bfd8/m/ad47a5fe17c34acb9bb2e101b075a9f9.aac",
////            "file:///android_asset/1_75680.opus,76076",
//            "file:///android_asset/1234.opus,76076",
//            "file:///android_asset/1_75680.aac",
//            "file:///android_asset/3_19119_17.opus",
//            "file:///android_asset/1234.opus",
////            "file:///android_asset/onlinechat_user_joins.wav",
////            "file:///android_asset/online_chat_user_join.mp3",
//            "http://cdn102.lizhi.fm/audio/2020/10/27/5140979884521277446_sd.m4a",
//            "https://cdnus101.183im.com/1/2/dc294e3a94b448873a7a6d7aeef0ea63/m/249b4c3243ce4e7f9d142e23b565af9a.aac",
        ).map {
            PlayListAdapter.MyMediaItem(createMockMediaItem(it))
        }
    }

    private fun createMockMediaItem(url: String = "http://cdn.lizhi.fm/audio/2019/06/23/2744633406271451142_hd.mp3"): MediaItem {
        val urlAndDuration = url.split(",")
        val url = urlAndDuration[0]
        val uri = MediaItem.Builder().setUri(Uri.parse(url))
        if (urlAndDuration.size > 1) {
            uri.setSpecifiedDuration(urlAndDuration[1].toLong())
        }
        if (url == "http://cdn.gzlzfm.com/audio/2021/03/06/5165665404497607174_hd.mp3") {
            uri.setUri(
                Player.Quality.LOW,
                Uri.parse("http://cdn.gzlzfm.com/audio/2021/03/06/5165665404497607174_sd.m4a")
            )
        }
        uri.setExtraData(Bundle().apply {
            putString(Reporter.REPORT_KEY_TRACE_ID, UUID.randomUUID().toString())
            if (url.endsWith(".mp3")) { // mp3 都走高优缓存
                putBoolean(LruCacheEvictor.CACHE_PRIORITY, true)
            }
        })
        return uri.build()
    }

    var start = 0L

    init {
        thread {
            while (true) {
                if (startPlaying) {
                    binding.seekBar.post {
                        binding.seekBar.progress = player.getPosition().toInt()
                        binding.position.text = "${player.getPosition() /1000 / 60}:${player.getPosition() / 1000 % 60}.${player.getPosition() % 1000}"
                    }
                    SystemClock.sleep(20)
                } else {
                    SystemClock.sleep(100)
                }
            }
        }
    }

    private var startPlaying = false

    override fun onPlaybackStateChange(status: Int) {
        startPlaying = status == PlayerState.STATE_PLAYING
        if(status == PlayerState.STATE_PLAYING) {
            TekiLog.i("main", "onPlaybackStateChange duration ${player.getDuration()} ms")
            start = System.currentTimeMillis()
        }
        if(status == PlayerState.STATE_ENDED) {
            TekiLog.i("main", "onPlaybackStateChange cost ${System.currentTimeMillis() - start} ms")
        }
        TekiLog.i("main", "onPlaybackStateChange $status")
        mainHandler.post {
            TekiLog.i("main", "ui thread onPlaybackStateChange $status")
            if (status == PlayerState.STATE_READY) {
                binding.seekBar.max = player.getDuration().toInt()
                val l2 = player.getDuration() / 1000
                binding.duration.text = "${l2 / 60}:${l2 % 60}.${String.format("%03d", l2 % 1000)}"
            }
            binding.tvState.text = PlayerState.getStateName(status)

            val text: String = when (status) {
                PlayerState.STATE_PLAYING -> "‖"
                else -> "▶"
            }
            binding.play.text = text
        }
    }


    override fun onError(errCode: Int, message: String) {
        TekiLog.e("main", "onError errCode: $errCode, message: $message")
    }

    override fun onPlayListFinished() {
        TekiLog.i("main", "onPlayListFinished")
    }

    private var currentPlayingIndex: Int = -1
    override fun onPlaybackChange(
        index: Int,
        item: MediaItem?,
        lastPosition: Long,
        reason: Int
    ) {
        mainHandler.post {
            TekiLog.i("main", "onPlaybackChange reason = $reason")
            list.forEachIndexed { i, myMediaItem ->
                myMediaItem.isSelected = i == index
            }
            this.currentPlayingIndex = index
            binding.rv.adapter?.notifyDataSetChanged()
        }
    }

    override fun onPlaybackRemoveOnList() {
    }

    override fun onPlayListUpdate() {
        mainHandler.post {
            (binding.rv.adapter as PlayListAdapter).run {
                dataSet.clear()
                dataSet.addAll(player.getMediaItemList().mapIndexed { index, mediaItem ->
                    MyMediaItem(mediaItem, currentPlayingIndex == index)
                })
                notifyDataSetChanged()
            }
        }
    }

}