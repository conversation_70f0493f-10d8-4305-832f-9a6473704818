package com.yibasan.component

import com.lizhi.component.tekiplayer.util.aes.AesComponent
import org.junit.Assert.assertEquals
import org.junit.Test
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.RandomAccessFile
import javax.crypto.Cipher
import javax.crypto.CipherInputStream

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
class ExampleUnitTest {
    @Test
    fun addition_isCorrect() {
        assertEquals(4, 2 + 2)
    }

    @Test
    fun encryptData() {
        // 输入需要加密的文件
        val inp = FileInputStream("/Users/<USER>/Downloads/src.mp3")
        // 输入加密文件的输出路径
        val out = FileOutputStream("/Users/<USER>/Downloads/jiami.mp3")
        val aesComponent = AesComponent().apply {
            setCipher("********************************", "1234567812345678", Cipher.ENCRYPT_MODE)
        }
//        val ba0 = aesComponent.encryptCipher!!.update(inp.readBytes())
//        println("size:${ba0?.size}")
        try {
            var bytesRead: Int
            val ba = ByteArray(1024)
            var size = 0
            while (inp.read(ba).also { bytesRead = it } != -1) {
                var b3 = ByteArray(bytesRead)
                ba.copyInto(b3,0,0,bytesRead)
                val ba2 = aesComponent.encryptCipher?.update(b3)
                out.write(ba2, 0, ba2?.size?:0)
                size += ba2?.size?:0
                ba.fill(0)
            }
            val ba3 = aesComponent.encryptCipher?.doFinal()
            out.write(ba3, 0, ba3?.size?:0)
            println("size2:$size")
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            inp.close()
            out.close()
        }

        assertEquals(1, 1)
    }

    @Test
    fun decryptData() {
        val inp = FileInputStream("/Users/<USER>/Downloads/ddeadbd6-c6f5-4e7a-862c-a00d68a6cce3.mp3")
        val out = FileOutputStream("/Users/<USER>/Downloads/3.mp3")
//        val ba:ByteArray = inp.readBytes()
//        val ba2 = AESUtil.decryptData("********************************", "1234567812345678", ba)
        val bufferSize = 1024 // 设置缓冲区的大小，可以根据需要进行调整
        val blockSize = 16 // AES加密算法的块大小为16字节
        val ba = ByteArray(bufferSize)
        val aesComponent = AesComponent().apply {
            setCipher("********************************", "1234567812345678")
        }
        try {
            var bytesRead: Int
            while (inp.read(ba).also { bytesRead = it } != -1) {
                var b3 = ByteArray(bytesRead)
                ba.copyInto(b3,0,0,bytesRead)
                val ba2 = aesComponent.decryptCipher?.update(b3)
                println("bytesRead:$bytesRead size:${ba2?.size}")
                out.write(ba2, 0, ba2?.size?:0)
                ba.fill(0)
            }
            val ba3 = aesComponent.decryptCipher?.doFinal()
            out.write(ba3, 0, ba3?.size?:0)
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            inp.close()
            out.close()
        }

        assertEquals(1, 1)
    }

    @Test
    fun decryptInputStreamData() {
        // 输入加密文件路径
//        val inp = FileInputStream("/Users/<USER>/Downloads/2c58b675-fcaa-4666-a7ce-035a9ea1d01b.opus")
//        val inp = FileInputStream("/Users/<USER>/Downloads/2d1ae18f-7428-48d8-8b8c-8da5f7aae051.aac")
//        val inp = FileInputStream("/Users/<USER>/Downloads/ddeadbd6-c6f5-4e7a-862c-a00d68a6cce3.mp3")
//        val inp = FileInputStream("/Users/<USER>/Downloads/249b4c3243ce4e7f9d142e23b565af9a.aac")
        val inp = FileInputStream("/Users/<USER>/Downloads/jiami.mp3")
        // 输出解密文件路径
//        val out = FileOutputStream("/Users/<USER>/Downloads/3.opus")
//        val out = FileOutputStream("/Users/<USER>/Downloads/3.aac")
//        val out = FileOutputStream("/Users/<USER>/Downloads/3.mp3", true)
        val out = FileOutputStream("/Users/<USER>/Downloads/jiemi.mp3", true)
        val aesComponent = AesComponent().apply {
            setCipher("********************************", "1234567812345678")
        }
        try {
//            println("jiami input size:${inp.readBytes().size}")
            val input = CipherInputStream(inp, aesComponent.decryptCipher)
//            println("jiemi input size:${input.readBytes().size}")
            out.write(input.readBytes())
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            inp.close()
            out.close()
        }

        assertEquals(1, 1)
    }

    @Test
    fun decryptInputStreamData2() {
        val inp = FileInputStream("/Users/<USER>/Downloads/ddeadbd6-c6f5-4e7a-862c-a00d68a6cce3.mp3")
        val out = FileOutputStream("/Users/<USER>/Downloads/3.mp3")
        val aesComponent = AesComponent().apply {
            setCipher("********************************", "1234567812345678")
        }
        try {
            val bufferSize = 1024
            val ba = ByteArray(bufferSize)
            var bytesRead: Int
            val input = CipherInputStream(inp, aesComponent.decryptCipher)
            while (input.read(ba).also { bytesRead = it } != -1) {
                out.write(ba, 0, bytesRead)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            inp.close()
            out.close()
        }

        assertEquals(1, 1)
    }

    @Test
    fun decryptInputStreamData3() {
        val file = File("/Users/<USER>/Downloads/ddeadbd6-c6f5-4e7a-862c-a00d68a6cce3.mp3")
        val fileRam = RandomAccessFile(file,"rw")
        val inp = FileInputStream(fileRam.fd)
        val out = FileOutputStream("/Users/<USER>/Downloads/3.mp3")
        val aesComponent = AesComponent().apply {
            setCipher("********************************", "1234567812345678")
        }
        var count = 0
        val ba = ByteArray(1024)
        try {
            var bytesRead: Int
            while (inp.read(ba).also { bytesRead = it } != -1) {
                count+=bytesRead
                var b3 = ByteArray(bytesRead)
                ba.copyInto(b3,0,0,bytesRead)
                val ba2 = aesComponent.decryptCipher?.update(b3)
                println("bytesRead:$bytesRead size:${ba2?.size}")
                out.write(ba2, 0, ba2?.size?:0)
                ba.fill(0)
                if (count > 10240) {
                    break
                }
            }
            val ba3 = aesComponent.decryptCipher?.doFinal()
            out.write(ba3, 0, ba3?.size?:0)
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            inp.close()
            out.close()
        }
        val file1 = File("/Users/<USER>/Downloads/ddeadbd6-c6f5-4e7a-862c-a00d68a6cce3.mp3")
        val fileRam1 = RandomAccessFile(file1,"rw")
        fileRam1.seek(count.toLong())
        val inp1 = FileInputStream(fileRam1.fd)
        val out1 = FileOutputStream("/Users/<USER>/Downloads/3.mp3", true)
        val aesComponent1 = AesComponent().apply {
            setCipher("********************************", "1234567812345678")
        }
        val ba1 = ByteArray(1024)
        try {
            var bytesRead1: Int
            while (inp1.read(ba1).also { bytesRead1 = it } != -1) {
                var b31 = ByteArray(bytesRead1)
                ba1.copyInto(b31,0,0,bytesRead1)
                val ba21 = aesComponent1.decryptCipher?.update(b31)
                println("bytesRead1:$bytesRead1 size:${ba21?.size}")
                out1.write(ba21, 0, ba21?.size?:0)
                ba1.fill(0)
            }
            val ba31 = aesComponent1.decryptCipher?.doFinal()
            out1.write(ba31, 0, ba31?.size?:0)
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            inp1.close()
            out1.close()
        }
        assertEquals(1, 1)
    }

    // 下面的未完成
    @Test
    fun multipleAes() { // 多个加密文件合并后解密
        val inp1 = FileInputStream("/Users/<USER>/Downloads/3.aac")
        val inp2 = FileInputStream("/Users/<USER>/Downloads/249b4c3243ce4e7f9d142e23b565af9a.aac")

        // 多个加密
        val out1 = FileOutputStream("/Users/<USER>/Downloads/multi1.aac")
        val encrypt1 = AesComponent().apply {
            setCipher("********************************", "1234567812345678", Cipher.ENCRYPT_MODE)
        }
        try {
            val input1 = CipherInputStream(inp1, encrypt1.encryptCipher)
            out1.write(input1.readBytes())
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            inp1.close()
            out1.close()
        }
        val out2 = FileOutputStream("/Users/<USER>/Downloads/multi2.aac")
//        val encrypt2 = AesComponent().apply {
//            setCipher("********************************", "1234567812345678", Cipher.ENCRYPT_MODE)
//        }
        try {
            val input2 = CipherInputStream(inp2, encrypt1.encryptCipher)
            out2.write(input2.readBytes())
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            inp2.close()
            out2.close()
        }
        // 合并
        val inpMul1 = FileInputStream("/Users/<USER>/Downloads/multi1.aac")
        val inpMul2 = FileInputStream("/Users/<USER>/Downloads/multi2.aac")
        // 多个加密
        val outMut = FileOutputStream("/Users/<USER>/Downloads/multi.aac")
        try {
            outMut.write(inpMul1.readBytes())
            outMut.write(inpMul2.readBytes())
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            inpMul1.close()
            inpMul2.close()
            outMut.close()
        }
        // 解密
        val inp3 = FileInputStream("/Users/<USER>/Downloads/multi.aac")
        val out3 = FileOutputStream("/Users/<USER>/Downloads/multijie.aac")
        val decrypt = AesComponent().apply {
            setCipher("********************************", "1234567812345678")
        }
        try {
            val input3 = CipherInputStream(inp3, decrypt.decryptCipher)
            out3.write(input3.readBytes())
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            inp3.close()
            out3.close()
        }

        assertEquals(1, 1)
    }

    @Test
    fun multipleAes2() { // 多个加密文件合并后解密
        val inp1 = FileInputStream("/Users/<USER>/Downloads/3.aac")
        val inp2 = FileInputStream("/Users/<USER>/Downloads/249b4c3243ce4e7f9d142e23b565af9a.aac")

        // 多个加密
        val out1 = FileOutputStream("/Users/<USER>/Downloads/multi1.aac")
        val encrypt1 = AesComponent().apply {
            setCipher("********************************", "1234567812345678", Cipher.ENCRYPT_MODE)
        }
        try {
            val input1 = CipherInputStream(inp1, encrypt1.encryptCipher)
            out1.write(input1.readBytes())
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            inp1.close()
            out1.close()
        }
        val out2 = FileOutputStream("/Users/<USER>/Downloads/multi2.aac")
//        val encrypt2 = AesComponent().apply {
//            setCipher("********************************", "1234567812345678", Cipher.ENCRYPT_MODE)
//        }
        try {
            val input2 = CipherInputStream(inp2, encrypt1.encryptCipher)
            out2.write(input2.readBytes())
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            inp2.close()
            out2.close()
        }
        // 合并
        val inpMul1 = FileInputStream("/Users/<USER>/Downloads/multi1.aac")
        val inpMul2 = FileInputStream("/Users/<USER>/Downloads/multi2.aac")
        // 多个加密
        val outMut = FileOutputStream("/Users/<USER>/Downloads/multi.aac")
        try {
            outMut.write(inpMul1.readBytes())
            outMut.write(inpMul2.readBytes())
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            inpMul1.close()
            inpMul2.close()
            outMut.close()
        }
        // 解密
        val inp3 = FileInputStream("/Users/<USER>/Downloads/multi.aac")
        val out3 = FileOutputStream("/Users/<USER>/Downloads/multijie.aac")
        val decrypt = AesComponent().apply {
            setCipher("********************************", "1234567812345678")
        }
        try {
            val input3 = CipherInputStream(inp3, decrypt.decryptCipher)
            out3.write(input3.readBytes())
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            inp3.close()
            out3.close()
        }

        assertEquals(1, 1)
    }
}