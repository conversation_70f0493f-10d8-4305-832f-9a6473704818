apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
//apply plugin: 'kotlin-android-extensions'
//apply from: 'https://gitlab.lizhi.fm/component_android/Gradle_Config/raw/master/maven-push.gradle'

android {
    compileSdkVersion rootProject.ext.android["compileSdkVersion"]

    defaultConfig {
        applicationId "com.lizhifm.component.tekiplayer.demo"
        minSdkVersion rootProject.ext.android["minSdkVersion"]
        targetSdkVersion rootProject.ext.android["targetSdkVersion"]
        versionCode getCompVersion()
        versionName VERSION_NAME
        viewBinding {
            enabled = true
        }

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test:runner:1.1.0-alpha4'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.0-alpha4'

    // third libs
    debugImplementation 'com.didichuxing.doraemonkit:dokitx:3.3.5'
    releaseImplementation 'com.didichuxing.doraemonkit:dokitx-no-op:3.3.5'
    implementation "com.yibasan.lizhifm.lizhi-log:lzlogan-lib:3.4.0"
    implementation 'com.yibasan.lizhifm.rds:lzrds-lib:2.9.7'

    debugImplementation(project(":tekiplayer-okhttp"))
    debugImplementation(project(":tekiplayer"))
    releaseImplementation 'com.lizhi.component.base:tekiplayer-base:' + VERSION_NAME
    releaseImplementation 'com.lizhi.component.base:tekiplayer-okhttp-base:' + VERSION_NAME
//    implementation 'com.lizhi.component.base:tekiplayer-base:*******-SNAPSHOT'
//    implementation 'com.lizhi.component.base:tekiplayer-okhttp-base:*******-SNAPSHOT'

    implementation "com.lizhi.component.base:basetool-base:1.0.8"

    implementation "androidx.recyclerview:recyclerview:1.2.0-alpha06"
    compileOnly "com.lizhi.component.lib:itnet-dns-lib:4.0.11-SNAPSHOT"
}


def getCompVersion() {
    def versionName = VERSION_NAME.split('-')[0]
    println(versionName)
    def versionSegments = versionName.split('\\.').toList()
    if (versionSegments.size() > 3 && !VERSION_NAME.contains("SNAPSHOT")) {
        throw new Exception("版本号不能超过3段")
    }
    while (versionSegments.size() < 3) {
        versionSegments.add('00')
    }
    def versionBuilder = new StringBuilder()
    def seg;
    for (int i = 0; i < versionSegments.size(); i++) {
        seg = versionSegments[i]
        if (seg.length() >= 3) {
        } else if (seg.length() == 1) {
            seg = "0$seg"
        }
        versionBuilder.append(seg)
    }
    def version = Integer.parseInt(versionBuilder.toString())
    println(version)
    return version
}