// Top-level build file where you can add configuration options common to all sub-projects/modules.
apply from: "config.gradle"

buildscript {
    ext.kotlin_version = "1.7.10"
//    ext.kotlin_version = "1.4.20"
    repositories {
        //公司私有服务器
        maven { url 'https://maven.lizhi.fm/nexus/content/repositories/releases/' }
        maven { url 'https://maven.lizhi.fm/nexus/content/repositories/snapshots/' }
        maven { url 'https://maven.lizhi.fm/nexus/content/groups/android_public/' }
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.1.3'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "com.lizhi.component.plugin:nativeProduct:1.0.5"
//        classpath "org.jetbrains.dokka:dokka-gradle-plugin:1.4.20"
//        classpath "com.lizhi.component.pods:litchi-pods-plugin:latest.release"
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    configurations.all {
        resolutionStrategy {
            force 'org.jetbrains.kotlin:kotlin-stdlib:1.4.20'
            force 'androidx.appcompat:appcompat:1.2.0'
        }
    }
    repositories {
        //公司私有服务器
        maven { url 'https://maven.lizhi.fm/nexus/content/repositories/releases/' }
        maven { url 'https://maven.lizhi.fm/nexus/content/repositories/snapshots/' }
        maven { url 'https://maven.lizhi.fm/nexus/content/groups/android_public/' }
        google()
        mavenCentral()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}