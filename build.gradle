// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    dependencies {
        classpath('com.android.tools.build:gradle:3.2.1')
        classpath('org.jetbrains.kotlin:kotlin-gradle-plugin:1.7.10')
        classpath('com.lizhi.component.plugin:nativeProduct:1.0.5')
    }
}
plugins {
    id 'com.android.application' version '7.1.0' apply false
    id 'com.android.library' version '7.1.0' apply false
}
