pluginManagement {
    repositories {
        //公司私有服务器
        maven { url 'https://maven.lizhi.fm/nexus/content/repositories/releases/' }
        maven { url 'https://maven.lizhi.fm/nexus/content/repositories/snapshots/' }
        maven { url 'https://maven.lizhi.fm/nexus/content/groups/android_public/' }
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        //公司私有服务器
        maven { url 'https://maven.lizhi.fm/nexus/content/repositories/releases/' }
        maven { url 'https://maven.lizhi.fm/nexus/content/repositories/snapshots/' }
        maven { url 'https://maven.lizhi.fm/nexus/content/groups/android_public/' }
        google()
        mavenCentral()
    }
}
rootProject.name = "player"
include ':app'
include ':lizhiaudiocore'
