<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="?attr/actionBarSize">

    <Button
        android:id="@+id/startplay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Start Playout"
        tools:layout_editor_absoluteX="151dp"
        tools:layout_editor_absoluteY="421dp" />
</androidx.constraintlayout.widget.ConstraintLayout>