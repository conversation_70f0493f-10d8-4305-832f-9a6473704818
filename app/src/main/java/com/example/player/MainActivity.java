package com.example.player;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.example.player.databinding.ActivityMainBinding;
import com.lizhi.audiocore.AudioBufferProcess;
import com.lizhi.audiocore.FillByteBufferCallback;

import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;

public class MainActivity extends AppCompatActivity implements FillByteBufferCallback {

    private ActivityMainBinding binding;

    private AudioBufferProcess audioBufferProcess;

    private ByteBuffer byteBuffer;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        audioBufferProcess = new AudioBufferProcess();
        audioBufferProcess.init(1, 48000, 10, false);
        audioBufferProcess.setFullByteBufferCallBack(this);
        byteBuffer = ByteBuffer.allocateDirect(2 * 48000 / 1000 * 60);
        audioBufferProcess.cacheDirectBufferAddress(byteBuffer);
        final boolean[] start = {false};

        Button startPlay  = findViewById(R.id.startplay);
        startPlay.setText("Start Decode");

        startPlay.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!start[0]) {
                    startPlay.setText("Stop Decode");
                    start[0] = true;
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            // File file = new File("/data/data/com.example.player/cache/3_19119_17.opus");
                            InputStream inputStream = null;
                            try {
                                inputStream = MainActivity.this.getResources().getAssets().open("2935_55.opus");
                            } catch (IOException e) {
                                throw new RuntimeException(e);
                            }
                            // FileInputStream inputStream;
                            byte[] data = new byte[512];
                            //
                            // try {
                            //     inputStream = new FileInputStream(file);
                            // } catch (FileNotFoundException e) {
                            //     throw new RuntimeException(e);
                            // }

                            // long length = file.length();
                            long size = 0;
                            boolean write = true;
                            while (true) {
                                try {
                                    if (write) {
                                        int read = inputStream.read(data);
                                        if (read < 512) {
                                            audioBufferProcess.sendDecodeBuffer(data, 0, true);
                                            Thread.sleep(1);
                                            runOnUiThread(new Runnable() {
                                                @Override
                                                public void run() {
                                                    startPlay.setText("Start Decode");
                                                    Toast.makeText(MainActivity.this,"解码结束",Toast.LENGTH_SHORT).show();
                                                }
                                            });
                                            break;
                                        }
                                        size += read;
                                    }
                                    write = audioBufferProcess.sendDecodeBuffer(data, 512, false);
                                   // Thread.sleep(1);
                                } catch (InterruptedException e) {
                                    throw new RuntimeException(e);
                                } catch (IOException e) {
                                    throw new RuntimeException(e);
                                }
                            }
                            audioBufferProcess.stopDecoderThread();
                        }
                    }).start();
                } else {
                    startPlay.setText("Start Decode");
                    start[0] = false;
                    audioBufferProcess.stopDecoderThread();
                }
            }
        });
    }

    @Override
    public void fullPcmDataSuccess() {
     //   Log.e("adam", "fullPcmDataSuccess");
    }

    @Override
    public void quicDecodeThreadSuccess() {
        Log.e("adam", "quicDecodeThreadSuccess");
        audioBufferProcess.unit();
    }

    @Override
    public void lastFrameFinished() {
        Log.e("adam", "lastFrameFinished");
    }

    @Override
    public void inPutErrorData() {
        Log.e("adam", "inPutErrorData");
    }
}